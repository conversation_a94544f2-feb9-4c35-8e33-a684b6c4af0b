package br.com.alice.schedule.logics

import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.not
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.models.StaffScheduleStatus
import br.com.alice.data.layer.services.StaffScheduleModelDataService
import io.ktor.util.date.WeekDay
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlin.test.Test

class StaffSchedulePredicatesLogicTest {
    private val staffId = UUID.randomUUID()
    private val startHour = LocalTime.of(9, 0)
    private val untilHour = LocalTime.of(12, 0)
    private val weekDay = WeekDay.MONDAY
    private val staffScheduleId = UUID.randomUUID()
    private val startDate = LocalDate.of(2024, 6, 1)
    private val expirationDate = LocalDate.of(2024, 6, 30)

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#buildPredicates with all fields present builds correct predicate`() {
        val predicate = StaffSchedulePredicatesLogic.buildPredicates(
            staffId, startHour, untilHour, weekDay, staffScheduleId, startDate, expirationDate
        )
        val expected  = Predicate.eq(StaffScheduleModelDataService.FieldOptions().staffId, staffId)
            .and(StaffScheduleModelDataService.FieldOptions().status.eq(StaffScheduleStatus.ACTIVE))
            .and(StaffScheduleModelDataService.FieldOptions().weekDay.eq(weekDay))
            .and(scope(
                scope(Predicate.less(StaffScheduleModelDataService.FieldOptions().startHour, untilHour)
                    .and(Predicate.greater(StaffScheduleModelDataService.FieldOptions().untilHour, startHour)))
                .or(scope(Predicate.eq(StaffScheduleModelDataService.FieldOptions().startHour, startHour)
                    .and(Predicate.eq(StaffScheduleModelDataService.FieldOptions().untilHour, untilHour)))
                )))
            .and(scope(
                scope(Predicate.greaterEq(StaffScheduleModelDataService.FieldOptions().startDate, startDate)
                    .and(Predicate.lessEq(StaffScheduleModelDataService.FieldOptions().startDate, expirationDate))
                )
                .or(StaffScheduleModelDataService.FieldOptions().startDate.isNull())))
            .and(not(Predicate.eq(StaffScheduleModelDataService.FieldOptions().id, staffScheduleId)))

        assertThat(predicate).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `buildPredicates with null startDate and expirationDate`() {
        val predicate = StaffSchedulePredicatesLogic.buildPredicates(
            staffId, startHour, untilHour, weekDay, staffScheduleId, null, null
        )
        val expected  = Predicate.eq(StaffScheduleModelDataService.FieldOptions().staffId, staffId)
            .and(StaffScheduleModelDataService.FieldOptions().status.eq(StaffScheduleStatus.ACTIVE))
            .and(StaffScheduleModelDataService.FieldOptions().weekDay.eq(weekDay))
            .and(scope(
                scope(Predicate.less(StaffScheduleModelDataService.FieldOptions().startHour, untilHour)
                .and(Predicate.greater(StaffScheduleModelDataService.FieldOptions().untilHour, startHour)))
                .or(scope(Predicate.eq(StaffScheduleModelDataService.FieldOptions().startHour, startHour)
                    .and(Predicate.eq(StaffScheduleModelDataService.FieldOptions().untilHour, untilHour)))
                ))
            )
            .and(scope(scope(Predicate.greaterEq(StaffScheduleModelDataService.FieldOptions().startDate, LocalDate.now()))
                .or(StaffScheduleModelDataService.FieldOptions().startDate.isNull())))
            .and(not(Predicate.eq(StaffScheduleModelDataService.FieldOptions().id, staffScheduleId)))

        assertThat(predicate).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `buildPredicates with only staffId and time overlap`() {
        val predicate = StaffSchedulePredicatesLogic.buildPredicates(
            staffId, startHour, untilHour, weekDay, null, null, null
        )
        val expected  = Predicate.eq(StaffScheduleModelDataService.FieldOptions().staffId, staffId)
            .and(StaffScheduleModelDataService.FieldOptions().status.eq(StaffScheduleStatus.ACTIVE))
            .and(StaffScheduleModelDataService.FieldOptions().weekDay.eq(weekDay))
            .and(scope(
                scope((Predicate.less(StaffScheduleModelDataService.FieldOptions().startHour, untilHour)
                .and(Predicate.greater(StaffScheduleModelDataService.FieldOptions().untilHour, startHour))))
                .or(scope(Predicate.eq(StaffScheduleModelDataService.FieldOptions().startHour, startHour)
                    .and(Predicate.eq(StaffScheduleModelDataService.FieldOptions().untilHour, untilHour)))
                ))
            )
            .and(scope(scope(Predicate.greaterEq(StaffScheduleModelDataService.FieldOptions().startDate, LocalDate.now()))
                .or(StaffScheduleModelDataService.FieldOptions().startDate.isNull())))
            .and(Predicate.isNotNull(StaffScheduleModelDataService.FieldOptions().id))

        assertThat(predicate).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `buildGeSlotsPredicate with all parameters`() {
        val startDate = LocalDate.now()
        val endDate = startDate.plusDays(5)
        val predicate = StaffSchedulePredicatesLogic.buildGeSlotsPredicate(
            staffId, startDate, endDate
        )
        val expected  = Predicate.eq(StaffScheduleModelDataService.FieldOptions().staffId, staffId)
            .and(StaffScheduleModelDataService.FieldOptions().status.eq(StaffScheduleStatus.ACTIVE))
            .and(scope(StaffScheduleModelDataService.FieldOptions().startDate.isNull().or(
                Predicate.lessEq(StaffScheduleModelDataService.FieldOptions().startDate, endDate))))
            .and(scope(StaffScheduleModelDataService.FieldOptions().expirationDate.isNull().or(
                Predicate.greaterEq(StaffScheduleModelDataService.FieldOptions().expirationDate, startDate))))

        assertThat(predicate).isEqualTo(expected)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `buildGeSlotsPredicate with only staffId`() {
        val predicate = StaffSchedulePredicatesLogic.buildGeSlotsPredicate(
            staffId, null, null
        )
        val expected  = Predicate.eq(StaffScheduleModelDataService.FieldOptions().staffId, staffId)
            .and(StaffScheduleModelDataService.FieldOptions().status.eq(StaffScheduleStatus.ACTIVE))

        assertThat(predicate).isEqualTo(expected)
    }

}
