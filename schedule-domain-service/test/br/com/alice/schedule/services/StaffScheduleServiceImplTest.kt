package br.com.alice.schedule.services

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.StaffScheduleStatus
import br.com.alice.data.layer.models.StaffScheduleType
import br.com.alice.data.layer.services.StaffScheduleModelDataService
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.model.StaffScheduleEditOption
import br.com.alice.schedule.model.events.StaffScheduleCreatedEvent
import br.com.alice.schedule.model.events.StaffScheduleDeletedEvent
import br.com.alice.schedule.model.events.StaffScheduleEventTypeAssociationsUpdatedEvent
import br.com.alice.schedule.model.events.StaffScheduleUpdatedEvent
import br.com.alice.schedule.model.exceptions.StaffScheduleOverlapException
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.util.date.WeekDay
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalTime
import java.time.LocalDate
import kotlin.test.Test
import kotlin.test.assertEquals
import br.com.alice.common.service.data.dsl.not as dslNot


class StaffScheduleServiceImplTest {

    private val staffScheduleData: StaffScheduleModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val staffScheduleServiceImpl = StaffScheduleServiceImpl(staffScheduleData, kafkaProducerService)

    private val staff = TestModelFactory.buildStaff()

    private val startHour = LocalTime.parse("09:00")
    private val untilHour = LocalTime.parse("09:10")
    private val weekDay = WeekDay.FRIDAY

    private val staffSchedule =
        TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour = startHour,
            untilHour = untilHour,
            weekDay = weekDay
        )
    private val staffScheduleModel = staffSchedule.toModel()
    private val staffSchedules = listOf(staffSchedule)
    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()

    @Test
    fun `#getStaffSchedules should get all staff schedules`() =
        runBlocking {
            coEvery {
                staffScheduleData.find(
                    queryEq {
                        where { staffId.eq(staff.id) and status.eq(StaffScheduleStatus.ACTIVE) }
                            .orderBy { startHour }
                            .sortOrder { asc }
                    }
                )
            } returns staffSchedules.map { it.toModel() }

            val staffSchedulerFound = staffScheduleServiceImpl.getStaffSchedules(staff.id)

            assertThat(staffSchedulerFound).isSuccessWithData(staffSchedules)
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#getStaffSchedules should get all staff schedules with startDate and endDate`() =
        runBlocking {
            val currentStartDate = LocalDate.now()
            val currentEndDate = currentStartDate.plusDays(5)
            coEvery {
                staffScheduleData.find(
                    queryEq {
                        where { staffId.eq(staff.id) and status.eq(StaffScheduleStatus.ACTIVE) and
                                scope(startDate.isNull() or startDate.lessEq(currentEndDate)) and
                        scope(expirationDate.isNull() or expirationDate.greaterEq(currentStartDate))}
                            .orderBy { startHour }
                            .sortOrder { asc }
                    }
                )
            } returns staffSchedules.map { it.toModel() }

            val staffSchedulerFound = staffScheduleServiceImpl.getStaffSchedules(staff.id, currentStartDate, currentEndDate)

            assertThat(staffSchedulerFound).isSuccessWithData(staffSchedules)
        }

    @Test
    fun `#getStaffScheduleById should the staff schedule`() =
        runBlocking {
            coEvery {
                staffScheduleData.findOne(
                    queryEq { where { id.eq(staffSchedule.id) } }
                )
            } returns staffScheduleModel

            val staffSchedulerFound = staffScheduleServiceImpl.getStaffScheduleById(staffSchedule.id)

            assertThat(staffSchedulerFound).isSuccessWithData(staffSchedule)
        }


    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#create won't create staff schedule with coinciding time`() = runBlocking<Unit> {
        coEvery {
            staffScheduleData.findOne(
                queryEq {
                    where {
                        buildPredicate()
                    }.orderBy { this.startDate }
                        .sortOrder { asc }
                    }
            )
        } returns staffScheduleModel

        val staffSchedulerFound = staffScheduleServiceImpl.create(
            staffSchedule.staffId,
            staffSchedule.startHour,
            staffSchedule.untilHour,
            staffSchedule.weekDay,
            staffSchedule.providerUnitId,
            staffSchedule.alsoDigital,
        )

        assertThat(staffSchedulerFound).isFailure()
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#create add a new staff schedule`() = runBlocking {
        coEvery {
            staffScheduleData.findOne(
                queryEq {
                    where {
                        buildPredicate()
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                }
            )
        } returns NotFoundException("not found").failure()

        coEvery {
            staffScheduleData.add(
                match {
                    it.weekDay == staffSchedule.weekDay &&
                            it.staffId == staffSchedule.staffId &&
                            it.startHour == staffSchedule.startHour &&
                            it.untilHour == staffSchedule.untilHour
                }
            )
        } returns staffScheduleModel

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val staffSchedulerFound = staffScheduleServiceImpl.create(
            staffSchedule.staffId,
            staffSchedule.startHour,
            staffSchedule.untilHour,
            staffSchedule.weekDay,
            staffSchedule.providerUnitId,
            staffSchedule.alsoDigital,
        )

        assertThat(staffSchedulerFound).isSuccessWithData(staffSchedule)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#createV2 should not create staff schedule if has colliding time`() = mockLocalDate { date ->

        val startDate = date.plusDays(5)
        val model = staffScheduleModel.copy(startDate = startDate)
        val staffScheduleToAdd = staffSchedule.copy(startDate = date, expirationDate = model.startDate?.minusDays(1))
        coEvery {
            staffScheduleData.findOne(
                queryEq {
                    where {
                        buildPredicate(withId = true)
                    }.orderBy { this.startDate }
                        .sortOrder { asc }
                }
            )
        } returns model

        coEvery {
            staffScheduleData.add(
                match {
                    it.weekDay == staffScheduleToAdd.weekDay &&
                            it.staffId == staffScheduleToAdd.staffId &&
                            it.startHour == staffScheduleToAdd.startHour &&
                            it.untilHour == staffScheduleToAdd.untilHour &&
                            it.startDate == staffScheduleToAdd.startDate &&
                            it.expirationDate == staffScheduleToAdd.expirationDate
                }
            )
        } returns staffScheduleToAdd.toModel()

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val staffSchedulerFound = staffScheduleServiceImpl.createV2(
            staffSchedule.staffId,
            staffSchedule,
            selectedDate = LocalDate.now(),
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )

        assertThat(staffSchedulerFound).isFailureOfType(StaffScheduleOverlapException::class)
        val message = "Já existe um slot cadastrado que coincide com o horário inserido. Início: 09:00, Fim: 09:10, Dia da semana: Sexta-feira, Data de início: ${startDate}, Data de expiração: -"
        assertEquals(message, staffSchedulerFound.failure().message)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#createV2 add a new staff schedule with option THIS_SLOT_AND_NEXT`() = mockLocalDate { date ->
        val staffScheduleToAdd = staffSchedule.copy(startDate = date)
        coEvery {
            staffScheduleData.findOne(
                queryEq {
                    where {
                        buildPredicate(withId = true)
                    }.orderBy { this.startDate }
                        .sortOrder { asc }
                }
            )
        } returns NotFoundException("not found").failure()

        coEvery {
            staffScheduleData.add(
                match {
                    it.weekDay == staffScheduleToAdd.weekDay &&
                            it.staffId == staffScheduleToAdd.staffId &&
                            it.startHour == staffScheduleToAdd.startHour &&
                            it.untilHour == staffScheduleToAdd.untilHour &&
                            it.startDate == staffScheduleToAdd.startDate &&
                            it.expirationDate == staffScheduleToAdd.expirationDate
                }
            )
        } returns staffScheduleModel

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val staffSchedulerFound = staffScheduleServiceImpl.createV2(
            staffSchedule.staffId,
            staffSchedule,
            date,
            StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )

        assertThat(staffSchedulerFound).isSuccessWithData(staffSchedule)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#createV2 add a new staff schedule with option THIS_SLOT_ONLY`() = mockLocalDate { date ->
        val staffScheduleToAdd = staffSchedule.copy(startDate = date, expirationDate = date)
        coEvery {
            staffScheduleData.findOne(
                queryEq {
                    where {
                        buildPredicate(withId = true, startDate = date, expirationDate = date)
                    }.orderBy { this.startDate }
                        .sortOrder { asc }
                }
            )
        } returns NotFoundException("not found").failure()

        coEvery {
            staffScheduleData.add(
                match {
                    it.weekDay == staffScheduleToAdd.weekDay &&
                            it.staffId == staffScheduleToAdd.staffId &&
                            it.startHour == staffScheduleToAdd.startHour &&
                            it.untilHour == staffScheduleToAdd.untilHour &&
                            it.startDate == staffScheduleToAdd.startDate &&
                            it.expirationDate == staffScheduleToAdd.expirationDate
                }
            )
        } returns staffScheduleModel

        coEvery { kafkaProducerService.produce(any()) } returns mockk()

        val staffSchedulerFound = staffScheduleServiceImpl.createV2(
            staffSchedule.staffId,
            staffSchedule,
            date,
            StaffScheduleEditOption.THIS_SLOT_ONLY
        )

        assertThat(staffSchedulerFound).isSuccessWithData(staffSchedule)
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#update updates staff schedule`() =
        runBlocking {
            val staffScheduleUpdated = staffSchedule.copy(
                startHour = staffSchedule.startHour,
                untilHour = staffSchedule.untilHour,
                weekDay = staffSchedule.weekDay
            )

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true)
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.update(staffScheduleUpdated.toModel())
            } returns staffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.update(
                staffScheduleUpdated
            )

            assertThat(staffSchedulerUpdated).isSuccessWithData(staffSchedule)
        }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#updateV2 updates staff schedule THIS_SLOT_AND_NEXT`() = mockLocalDate { selectedDate ->
        mockRangeUuidAndDateTime { uuid, dateTime ->
            val newStaffSchedule = staffSchedule.copy(
                carveOutHours = 24
            )
            val oldStaffScheduleUpdated = staffSchedule.copy(
                expirationDate = selectedDate.minusDays(1)
            )
            val staffScheduleUpdated = newStaffSchedule.copy(
                startDate = selectedDate,
                expirationDate = null,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime
            )

            coEvery { staffScheduleData.get(staffSchedule.id) } returns staffSchedule.toModel().success()

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true)
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.update(oldStaffScheduleUpdated.toModel())
            } returns oldStaffScheduleUpdated.toModel()

            coEvery {
                staffScheduleData.add(staffScheduleUpdated.toModel())
            } returns staffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.updateV2(
                newStaffSchedule,
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
                null
            )

            assertThat(staffSchedulerUpdated).isSuccessWithData(staffScheduleUpdated)
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#updateV2 updates staff schedule THIS_SLOT_ONLY`() = mockLocalDate { selectedDate ->
        mockRangeUuidAndDateTime { uuid, dateTime ->
            val newStaffSchedule = staffSchedule.copy(
                carveOutHours = 24
            )
            val oldStaffScheduleUpdated = staffSchedule.copy(
                expirationDate = selectedDate.minusDays(1)
            )
            val futureStaffScheduleUpdated = staffSchedule.copy(
                startDate = selectedDate.plusDays(1),
                expirationDate = null,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime,
            )
            val staffScheduleUpdated = newStaffSchedule.copy(
                startDate = selectedDate,
                expirationDate = selectedDate,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime
            )

            coEvery { staffScheduleData.get(staffSchedule.id) } returns staffSchedule.toModel().success()

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true, startDate = selectedDate.plusDays(1))
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true, startDate = selectedDate, expirationDate = selectedDate)
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.update(oldStaffScheduleUpdated.toModel())
            } returns oldStaffScheduleUpdated.toModel()

            coEvery {
                staffScheduleData.add(staffScheduleUpdated.toModel())
            } returns staffScheduleUpdated.toModel()

            coEvery {
                staffScheduleData.add(futureStaffScheduleUpdated.toModel())
            } returns futureStaffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.updateV2(
                newStaffSchedule,
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_ONLY,
                null
            )

            assertThat(staffSchedulerUpdated).isSuccessWithData(staffScheduleUpdated)
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#updateV2 not updates staff schedule if there is an error on save`() = mockLocalDate { selectedDate ->
        mockRangeUuidAndDateTime { uuid, dateTime ->
            val newStaffSchedule = staffSchedule.copy(
                carveOutHours = 24
            )
            val oldStaffScheduleUpdated = staffSchedule.copy(
                expirationDate = selectedDate.minusDays(1)
            )
            val futureStaffScheduleUpdated = staffSchedule.copy(
                startDate = selectedDate.plusDays(1),
                expirationDate = null,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime,
            )
            val staffScheduleUpdated = newStaffSchedule.copy(
                startDate = selectedDate,
                expirationDate = selectedDate,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime
            )

            coEvery { staffScheduleData.get(staffSchedule.id) } returns staffSchedule.toModel().success()

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true, startDate = selectedDate.plusDays(1))
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true, startDate = selectedDate, expirationDate = selectedDate)
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.update(oldStaffScheduleUpdated.toModel())
            } returns oldStaffScheduleUpdated.toModel()

            coEvery {
                staffScheduleData.add(staffScheduleUpdated.toModel())
            } returns BadRequestException(message =  "bad_request").failure()

            coEvery {
                staffScheduleData.add(futureStaffScheduleUpdated.toModel())
            } returns futureStaffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.updateV2(
                newStaffSchedule,
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_ONLY,
                null
            )

            assertThat(staffSchedulerUpdated).isFailure()
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#updateV2 updates staff schedule THIS_SLOT_ONLY and do not create future slot if expiration date is in the same day`() = mockLocalDate { selectedDate ->
        mockRangeUuidAndDateTime { uuid, dateTime ->
            val staffScheduleWithExpirationDate = staffSchedule.copy(
                expirationDate = selectedDate
            )
            val newStaffSchedule = staffScheduleWithExpirationDate.copy(
                carveOutHours = 24
            )
            val oldStaffScheduleUpdated = staffScheduleWithExpirationDate.copy(
                expirationDate = selectedDate.minusDays(1)
            )
            val staffScheduleUpdated = newStaffSchedule.copy(
                startDate = selectedDate,
                expirationDate = selectedDate,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime
            )

            coEvery { staffScheduleData.get(staffScheduleWithExpirationDate.id) } returns staffScheduleWithExpirationDate.toModel().success()

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true, startDate = selectedDate.plusDays(1))
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true, startDate = selectedDate, expirationDate = selectedDate)
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.update(oldStaffScheduleUpdated.toModel())
            } returns oldStaffScheduleUpdated.toModel()

            coEvery {
                staffScheduleData.add(staffScheduleUpdated.toModel())
            } returns staffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.updateV2(
                newStaffSchedule,
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_ONLY,
                null
            )

            assertThat(staffSchedulerUpdated).isSuccessWithData(staffScheduleUpdated)
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#updateV2 updates staff schedule THIS_SLOT_ONLY and inactivate old slot cause startDate is the same of selectedDate and not create future slot`() = mockLocalDate { selectedDate ->
        mockRangeUuidAndDateTime { uuid, dateTime ->
            val staffScheduleWithExpirationDate = staffSchedule.copy(
                startDate = selectedDate,
                expirationDate = selectedDate
            )
            val newStaffSchedule = staffScheduleWithExpirationDate.copy(
                carveOutHours = 24
            )
            val oldStaffScheduleUpdated = staffScheduleWithExpirationDate.copy(
                status = StaffScheduleStatus.INACTIVE
            )
            val staffScheduleUpdated = newStaffSchedule.copy(
                startDate = selectedDate,
                expirationDate = selectedDate,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime
            )

            coEvery { staffScheduleData.get(staffScheduleWithExpirationDate.id) } returns staffScheduleWithExpirationDate.toModel().success()

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true, startDate = selectedDate, expirationDate = selectedDate)
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns NotFoundException("not found")

            coEvery {
                staffScheduleData.update(oldStaffScheduleUpdated.toModel())
            } returns oldStaffScheduleUpdated.toModel()

            coEvery {
                staffScheduleData.add(staffScheduleUpdated.toModel())
            } returns staffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.updateV2(
                newStaffSchedule,
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_ONLY,
                null
            )

            assertThat(staffSchedulerUpdated).isSuccessWithData(staffScheduleUpdated)
        }
    }

    @OptIn(OrPredicateUsage::class)
    @Test
    fun `#updateV2 should not update staff schedule if there is colliding time`() = mockLocalDate { selectedDate ->
        mockRangeUuidAndDateTime { uuid, dateTime ->
            val newStaffSchedule = staffSchedule.copy(
                carveOutHours = 24
            )
            val oldStaffScheduleUpdated = staffSchedule.copy(
                expirationDate = selectedDate.minusDays(1)
            )
            val staffScheduleUpdated = newStaffSchedule.copy(
                startDate = selectedDate,
                expirationDate = null,
                id = uuid,
                createdAt = dateTime,
                updatedAt = dateTime
            )

            coEvery { staffScheduleData.get(staffSchedule.id) } returns staffSchedule.toModel().success()

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            buildPredicate(withId = true)
                        }.orderBy { this.startDate }
                            .sortOrder { asc }
                    }
                )
            } returns staffSchedule.toModel().success()

            coEvery {
                staffScheduleData.update(oldStaffScheduleUpdated.toModel())
            } returns oldStaffScheduleUpdated.toModel()

            coEvery {
                staffScheduleData.add(staffScheduleUpdated.toModel())
            } returns staffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(any())
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.updateV2(
                newStaffSchedule,
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
                null
            )

            assertThat(staffSchedulerUpdated).isFailureOfType(StaffScheduleOverlapException::class)
            val message = "Já existe um slot cadastrado que coincide com o horário inserido. Início: 09:00, Fim: 09:10, Dia da semana: Sexta-feira, Data de início: -, Data de expiração: -"
            assertEquals(message, staffSchedulerUpdated.failure().message)
        }
    }

    @Test
    fun `#delete updates staff schedule with inactive status`() =
        runBlocking {
            val staffScheduleUpdated = staffSchedule.copy(
                status = StaffScheduleStatus.INACTIVE,
            )

            coEvery {
                staffScheduleData.findOne(
                    queryEq {
                        where {
                            this.id.eq(staffSchedule.id)
                        }
                    }
                )
            } returns staffScheduleModel

            coEvery {
                staffScheduleData.update(staffScheduleUpdated.toModel())
            } returns staffScheduleUpdated.toModel()

            coEvery {
                kafkaProducerService.produce(match { it.javaClass == StaffScheduleDeletedEvent::class.java })
            } returns mockk()


            val staffSchedulerUpdated = staffScheduleServiceImpl.delete(
                staffSchedule.id
            )

            assertThat(staffSchedulerUpdated).isSuccessWithData(staffScheduleUpdated)
        }

    @Test
    fun `#deleteV2 updates staff schedule with option THIS_SLOT_ONLY`() =
        mockLocalDate { selectedDate ->
            mockRangeUuidAndDateTime { uuid, dateTime ->
                val oldStaffScheduleUpdated = staffSchedule.copy(expirationDate = selectedDate.minusDays(1))
                val futureStaffScheduleUpdated = staffSchedule.copy(
                    startDate = selectedDate.plusDays(1),
                    id = uuid,
                    createdAt = dateTime,
                    updatedAt = dateTime
                )

                coEvery {
                    staffScheduleData.findOne(
                        queryEq {
                            where {
                                this.id.eq(staffSchedule.id)
                            }
                        }
                    )
                } returns staffScheduleModel

                coEvery {
                    staffScheduleData.findOne(
                        queryEq {
                            where {
                                buildPredicate(
                                    withId = true,
                                    startDate = selectedDate.plusDays(1),
                                    expirationDate = null
                                )
                            }.orderBy { this.startDate }
                                .sortOrder { asc }
                        }
                    )
                } returns NotFoundException("not found")

                coEvery {
                    staffScheduleData.update(oldStaffScheduleUpdated.toModel())
                } returns oldStaffScheduleUpdated.toModel()

                coEvery {
                    staffScheduleData.add(futureStaffScheduleUpdated.toModel())
                } returns futureStaffScheduleUpdated.toModel()

                coEvery {
                    kafkaProducerService.produce(match { it.javaClass == StaffScheduleUpdatedEvent::class.java })
                } returns mockk()

                coEvery {
                    kafkaProducerService.produce(match { it.javaClass == StaffScheduleCreatedEvent::class.java })
                } returns mockk()

                val staffSchedulerUpdated = staffScheduleServiceImpl.deleteV2(
                    staffSchedule.id,
                    requesterId = null,
                    selectedDate = selectedDate,
                    selectedOption = StaffScheduleEditOption.THIS_SLOT_ONLY
                )

                assertThat(staffSchedulerUpdated).isSuccessWithData(oldStaffScheduleUpdated)
            }
        }

    @Test
    fun `#deleteV2 updates staff schedule with option THIS_SLOT_ONLY and not create future slot cause expiration date`() =
        mockLocalDate { selectedDate ->
            mockRangeUuidAndDateTime { uuid, dateTime ->
                val staffScheduleWithExpirationDate = staffSchedule.copy(expirationDate = selectedDate)
                val oldStaffScheduleUpdated = staffScheduleWithExpirationDate.copy(expirationDate = selectedDate.minusDays(1))

                coEvery {
                    staffScheduleData.findOne(
                        queryEq {
                            where {
                                this.id.eq(staffSchedule.id)
                            }
                        }
                    )
                } returns staffScheduleWithExpirationDate.toModel()

                coEvery {
                    staffScheduleData.update(oldStaffScheduleUpdated.toModel())
                } returns oldStaffScheduleUpdated.toModel()

                coEvery {
                    kafkaProducerService.produce(match { it.javaClass == StaffScheduleUpdatedEvent::class.java })
                } returns mockk()

                coEvery {
                    kafkaProducerService.produce(match { it.javaClass == StaffScheduleCreatedEvent::class.java })
                } returns mockk()

                val staffSchedulerUpdated = staffScheduleServiceImpl.deleteV2(
                    staffScheduleWithExpirationDate.id,
                    requesterId = null,
                    selectedDate = selectedDate,
                    selectedOption = StaffScheduleEditOption.THIS_SLOT_ONLY
                )

                assertThat(staffSchedulerUpdated).isSuccessWithData(oldStaffScheduleUpdated)
            }
        }

    @Test
    fun `#deleteV2 updates staff schedule with option THIS_SLOT_ONLY and inactivate old slot cause start date is the same of selected date`() =
        mockLocalDate { selectedDate ->
            mockRangeUuidAndDateTime { uuid, dateTime ->
                val staffScheduleWithExpirationDate = staffSchedule.copy(expirationDate = selectedDate, startDate = selectedDate)
                val oldStaffScheduleUpdated = staffScheduleWithExpirationDate.copy(status = StaffScheduleStatus.INACTIVE)

                coEvery {
                    staffScheduleData.findOne(
                        queryEq {
                            where {
                                this.id.eq(staffSchedule.id)
                            }
                        }
                    )
                } returns staffScheduleWithExpirationDate.toModel()

                coEvery {
                    staffScheduleData.update(oldStaffScheduleUpdated.toModel())
                } returns oldStaffScheduleUpdated.toModel()

                coEvery {
                    kafkaProducerService.produce(match { it.javaClass == StaffScheduleDeletedEvent::class.java })
                } returns mockk()

                val staffSchedulerUpdated = staffScheduleServiceImpl.deleteV2(
                    staffScheduleWithExpirationDate.id,
                    requesterId = null,
                    selectedDate = selectedDate,
                    selectedOption = StaffScheduleEditOption.THIS_SLOT_ONLY
                )

                assertThat(staffSchedulerUpdated).isSuccessWithData(oldStaffScheduleUpdated)
            }
        }

    @Test
    fun `#deleteV2 updates staff schedule with option THIS_SLOT_AND_NEXT`() =
        mockLocalDate { selectedDate ->
            mockRangeUuidAndDateTime { uuid, dateTime ->
                val oldStaffScheduleUpdated = staffSchedule.copy(expirationDate = selectedDate.minusDays(1))

                coEvery {
                    staffScheduleData.findOne(
                        queryEq {
                            where {
                                this.id.eq(staffSchedule.id)
                            }
                        }
                    )
                } returns staffSchedule.toModel()

                coEvery {
                    staffScheduleData.update(oldStaffScheduleUpdated.toModel())
                } returns oldStaffScheduleUpdated.toModel()

                coEvery {
                    kafkaProducerService.produce(match { it.javaClass == StaffScheduleUpdatedEvent::class.java })
                } returns mockk()

                val staffSchedulerUpdated = staffScheduleServiceImpl.deleteV2(
                    staffSchedule.id,
                    requesterId = null,
                    selectedDate = selectedDate,
                    selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
                )

                assertThat(staffSchedulerUpdated).isSuccessWithData(oldStaffScheduleUpdated)
            }
        }

    @Test
    fun `#addList should add a list of staff schedules`(): Unit = runBlocking {
        val staffSchedule1 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour,
            untilHour
        )
        val staffSchedule2 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour.plusMinutes(10),
            untilHour.plusMinutes(10)
        )
        val staffSchedules = listOf(staffSchedule1, staffSchedule2)
        val staffScheduleModels = staffSchedules.map { it.toModel() }

        coEvery {
            staffScheduleData.find(queryEq {
                where {
                    this.staffId.eq(staff.id).and(this.status.eq(StaffScheduleStatus.ACTIVE))
                }
            })
        } returns emptyList()

        coEvery {
            staffScheduleData.addList(staffScheduleModels)
        } returns staffScheduleModels

        coEvery {
            kafkaProducerService.produce(any<StaffScheduleCreatedEvent>())
        } returns mockk()

        val result = staffScheduleServiceImpl.addList(staffSchedules)

        assertThat(result).isSuccessWithData(staffSchedules)

        coVerify(exactly = 2) { kafkaProducerService.produce(any<StaffScheduleCreatedEvent>()) }
    }

    @Test
    fun `#addList should not add staff schedules if they overlap`(): Unit = runBlocking {
        val staffSchedule1 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour,
            untilHour
        )
        val staffSchedule2 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour,
            untilHour
        )
        val staffSchedules = listOf(staffSchedule1, staffSchedule2)

        coEvery {
            staffScheduleData.find(queryEq {
                where {
                    this.staffId.eq(staff.id).and(this.status.eq(StaffScheduleStatus.ACTIVE))
                }
            })
        } returns emptyList()

        val result = staffScheduleServiceImpl.addList(staffSchedules)

        assertThat(result).isFailureOfType(StaffScheduleOverlapException::class)

        coVerify(exactly = 0) { kafkaProducerService.produce(any<StaffScheduleCreatedEvent>()) }
    }

    @Test
    fun `#updateList should update a list of staff schedules`(): Unit = runBlocking {
        val staffSchedule1 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour,
            untilHour
        )
        val staffSchedule2 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour.plusMinutes(10),
            untilHour.plusMinutes(10)
        )
        val staffSchedules = listOf(staffSchedule1, staffSchedule2)
        val staffScheduleModels = staffSchedules.map { it.toModel() }

        coEvery {
            staffScheduleData.find(queryEq {
                where {
                    this.staffId.eq(staff.id).and(this.status.eq(StaffScheduleStatus.ACTIVE))
                }
            })
        } returns emptyList()

        coEvery {
            staffScheduleData.updateList(staffScheduleModels)
        } returns staffScheduleModels

        coEvery {
            staffScheduleData.find(queryEq {
                where {
                    this.id.inList(staffSchedules.map { it.id })
                }
            })
        } returns staffScheduleModels

        coEvery {
            kafkaProducerService.produce(any<StaffScheduleUpdatedEvent>())
        } returns mockk()

        val result = staffScheduleServiceImpl.updateList(staffSchedules)

        coVerify(exactly = 2) { kafkaProducerService.produce(any<StaffScheduleUpdatedEvent>()) }

        assertThat(result).isSuccessWithData(staffSchedules)
    }

    @Test
    fun `#updateList should not update staff schedules if they overlap`(): Unit = runBlocking {
        val staffSchedule1 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour,
            untilHour
        )
        val staffSchedule2 = TestModelFactory.buildStaffSchedule(
            staffId = staff.id,
            startHour,
            untilHour
        )
        val staffSchedules = listOf(staffSchedule1, staffSchedule2)

        coEvery {
            staffScheduleData.find(queryEq {
                where {
                    this.staffId.eq(staff.id).and(this.status.eq(StaffScheduleStatus.ACTIVE))
                }
            })
        } returns emptyList()

        val result = staffScheduleServiceImpl.updateList(staffSchedules)

        coVerify(exactly = 0) { kafkaProducerService.produce(any<StaffScheduleUpdatedEvent>()) }

        assertThat(result).isFailureOfType(StaffScheduleOverlapException::class)
    }

    @Test
    fun `#updateList should accept empty list`(): Unit = runBlocking {
        val result = staffScheduleServiceImpl.updateList(emptyList())

        assertThat(result).isSuccessWithData(emptyList())
    }

    @Test
    fun `#updateStaffScheduleEventTypeAssociation should update staff schedule with new event type exception`() =
        runBlocking {
            val updatedStaffSchedule = staffSchedule.copy(
                exceptionEventTypes = listOf(appointmentScheduleEventType.id)
            )

            coEvery {
                staffScheduleData.findOne(queryEq {
                    where { id.eq(staffSchedule.id) }
                })
            } returns staffScheduleModel
            coEvery {
                staffScheduleData.update(
                    staffScheduleModel.copy(exceptionEventTypes = listOf(appointmentScheduleEventType.id))
                )
            } returns updatedStaffSchedule.toModel()
            coEvery {
                kafkaProducerService.produce(
                    match {
                        it::class == StaffScheduleEventTypeAssociationsUpdatedEvent::class
                    },
                    staffSchedule.id.toString(),
                )
            } returns mockk()

            val result = staffScheduleServiceImpl.updateStaffScheduleEventTypeAssociation(
                staffScheduleId = staffSchedule.id,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                isDisassociation = true,
            )

            assertThat(result).isSuccessWithData(updatedStaffSchedule)
        }

    @Test
    fun `#getActiveOnSiteByStaff should get all staff schedules`() =
        runBlocking {
            coEvery {
                staffScheduleData.find(
                    queryEq {
                        where {
                            staffId.eq(staff.id) and
                                    status.eq(StaffScheduleStatus.ACTIVE) and
                                    type.eq(StaffScheduleType.HAD) and
                                    providerUnitId.isNotNull()
                        }
                    }
                )
            } returns staffSchedules.map { it.toModel() }

            val staffSchedulerFound = staffScheduleServiceImpl.getActiveOnSiteByStaff(staff.id)

            assertThat(staffSchedulerFound).isSuccessWithData(staffSchedules)

            coVerifyOnce { staffScheduleData.find(any()) }
        }

    @OptIn(OrPredicateUsage::class)
    private fun buildPredicate(
        startHour: LocalTime = staffSchedule.startHour,
        untilHour: LocalTime = staffSchedule.untilHour,
        weekDay: WeekDay = staffSchedule.weekDay,
        startDate: LocalDate = LocalDate.now(),
        expirationDate: LocalDate? = null,
        withId: Boolean = false
    ) =
        Predicate.eq(StaffScheduleModelDataService.FieldOptions().staffId, staff.id)
            .and(StaffScheduleModelDataService.FieldOptions().status.eq(StaffScheduleStatus.ACTIVE))
            .and(StaffScheduleModelDataService.FieldOptions().weekDay.eq(weekDay))
            .and(
                scope(
                    scope((Predicate.less(StaffScheduleModelDataService.FieldOptions().startHour, untilHour)
                        .and(Predicate.greater(StaffScheduleModelDataService.FieldOptions().untilHour, startHour))))
                        .or(scope(Predicate.eq(StaffScheduleModelDataService.FieldOptions().startHour, startHour)
                            .and(Predicate.eq(StaffScheduleModelDataService.FieldOptions().untilHour, untilHour)))
                        ))
            )
            .and(scope(
                let {
                    val startPredicate = Predicate.greaterEq(StaffScheduleModelDataService.FieldOptions().startDate, startDate)
                    if (expirationDate != null) scope(startPredicate.and(Predicate.lessEq(
                        StaffScheduleModelDataService.FieldOptions().startDate,
                        expirationDate))
                    ) else scope(startPredicate)
                }.or(StaffScheduleModelDataService.FieldOptions().startDate.isNull()))
            )
            .and(
                if (withId)
                dslNot(
                    Predicate.eq(
                        StaffScheduleModelDataService.FieldOptions().id,
                        staffSchedule.id
                    )
                )
                else
                    Predicate.isNotNull(StaffScheduleModelDataService.FieldOptions().id)
            )
}
