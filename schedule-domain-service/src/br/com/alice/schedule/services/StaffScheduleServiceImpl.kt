package br.com.alice.schedule.services

import br.com.alice.common.core.extensions.fromUTCToSaoPauloTimeZone
import br.com.alice.common.core.extensions.nextDay
import br.com.alice.common.core.extensions.timesOverlap
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.data.layer.models.StaffScheduleModel
import br.com.alice.data.layer.models.StaffScheduleStatus
import br.com.alice.data.layer.models.StaffScheduleType
import br.com.alice.data.layer.services.StaffScheduleModelDataService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.converters.toTransport
import br.com.alice.schedule.model.StaffScheduleEditOption
import br.com.alice.schedule.logics.StaffSchedulePredicatesLogic
import br.com.alice.schedule.model.events.StaffScheduleCreatedEvent
import br.com.alice.schedule.model.events.StaffScheduleDeletedEvent
import br.com.alice.schedule.model.events.StaffScheduleEventTypeAssociationsUpdatedEvent
import br.com.alice.schedule.model.events.StaffScheduleUpdatedEvent
import br.com.alice.schedule.model.exceptions.StaffScheduleInvalidException
import br.com.alice.schedule.model.exceptions.StaffScheduleOverlapException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.isFailure
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.util.date.WeekDay
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

class StaffScheduleServiceImpl(
    private val staffScheduleModelDataService: StaffScheduleModelDataService,
    private val kafkaProducerService: KafkaProducerService,
) : StaffScheduleService {

    override suspend fun getStaffSchedules(staffId: UUID, startDate: LocalDate?, endDate: LocalDate?): Result<List<StaffSchedule>, Throwable> {
        val schedulesData = staffScheduleModelDataService.find {
            where { StaffSchedulePredicatesLogic.buildGeSlotsPredicate(staffId, startDate, endDate) }
                .orderBy { this.startHour }
                .sortOrder { asc }
        }

        return schedulesData.map { availabilities -> availabilities.sortedBy { it.weekDay } }
            .mapEach { it.toTransport() }
    }

    override suspend fun getStaffScheduleById(id: UUID) =
        staffScheduleModelDataService.findOne {
            where { this.id.eq(id) }
        }.map { it.toTransport() }

    override suspend fun create(
        staffId: UUID,
        startHour: LocalTime,
        untilHour: LocalTime,
        weekDay: WeekDay,
        providerUnitId: UUID?,
        alsoDigital: Boolean,
        type: StaffScheduleType,
        carveOutHours: Int?,
    ): Result<StaffSchedule, Throwable> =
        this.validateStaffScheduleUpsertWithCollision(
            staffId = staffId,
            startHour = startHour,
            untilHour = untilHour,
            weekDay = weekDay
        )
            .flatMap {
                staffScheduleModelDataService.add(
                    StaffScheduleModel(
                        staffId = staffId,
                        weekDay = weekDay,
                        startHour = startHour,
                        untilHour = untilHour,
                        providerUnitId = providerUnitId,
                        alsoDigital = alsoDigital,
                        type = type,
                        carveOutHours = carveOutHours,
                    )
                ).map { it.toTransport() }.then { kafkaProducerService.produce(StaffScheduleCreatedEvent(it)) }
            }
            .flatMapError {
                handleStaffScheduleUpsertErrors(
                    exception = it,
                    method = "create",
                )
            }

    override suspend fun createV2(
        staffId: UUID,
        staffSchedule: StaffSchedule,
        selectedDate: LocalDate,
        selectedOption: StaffScheduleEditOption
    ): Result<StaffSchedule, Throwable> =
        this.validateStaffScheduleUpsert(
            staffId = staffId,
            startHour = staffSchedule.startHour,
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay
        )
            .flatMap {
                when (selectedOption) {
                    StaffScheduleEditOption.THIS_SLOT_ONLY -> createSlotWithDates(
                        staffSchedule,
                        startDate = selectedDate,
                        expirationDate = selectedDate
                    )

                    else -> createSlotWithDates(staffSchedule, startDate = selectedDate, expirationDate = null)
                }
            }
            .flatMapError {
                handleStaffScheduleUpsertErrors(
                    exception = it,
                    method = "create",
                )
            }

    override suspend fun update(
        model: StaffSchedule
    ): Result<StaffSchedule, Throwable> =
        this.validateStaffScheduleUpsertWithCollision(
            staffId = model.staffId,
            startHour = model.startHour,
            untilHour = model.untilHour,
            weekDay = model.weekDay,
            staffScheduleId = model.id,
        )
            .flatMap {
                staffScheduleModelDataService.update(model.toModel())
                    .map { it.toTransport() }
                    .then { kafkaProducerService.produce(StaffScheduleUpdatedEvent(it)) }
            }
            .flatMapError {
                handleStaffScheduleUpsertErrors(
                    exception = it,
                    method = "update",
                )
            }

    override suspend fun updateV2(
        staffSchedule: StaffSchedule,
        selectedDate: LocalDate,
        selectedOption: StaffScheduleEditOption,
        requesterId: UUID?
    ): Result<StaffSchedule, Throwable> =
        this.validateStaffScheduleUpsert(
            staffId = staffSchedule.staffId,
            startHour = staffSchedule.startHour,
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay,
            staffScheduleId = staffSchedule.id,
        )
            .flatMap {
                get(staffSchedule.id).flatMap { oldStaffSchedule ->
                    when (selectedOption) {
                        StaffScheduleEditOption.THIS_SLOT_ONLY -> {
                            //create slot that will work only for one day
                            createSlotWithDates(
                                staffSchedule = staffSchedule,
                                startDate = selectedDate,
                                expirationDate = selectedDate
                            ).then {
                                createFutureSlotWithStartDate(oldStaffSchedule, selectedDate)
                            }
                        }

                        else -> createSlotWithDates(
                            staffSchedule = staffSchedule,
                            startDate = selectedDate,
                            expirationDate = staffSchedule.expirationDate
                        )
                    }.then {
                        updateOldSlotDates(oldStaffSchedule, selectedDate = selectedDate, requesterId = requesterId)
                    }
                }.map { it }
            }
            .flatMapError {
                handleStaffScheduleUpsertErrors(
                    exception = it,
                    method = "update",
                )
            }

    override suspend fun delete(id: UUID, requesterId: UUID?): Result<StaffSchedule, Throwable> =
        this.getStaffScheduleById(id).flatMap {
            deleteSlot(it, requesterId)
        }

    override suspend fun deleteV2(id: UUID, requesterId: UUID?, selectedDate: LocalDate, selectedOption: StaffScheduleEditOption): Result<StaffSchedule, Throwable> =
        this.getStaffScheduleById(id).flatMap { staffSchedule ->
            updateOldSlotDates(staffSchedule, selectedDate = selectedDate, requesterId = requesterId).then {
                if (selectedOption == StaffScheduleEditOption.THIS_SLOT_ONLY) {
                    createFutureSlotWithStartDate(staffSchedule, selectedDate)
                }
            }
        }

    override suspend fun addList(models: List<StaffSchedule>) =
        if (models.isNotEmpty()) performBulkOperation(
            models.map { it.toModel() }, BulkOperation.CREATE
        ).mapEach { it.toTransport() }
        else emptyList<StaffSchedule>().success()

    override suspend fun updateList(models: List<StaffSchedule>) =
        if (models.isNotEmpty()) performBulkOperation(
            models.map { it.toModel() }, BulkOperation.UPDATE
        ).mapEach { it.toTransport() }
        else emptyList<StaffSchedule>().success()

    override suspend fun updateStaffScheduleEventTypeAssociation(
        staffScheduleId: UUID,
        appointmentScheduleEventTypeId: UUID,
        isDisassociation: Boolean,
    ): Result<StaffSchedule, Throwable> {
        return this.getStaffScheduleById(staffScheduleId).flatMap {
            val exceptionEventTypes = if (isDisassociation) {
                it.exceptionEventTypes.plus(appointmentScheduleEventTypeId).toSet().toList()
            } else {
                it.exceptionEventTypes.minus(appointmentScheduleEventTypeId)
            }

            staffScheduleModelDataService.update(
                it.copy(
                    exceptionEventTypes = exceptionEventTypes
                ).toModel()
            )
                .map { it.toTransport() }
                .then { staffSchedule ->
                    kafkaProducerService.produce(
                        StaffScheduleEventTypeAssociationsUpdatedEvent(staffSchedule = staffSchedule),
                        staffSchedule.id.toString(),
                    )
                }
        }
    }

    suspend fun get(id: UUID): Result<StaffSchedule, Throwable> =
        staffScheduleModelDataService.get(id).map { it.toTransport() }

    suspend fun getWithUTCWeekDay(id: UUID): Result<StaffSchedule, Throwable> =
        staffScheduleModelDataService.getWithUTCWeekDay(id).map { it.toTransport() }

    suspend fun getActiveByStaffs(staffIds: List<UUID>): Result<List<StaffSchedule>, Throwable> =
        staffScheduleModelDataService.find {
            where { this.staffId.inList(staffIds) and this.status.eq(StaffScheduleStatus.ACTIVE) }
                .orderBy { this.startHour }
                .sortOrder { asc }
        }.mapEach { it.toTransport() }

    suspend fun getByIds(ids: List<UUID>): Result<List<StaffSchedule>, Throwable> =
        staffScheduleModelDataService.find {
            where { this.id.inList(ids) }
        }.mapEach { it.toTransport() }

    suspend fun getActiveOnSiteByStaff(staffId: UUID): Result<List<StaffSchedule>, Throwable> =
        staffScheduleModelDataService.find {
            where {
                this.staffId.eq(staffId) and
                        this.status.eq(StaffScheduleStatus.ACTIVE) and
                        this.type.eq(StaffScheduleType.HAD) and
                        this.providerUnitId.isNotNull()
            }
        }.mapEach { it.toTransport() }

    @OptIn(OrPredicateUsage::class)
    private suspend fun getCoincidingTime(
        staffId: UUID,
        startHour: LocalTime,
        untilHour: LocalTime,
        weekDay: WeekDay,
        staffScheduleId: UUID?,
        startDate: LocalDate?,
        expirationDate: LocalDate?,
    ): Result<Boolean, Throwable> {
        val startHourInBrtTimezone = startHour.fromUTCToSaoPauloTimeZone()
        val utcWeekDay =
            if (startHour.isBefore(startHourInBrtTimezone)) weekDay.nextDay()
            else weekDay

        return staffScheduleModelDataService.findOne {
            where {
                StaffSchedulePredicatesLogic.buildPredicates(
                    staffId,
                    startHour,
                    untilHour,
                    utcWeekDay,
                    staffScheduleId,
                    startDate,
                    expirationDate
                )
            }.orderBy { this.startDate }
                .sortOrder { asc }
        }.map { it.toTransport() }.flatMap {
            StaffScheduleOverlapException(
                startHour = it.startHour.toString(),
                untilHour = it.untilHour.toString(),
                weekday = it.weekDay,
                startDate = it.startDate,
                expirationDate = it.expirationDate,
            ).failure()
        }.coFoldNotFound { true.success() }
    }

    private suspend fun performBulkOperation(
        models: List<StaffScheduleModel>,
        operation: BulkOperation,
    ): Result<List<StaffScheduleModel>, Throwable> {
        return staffScheduleModelDataService.find {
            where {
                this.staffId.eq(models.first().staffId).and(this.status.eq(StaffScheduleStatus.ACTIVE))
            }
        }.flatMap { existingStaffSchedules ->
            val validation = models.map { staffSchedule ->
                validateStaffScheduleBulkUpsert(
                    existingStaffSchedules = existingStaffSchedules + models.filterNot { it.id == staffSchedule.id },
                    startHour = staffSchedule.startHour,
                    untilHour = staffSchedule.untilHour,
                    weekDay = staffSchedule.weekDay,
                )
            }

            val validationFailure = validation.find { it.isFailure() }

            return@flatMap validationFailure?.let { failure ->
                failure.flatMapError {
                    handleStaffScheduleUpsertErrors(
                        exception = it,
                        method = "update",
                    )
                }.flatMap {
                    Result.of {
                        emptyList()
                    }
                }
            } ?: run {
                models
                    .chunked(50).pmap { chunkedStaffSchedules ->
                        when (operation) {
                            BulkOperation.CREATE -> {
                                val result = staffScheduleModelDataService.addList(chunkedStaffSchedules).get()
                                result.map {
                                    kafkaProducerService.produce(StaffScheduleCreatedEvent(it.toTransport()))
                                }
                                result
                            }

                            BulkOperation.UPDATE -> {
                                val staffSchedulesToUpdateById =
                                    getByIds(chunkedStaffSchedules.map { it.id }).map { staffSchedules ->
                                        staffSchedules.associateBy { it.id }
                                    }.get()
                                val result = staffScheduleModelDataService.updateList(chunkedStaffSchedules.map {
                                    it.copy(version = staffSchedulesToUpdateById[it.id]?.version ?: it.version)
                                }).get()
                                result.map {
                                    kafkaProducerService.produce(StaffScheduleUpdatedEvent(it.toTransport()))
                                }
                                result
                            }
                        }
                    }
                    .flatten()
                    .success()
            }
        }
    }

    private suspend fun validateStaffScheduleUpsertWithCollision(
        staffId: UUID,
        startHour: LocalTime,
        untilHour: LocalTime,
        weekDay: WeekDay,
        staffScheduleId: UUID? = null,
        startDate: LocalDate? = null,
        expirationDate: LocalDate? = null,
    ) = span("validateStaffScheduleUpsertWithCollision") { span ->
        span.setAttribute("staff_id", staffId.toString())
        span.setAttribute("start_hour", startHour.toString())
        span.setAttribute("until_hour", untilHour.toString())
        span.setAttribute("week_day", weekDay.name)
        span.setAttribute("staff_schedule_id", staffScheduleId.toString())

        if (isInvalidPeriod(startHour, untilHour)) {
            logger.error("StaffScheduleServiceImpl::validateStaffScheduleUpsertWithCollision the end time of a staff schedule must be after the start time.")
            StaffScheduleInvalidException(startHour.toString(), untilHour.toString(), weekDay.name).failure()
        } else {
            this.getCoincidingTime(
                staffId = staffId,
                startHour = startHour,
                untilHour = untilHour,
                weekDay = weekDay,
                staffScheduleId = staffScheduleId,
                startDate = startDate,
                expirationDate = expirationDate
            )
        }
    }

    private suspend fun validateStaffScheduleUpsert(
        staffId: UUID,
        startHour: LocalTime,
        untilHour: LocalTime,
        weekDay: WeekDay,
        staffScheduleId: UUID? = null
    ) = span("validateStaffScheduleUpsert") { span ->
        span.setAttribute("staff_id", staffId.toString())
        span.setAttribute("start_hour", startHour.toString())
        span.setAttribute("until_hour", untilHour.toString())
        span.setAttribute("week_day", weekDay.name)
        span.setAttribute("staff_schedule_id", staffScheduleId.toString())

        if (isInvalidPeriod(startHour, untilHour)) {
            logger.error("StaffScheduleServiceImpl::validateStaffScheduleUpsert the end time of a staff schedule must be after the start time.")
            StaffScheduleInvalidException(startHour.toString(), untilHour.toString(), weekDay.name).failure()
        } else {
            true.success()
        }
    }

    private fun validateStaffScheduleBulkUpsert(
        existingStaffSchedules: List<StaffScheduleModel>,
        startHour: LocalTime,
        untilHour: LocalTime,
        weekDay: WeekDay,
    ): Result<Boolean, Throwable> =
        if (isInvalidPeriod(startHour, untilHour)) {
            logger.error(
                "StaffScheduleServiceImpl::validateStaffScheduleBulkUpsert the end time of a staff schedule must be after the start time.",
                "start_hour" to startHour,
                "until_hour" to untilHour,
                "week_day" to weekDay
            )
            StaffScheduleInvalidException(startHour.toString(), untilHour.toString(), weekDay.toString()).failure()
        } else {
            existingStaffSchedules.find {
                timesOverlap(Pair(it.startHour, it.untilHour), Pair(startHour, untilHour)) && it.weekDay == weekDay
            }?.let {
                StaffScheduleOverlapException(
                    it.startHour.toString(),
                    it.untilHour.toString(),
                    it.weekDay,
                    it.startDate,
                    it.expirationDate
                ).failure()
            } ?: run {
                true.success()
            }
        }

    private fun isInvalidPeriod(startHour: LocalTime, untilHour: LocalTime): Boolean {
        val startHourInBrtTimezone = startHour.fromUTCToSaoPauloTimeZone()
        val untilHourInBrtTimezone = untilHour.fromUTCToSaoPauloTimeZone()

        return startHourInBrtTimezone.isAfter(untilHourInBrtTimezone) || startHourInBrtTimezone == untilHourInBrtTimezone
    }

    private fun handleStaffScheduleUpsertErrors(
        exception: Throwable,
        method: String
    ) = when (exception::class.java) {
        StaffScheduleOverlapException::class.java -> {
            logger.info(
                "StaffScheduleServiceImpl::$method there is already a staff schedule with coinciding time",
                "exception" to exception
            )
            exception.failure()
        }

        StaffScheduleInvalidException::class.java -> {
            logger.info(
                "StaffScheduleServiceImpl::$method until hour is before start hour",
                "exception" to exception
            )
            exception.failure()
        }

        else -> exception.failure()
    }

    private suspend fun createSlotWithDates(
        staffSchedule: StaffSchedule,
        startDate: LocalDate?,
        expirationDate: LocalDate?
    ): Result<StaffSchedule, Throwable> =
        this.getCoincidingTime(
            staffSchedule.staffId,
            staffSchedule.startHour,
            staffSchedule.untilHour,
            staffSchedule.weekDay,
            staffSchedule.id,
            startDate,
            expirationDate
        ).flatMap {
            staffScheduleModelDataService.add(
                StaffScheduleModel(
                    staffId = staffSchedule.staffId,
                    weekDay = staffSchedule.weekDay,
                    startHour = staffSchedule.startHour,
                    untilHour = staffSchedule.untilHour,
                    providerUnitId = staffSchedule.providerUnitId,
                    alsoDigital = staffSchedule.alsoDigital,
                    type = staffSchedule.type,
                    carveOutHours = staffSchedule.carveOutHours,
                    startDate = startDate,
                    expirationDate = expirationDate,
                )
            ).map { it.toTransport() }.then { kafkaProducerService.produce(StaffScheduleCreatedEvent(it)) }
        }

    private suspend fun updateOldSlotDates(
        staffSchedule: StaffSchedule,
        selectedDate: LocalDate,
        requesterId: UUID?
    ): Result<StaffSchedule, Throwable> {
        val startDate = staffSchedule.startDate
        if (startDate == null || startDate < selectedDate) {
            //update old slot adding expiration date
            return staffScheduleModelDataService.update(
                staffSchedule.copy(expirationDate = selectedDate.minusDays(1)).toModel()
            )
                .map { it.toTransport() }
                .then { kafkaProducerService.produce(StaffScheduleUpdatedEvent(it)) }
        }
        //update old slot inactivating status
        return deleteSlot(staffSchedule, requesterId)
    }

    private suspend fun deleteSlot(staffSchedule: StaffSchedule, requesterId: UUID?) =
        staffScheduleModelDataService.update(
            staffSchedule.copy(
                status = StaffScheduleStatus.INACTIVE,
                lastUpdatedBy = requesterId,
            ).toModel()
        ).map { it.toTransport() }.then {
            kafkaProducerService.produce(StaffScheduleDeletedEvent(it))
        }

    private suspend fun createFutureSlotWithStartDate(staffSchedule: StaffSchedule, selectedDate: LocalDate): Result<StaffSchedule, Throwable>? {
        val expirationDate = staffSchedule.expirationDate
        if (expirationDate == null || expirationDate > selectedDate) {
            //if slot continue, create future slot with startDate
            return createSlotWithDates(
                staffSchedule = staffSchedule,
                startDate = selectedDate.plusDays(1),
                expirationDate = staffSchedule.expirationDate
            )
        }
        return null
    }

    enum class BulkOperation {
        UPDATE, CREATE
    }

}

