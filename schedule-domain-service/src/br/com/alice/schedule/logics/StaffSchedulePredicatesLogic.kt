package br.com.alice.schedule.logics

import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.not
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.data.layer.models.StaffScheduleStatus
import br.com.alice.data.layer.services.StaffScheduleModelDataService
import io.ktor.util.date.WeekDay
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

object StaffSchedulePredicatesLogic {

    fun buildPredicates(
        staffId: UUID,
        startHour: LocalTime,
        untilHour: LocalTime,
        weekDay: WeekDay,
        staffScheduleId: UUID?,
        startDate: LocalDate?,
        expirationDate: LocalDate?,
    ): Predicate =
        buildStaffPredicate(staffId)
            .withStatus()
            .withWeekDay(weekDay)
            .compareHours(startHour, untilHour)
            .withStartDate(startDate, expirationDate)
            .withId(staffScheduleId)

    fun buildGeSlotsPredicate(staffId: UUID, startDate: LocalDate?, endDate: LocalDate?): Predicate {
        var predicate = buildStaffPredicate(staffId).withStatus()

        if (endDate != null) {
            predicate = predicate.getSlotsWithStartDate(endDate)
        }

        if (startDate != null) {
            predicate = predicate.getSlotsWithEndDate(startDate)
        }

        return predicate
    }

    private fun buildStaffPredicate(staffId: UUID) =
        Predicate.eq(StaffScheduleModelDataService.FieldOptions().staffId, staffId)

    private fun Predicate.withStatus(status: StaffScheduleStatus = StaffScheduleStatus.ACTIVE) =
        this and Predicate.eq(StaffScheduleModelDataService.FieldOptions().status, status)

    private fun Predicate.withWeekDay(weekDay: WeekDay) =
        this and Predicate.eq(StaffScheduleModelDataService.FieldOptions().weekDay, weekDay)

    @OptIn(OrPredicateUsage::class)
    private fun Predicate.compareHours(startHour: LocalTime, untilHour: LocalTime) =
        (StaffScheduleModelDataService.FieldOptions().startHour to StaffScheduleModelDataService.FieldOptions().untilHour)
            .let {
                val startHourField = it.first
                val untilHourField = it.second
                scope(
                    scope(
                        Predicate.less(that = startHourField, value = untilHour)
                            .and( Predicate.greater(that = untilHourField, value = startHour))
                    ).or(
                        scope(
                            Predicate.eq(
                                that = startHourField,
                                value = startHour
                            ).and(
                                Predicate.eq(
                                    that = untilHourField,
                                    value = untilHour
                                )
                            )
                        )

                    )
                )
            }
            .let { this and it }

    @OptIn(OrPredicateUsage::class)
    private fun Predicate.withStartDate(startDate: LocalDate?, expirationDate: LocalDate?) =
        StaffScheduleModelDataService.FieldOptions().startDate
            .let { modelStartDateField ->
                val startDatePredicate = Predicate.greaterEq(that = modelStartDateField, value = startDate ?: LocalDate.now())

                val finalPredicate = if (expirationDate != null) {
                    startDatePredicate.and(Predicate.lessEq(that = modelStartDateField, value = expirationDate))
                } else {
                    startDatePredicate
                }

                this and scope(
                    scope(finalPredicate)
                        .or(second = modelStartDateField.isNull())
                )
            }


    private fun Predicate.withId(staffScheduleId: UUID?) =
        StaffScheduleModelDataService.FieldOptions().id
            .let { modelIdField ->
                if (staffScheduleId != null) not(Predicate.eq(modelIdField, staffScheduleId))
                else Predicate.isNotNull(modelIdField)
            }
            .let { this and it }

    @OptIn(OrPredicateUsage::class)
    private fun Predicate.getSlotsWithStartDate(endDate: LocalDate) =
        StaffScheduleModelDataService.FieldOptions().startDate
            .let { modelStartDateField ->
                this and scope(modelStartDateField.isNull().or(modelStartDateField.lessEq(endDate)))
            }

    @OptIn(OrPredicateUsage::class)
    private fun Predicate.getSlotsWithEndDate(startDate: LocalDate) =
        StaffScheduleModelDataService.FieldOptions().expirationDate
            .let { modelExpirationDateField ->
                this and scope(modelExpirationDateField.isNull().or(modelExpirationDateField.greaterEq(startDate)))
            }
}
