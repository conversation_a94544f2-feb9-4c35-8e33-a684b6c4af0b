package br.com.alice.membership.model.onboarding

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.HealthDeclarationAnswer
import br.com.alice.data.layer.models.HealthDeclarationQuestionType
import br.com.alice.data.layer.models.Imc
import br.com.alice.data.layer.models.Person

data class HealthDeclarationAnswerOption(
    val label: String,
    val value: String = label,
    val tooltip: String? = null,
    val exclusive: Boolean = false,
    val legalResponsibleLabel: String? = null,
    val subQuestion: HealthDeclarationQuestion? = null
)

data class GroupedAnswerOption(
    val title: String,
    val options: List<HealthDeclarationAnswerOption>
)

open class HealthDeclarationQuestion(
    val type: HealthDeclarationQuestionType,
    val question: String,
    val answerOptions: List<HealthDeclarationAnswerOption> = emptyList(),
    val allowOther: Boolean = true,
    val otherPlaceholder: String? = null,
    val progress: Int = 0
) {

    fun answer(answer: String, healthConditions: List<HealthCondition>? = null, denyAnswer: String? = null) =
        HealthDeclarationAnswer(
            questionType = type,
            question = question,
            answer = answer,
            healthConditions = healthConditions,
            denyAnswers = denyAnswer,
        )
}

open class HealthDeclarationGroupedQuestion(
    type: HealthDeclarationQuestionType,
    question: String,
    answerOptions: List<HealthDeclarationAnswerOption> = emptyList(),
    allowOther: Boolean = true,
    progress: Int,
    val groupedAnswerOptions: List<GroupedAnswerOption>
) : HealthDeclarationQuestion(
    type = type,
    question = question,
    answerOptions = answerOptions,
    allowOther = allowOther,
    progress = progress
)

data class ConditionAnswer(
    val questionType: HealthDeclarationQuestionType,
    val selectedAnswers: List<String>
)

data class HealthDeclarationRequest(
    val person: Person,
    val weight: Double,
    val height: Double,
    val conditions: List<String>,
    val survey: Map<String, Any>,
    val openAnswer: String? = null
) {
    fun imc() = Imc(height, weight).calculate()
}

class QuestionConfigurationException(
    message: String,
    code: String = "question_configuration",
    cause: Throwable? = null
) : BadRequestException(code = code, message = message, cause = cause)

class QuestionValidationException(
    message: String = "health declaration hasn't started",
    code: String = "health_declaration_not_started",
    cause: Throwable? = null
) : BadRequestException(code = code, message = message, cause = cause)

class HealthDeclarationNotStartedException(
    message: String = "health declaration hasn't started",
    code: String = "health_declaration_not_started",
    cause: Throwable? = null
) : NotFoundException(code = code, message = message, cause = cause)
