package br.com.alice.membership.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonLogin
import com.github.kittinunf.result.Result
import java.math.BigInteger
import java.util.UUID

@RemoteService
interface PersonLoginService: Service {

    companion object {
        val TWENTY_FOUR_HOURS = (60 * 12L).toBigInteger()
    }

    override val namespace get() = "membership"
    override val serviceName get() = "person_login"

    suspend fun get(id: UUID): Result<PersonLogin, Throwable>

    suspend fun findByPersonId(personId: PersonId): Result<List<PersonLogin>, Throwable>

    suspend fun update(personLogin: PersonLogin): Result<PersonLogin, Throwable>

    suspend fun signInByNationalId(nationalId: String, accessCode: String): Result<PersonLogin, Throwable>

    suspend fun getLoginInformation(nationalId: String): Result<Person, Throwable>

    suspend fun generateNewAccessCode(person: Person, expirationInMinutes: BigInteger = TWENTY_FOUR_HOURS, firstAccess: Boolean = false): Result<PersonLogin, Throwable>

    suspend fun archiveLogins(personId: PersonId): Result<PersonLogin, Throwable>
}

open class LoginNotFoundException(
    message: String = "Não achamos esse CPF na nossa base. Procure seu RH ou seu corretor.",
    code: String = "invalid_login_credentials",
    cause: Throwable? = null
) : AuthorizationException(message, code, cause)

open class LoginExpiredException(
    message: String,
    code: String = "login_expired",
    cause: Throwable? = null
) : AuthorizationException(message, code, cause)

open class InvalidCredentialsException(
    message: String = "Não achamos esse CPF na nossa base. Procure seu RH ou seu corretor.",
    code: String = "invalid_login_credentials",
    cause: Throwable? = null
) : AuthorizationException(message, code, cause)

open class LoginNotFoundForGASException(
    message: String,
    code: String = "invalid_credentials",
    cause: Throwable? = null
) : AuthorizationException(message, code, cause)

open class InvalidCredentialsForGASException(
    message: String,
    code: String = "invalid_credentials",
    cause: Throwable? = null
) : AuthorizationException(message, code, cause)

class LoginMaxAttemptsReachedException(
    message: String,
    code: String = "login_max_attempts_reached",
    cause: Throwable? = null
) : AuthorizationException(message, code, cause) {
    constructor(limit: Int): this(
        message = "You've reached the maximum login attempts. Max: $limit"
    )
}

