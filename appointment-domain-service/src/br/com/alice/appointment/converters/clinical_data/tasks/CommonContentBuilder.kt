package br.com.alice.appointment.converters.clinical_data.tasks

import br.com.alice.appointment.model.TaskHtmlContent
import br.com.alice.appointment.model.TaskHtmlGroup
import br.com.alice.appointment.storages.ResourceLoader
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskType

abstract class CommonContentBuilder : TaskContent {
    private val taskInfo = ResourceLoader.load("clinical-record/info_task.html")
        .readText()

    private val tasks = ResourceLoader.load("clinical-record/task.html")
        .readText()


    fun <T : HealthPlanTask> getTasksOrNull(tasks: List<HealthPlanTask>, type: HealthPlanTaskType): List<T>? =
        tasks.filter { it.type == type }
            .map { it.specialize<T>() }
            .ifEmpty { null }

    fun toHtml(task: TaskHtmlGroup): String =
        tasks.replace("{{TITLE}}", task.title)
            .replace("{{TASKS}}", buildTasks(task.tasks))
            .replace("<strong></strong>", "")
            .replace("<span></span>", "")


    private fun buildTasks(tasks: List<TaskHtmlContent>) = tasks.joinToString("") { task ->
        taskInfo
            .replace("{{TASK_TITLE}}", task.title.orEmpty())
            .replace("{{TASK_CONTENT}}", task.content)
            .replace("{{TASK_SUB_CONTENT}}", task.subContent.orEmpty())

    }

    protected fun HealthPlanTask.getCaseRecordDescription() = this.caseRecordDetails.firstOrNull()?.description?.description
}
