package br.com.alice.action.plan.consumers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import com.github.kittinunf.result.onFailure
import com.github.kittinunf.result.onSuccess

class RemoveAppointmentScheduleWhenCancelledConsumer(
    private val actionPlanTaskService: ActionPlanTaskService
) : Consumer() {


    suspend fun handle(event: AppointmentScheduleCancelledEvent) = withSubscribersEnvironment {
        val scheduleId = event.payload.id
        actionPlanTaskService.removeAppointmentScheduleFromTasks(scheduleId, ActionPlanTaskStatus.ACTIVE)
            .onFailure {
                logger.error(
                    "$LOG_PREFIX Error when remove schedule from tasks",
                    "exception", it,
                    "appointment_schedule_id", scheduleId
                )
            }
            .onSuccess { logger.info("$LOG_PREFIX Schedule removed from tasks", "appointment_schedule_id", scheduleId) }
    }

    private companion object {
        const val LOG_PREFIX = "[RemoveAppointmentScheduleWhenCancelledConsumer::handle]"
    }
}
