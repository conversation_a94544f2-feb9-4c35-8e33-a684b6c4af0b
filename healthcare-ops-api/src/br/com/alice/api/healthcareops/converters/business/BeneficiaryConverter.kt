package br.com.alice.api.healthcareops.converters.business

import br.com.alice.api.healthcareops.models.BeneficiaryAndPersonRequest
import br.com.alice.api.healthcareops.models.BeneficiaryAndPersonRequestBatch
import br.com.alice.api.healthcareops.models.BeneficiaryCompiledViewResponse
import br.com.alice.api.healthcareops.models.BeneficiaryDetailResponse
import br.com.alice.api.healthcareops.models.BeneficiaryInfoResponse
import br.com.alice.api.healthcareops.models.BeneficiaryMemberInfo
import br.com.alice.api.healthcareops.models.BeneficiaryOnboardingDetailResponse
import br.com.alice.api.healthcareops.models.BeneficiaryOnboardingPhaseDetailResponse
import br.com.alice.api.healthcareops.models.BeneficiaryOnboardingPhaseResponse
import br.com.alice.api.healthcareops.models.BeneficiaryOnboardingResponse
import br.com.alice.api.healthcareops.models.BeneficiaryRequest
import br.com.alice.api.healthcareops.models.BeneficiarySimpleAndPersonBatchRequest
import br.com.alice.api.healthcareops.models.BeneficiaryWithPerson
import br.com.alice.api.healthcareops.models.CassiMemberDetailResponse
import br.com.alice.api.healthcareops.models.PersonDetailResponse
import br.com.alice.business.client.BeneficiaryCompiledViewFilters
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.exceptions.InvalidBeneficiaryOnboardingFlowState
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.common.core.extensions.cleanSymbols
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.orNull
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryCompiledView
import br.com.alice.data.layer.models.BeneficiaryHubspot
import br.com.alice.data.layer.models.BeneficiaryOnboarding
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhase
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.common.BeneficiaryType
import br.com.alice.data.layer.models.BeneficiaryViewInsuranceStatus
import br.com.alice.data.layer.models.CassiMember
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.toActiveOnboardingStatus
import br.com.alice.data.layer.models.toOnboardingStatus
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

object BeneficiaryConverter {
    fun Beneficiary.toBeneficiaryDetailResponse(
        person: Person,
        member: Member,
        beneficiaryHubspot: BeneficiaryHubspot? = null,
        cassiMember: CassiMember? = null,
        company: Company,
        product: Product,
        parentBeneficiaryPerson: Person? = null,
    ) = BeneficiaryDetailResponse(
        id = this.id,
        parentBeneficiary = this.parentBeneficiary,
        personId = this.personId.toUUID(),
        companyId = this.companyId,
        type = this.type,
        contractType = this.contractType,
        parentBeneficiaryRelationType = this.parentBeneficiaryRelationType,
        parentBeneficiaryRelatedAt = this.parentBeneficiaryRelatedAt,
        activatedAt = this.activatedAt,
        hiredAt = this.hiredAt,
        canceledAt = this.canceledAt,
        canceledReason = this.canceledReason,
        canceledDescription = this.canceledDescription,
        hasContributed = this.hasContributed,
        cnpj = this.cnpj,
        subcontractId = this.companySubContractId,
        memberStatus = this.memberStatus,
        hubspotDealId = beneficiaryHubspot?.externalDealId,
        cassiMember = cassiMember?.toCassiMemberDetailResponse(),
        personInfo = person.toPersonDetailResponse(),
        onboarding = this.onboarding?.toBeneficiaryOnboardingDetailResponse(),
        productId = member.productId,
        companyName = company.name,
        productTitle = product.title,
        parentBeneficiaryPerson = parentBeneficiaryPerson?.toPersonDetailResponse(),
    )

    fun Beneficiary.toBeneficiaryInfoResponse(
        person: Person,
        member: Member?,
        cassiMember: CassiMember?,
        hubspotDealId: String?
    ) =
        BeneficiaryInfoResponse(
            id = this.id,
            parentBeneficiary = this.parentBeneficiary,
            personId = person.id.id,
            companyId = this.companyId,
            type = this.type,
            contractType = this.contractType,
            parentBeneficiaryRelationType = this.parentBeneficiaryRelationType,
            parentBeneficiaryRelatedAt = this.parentBeneficiaryRelatedAt?.toLocalDate()?.toString(),
            nationalId = person.nationalId,
            firstName = person.getSocialOrFirstName,
            lastName = person.lastName,
            email = person.email,
            sex = person.sex,
            dateOfBirth = person.dateOfBirth?.toLocalDate()?.toString(),
            phoneNumber = person.phoneNumber,
            mothersName = person.mothersName,
            tags = person.tags?.joinToString(","),
            activatedAt = this.activatedAt.toLocalDate().toString(),
            hiredAt = this.hiredAt?.toLocalDate()?.toString(),
            addressPostalCode = person.addresses.firstOrNull()?.postalCode,
            addressStreet = person.addresses.firstOrNull()?.street,
            addressNumber = person.addresses.firstOrNull()?.number?.toIntOrNull(),
            addressComplement = person.addresses.firstOrNull()?.complement,
            addressNeighborhood = person.addresses.firstOrNull()?.neighbourhood,
            addressCity = person.addresses.firstOrNull()?.city,
            addressState = person.addresses.firstOrNull()?.state?.toString(),
            initialProductId = this.onboarding?.initialProductId,
            flowType = this.onboarding?.flowType,
            currentPhase = this.onboarding?.currentPhase,
            member = member?.toBeneficiaryMemberInfo(),
            currentFlowType = this.onboarding?.flowType,
            cassiMemberId = cassiMember?.memberId,
            cassiStartDate = cassiMember?.startDate?.toLocalDate()?.toString(),
            cassiExpirationDate = cassiMember?.expirationDate?.toLocalDate()?.toString(),
            cassiAccountNumber = cassiMember?.accountNumber,
            canceledAt = this.canceledAt?.toLocalDate()?.toString(),
            canceledReason = this.canceledReason,
            canceledDescription = this.canceledDescription,
            hasContributed = this.hasContributed,
            cnpj = this.cnpj,
            hubspotDealId = hubspotDealId,
            subcontractId = companySubContractId,
        )

    private fun Member.toBeneficiaryMemberInfo() = BeneficiaryMemberInfo(
        status = this.status,
    )

    fun CassiMember.toCassiMemberDetailResponse() = CassiMemberDetailResponse(
        cassiAccountNumber = this.accountNumber,
        cassiStartDate = this.startDate,
        cassiExpirationDate = this.expirationDate,
        cassiMemberId = this.id
    )

    fun Person.toPersonDetailResponse() = PersonDetailResponse(
        email = this.email,
        nationalId = this.nationalId,
        firstName = this.firstName,
        lastName = this.lastName,
        socialName = this.socialName,
        address = this.mainAddress,
        sex = this.sex,
        dateOfBirth = this.dateOfBirth.toString(),
        phoneNumber = this.phoneNumber,
        mothersName = this.mothersName,
        tags = this.tags.toString(),
    )

    fun BeneficiaryOnboarding.toBeneficiaryOnboardingDetailResponse() = BeneficiaryOnboardingDetailResponse(
        currentPhase = this.currentPhase?.toBeneficiaryOnboardingPhaseDetailResponse(),
        initialProductId = this.initialProductId,
        flowType = this.flowType
    )

    private fun BeneficiaryOnboardingPhase.toBeneficiaryOnboardingPhaseDetailResponse() =
        BeneficiaryOnboardingPhaseDetailResponse(
            phaseType = this.phase,
            transactedAt = this.transactedAt
        )

    fun BeneficiaryAndPersonRequest.validateFlowType() =
        when (this.flowType) {
            BeneficiaryOnboardingFlowType.UNDEFINED -> InvalidBeneficiaryOnboardingFlowState(flowType).failure()
            else -> this.success()
        }

    fun BeneficiaryAndPersonRequest.toBeneficiaryTransport() = BeneficiaryTransport(
        parentBeneficiary = this.parentBeneficiary,
        companyId = this.companyId,
        type = employeeOrDependent(this.parentBeneficiary),
        contractType = this.contractType,
        parentBeneficiaryRelationType = this.parentBeneficiaryRelationType,
        parentBeneficiaryRelatedAt = this.parentBeneficiaryRelatedAt?.toLocalDateStartOfDay(),
        activatedAt = this.activatedAt.toLocalDateStartOfDay(),
        hiredAt = this.hiredAt?.toLocalDateStartOfDay(),
        cnpj = this.cnpj.nullIfBlank(),
        birthDate = this.dateOfBirth?.toLocalDateStartOfDay(),
        address = Address(
            postalCode = this.addressPostalCode,
            street = this.addressStreet,
            number = this.addressNumber.toString(),
            complement = this.addressComplement,
            neighbourhood = this.addressNeighborhood,
            city = this.addressCity,
            state = State.valueOf(this.addressState),
        ),
        email = this.email,
        firstName = this.firstName,
        lastName = this.lastName,
        nationalId = this.nationalId,
        phoneNumber = this.phoneNumber,
        mothersName = this.mothersName,
        sex = this.sex,
        initialProductId = initialProductId,
        tags = this.tags?.split(","),
        companySubContractId = subcontractId,
    )

    private fun employeeOrDependent(parentBeneficiaryId: UUID?) =
        if (parentBeneficiaryId != null) BeneficiaryType.DEPENDENT else BeneficiaryType.EMPLOYEE

    private fun String.toLocalDateStartOfDay(): LocalDateTime = LocalDate.parse(this).atStartOfDay().plusHours(3)

    fun BeneficiaryAndPersonRequest.toCassiMemberInfo() =
        buildCassiMemberInfo(this.cassiAccountNumber, this.cassiStartDate, this.cassiExpirationDate)

    fun BeneficiaryRequest.toCassiMemberInfo() =
        buildCassiMemberInfo(this.cassiAccountNumber, this.cassiStartDate, this.cassiExpirationDate)

    private fun buildCassiMemberInfo(
        accountNumber: String?,
        startDate: String?,
        expirationDate: String?,
    ): CassiMemberInfo? =
        if (accountNumber != null && startDate != null && expirationDate != null)
            CassiMemberInfo(
                accountNumber = accountNumber,
                startDate = startDate,
                expirationDate = expirationDate,
            )
        else null

    fun Beneficiary.updateFieldsFromRequest(request: BeneficiaryRequest): Beneficiary = this.copy(
        parentBeneficiary = request.parentBeneficiary,
        companyId = request.companyId,
        companySubContractId = request.subcontractId,
        type = employeeOrDependent(request.parentBeneficiary),
        contractType = request.contractType,
        parentBeneficiaryRelationType = request.parentBeneficiaryRelationType,
        parentBeneficiaryRelatedAt = request.parentBeneficiaryRelatedAt?.toLocalDateStartOfDay(),
        activatedAt = request.activatedAt.toLocalDateStartOfDay(),
        hiredAt = request.hiredAt?.toLocalDateStartOfDay(),
        cnpj = request.cnpj.nullIfBlank()
    )

    fun BeneficiaryAndPersonRequestBatch.sanitize(): List<BeneficiarySimpleAndPersonBatchRequest> =
        this.batch.map {
            it.copy(
                parentNationalId = it.parentNationalId?.trim(),
                companyCnpj = it.companyCnpj.trim(),
                activatedAt = it.activatedAt.trim(),
                email = it.email.trim(),
                nationalId = it.nationalId.trim(),
                firstName = it.firstName.trim(),
                lastName = it.lastName.trim(),
                socialName = it.socialName?.trim(),
                addressPostalCode = it.addressPostalCode.trim(),
                addressStreet = it.addressStreet.trim(),
                addressComplement = it.addressComplement?.trim(),
                addressNeighborhood = it.addressNeighborhood?.trim(),
                addressCity = it.addressCity.trim(),
                addressState = it.addressState.trim(),
                dateOfBirth = it.dateOfBirth?.trim(),
                phoneNumber = it.phoneNumber?.trim(),
                mothersName = it.mothersName,
                tags = it.tags?.trim(),
                initialProductName = it.initialProductName.trim(),
                cassiExpirationDate = it.cassiExpirationDate?.trim(),
                cassiAccountNumber = it.cassiAccountNumber?.trim(),
                cassiStartDate = it.cassiStartDate?.trim(),
                cnpj = it.cnpj?.trim().nullIfBlank(),
                hiredAt = it.hiredAt,
            )
        }

    fun BeneficiarySimpleAndPersonBatchRequest.toBeneficiaryAndPersonRequest(
        parentPeople: List<BeneficiaryWithPerson>,
        companies: List<Company>,
        initialProductId: UUID?,
    ): BeneficiaryAndPersonRequest = BeneficiaryAndPersonRequest(
        parentBeneficiary = this.parentNationalId?.findParentId(parentPeople),
        companyId = companies.first { it.cnpj == this.companyCnpj }.id,
        contractType = this.getContractType(),
        subcontractId = this.subContractId,
        parentBeneficiaryRelationType = this.getParentBeneficiaryRelationType(),
        activatedAt = this.activatedAt,
        email = this.email,
        nationalId = this.nationalId,
        firstName = this.firstName,
        lastName = this.lastName,
        socialName = this.socialName,
        sex = this.getSex(),
        dateOfBirth = this.dateOfBirth,
        phoneNumber = this.phoneNumber,
        tags = this.tags,
        addressState = this.addressState,
        addressCity = this.addressCity,
        addressNumber = this.addressNumber,
        addressStreet = this.addressStreet,
        addressPostalCode = this.addressPostalCode,
        addressComplement = this.addressComplement,
        addressNeighborhood = this.addressNeighborhood,
        mothersName = mothersName,
        flowType = this.getFlowType(),
        initialProductId = initialProductId,
        cassiAccountNumber = this.cassiAccountNumber,
        cassiStartDate = this.cassiStartDate,
        cassiExpirationDate = this.cassiExpirationDate,
        cnpj = this.cnpj.nullIfBlank(),
        hiredAt = this.hiredAt,
    )


    private fun String.findParentId(parentBeneficiary: List<BeneficiaryWithPerson>) =
        this.let { parentBeneficiary.first { beneficiaryWithPerson -> beneficiaryWithPerson.person.nationalId == it } }.beneficiary.id

    fun createBeneficiaryWithPerson(beneficiaries: List<Beneficiary>, people: List<Person>) =
        beneficiaries.map { beneficiary ->
            BeneficiaryWithPerson(beneficiary, people.first { person -> person.id == beneficiary.personId })
        }

    fun Parameters.toBeneficiaryFilterWithCompanyId(sortParams: Pair<String, String>?, range: IntRange) =
        BeneficiaryCompiledViewFilters(
            companyId = this["company_id"]?.toUUID(),
            insuranceStatus = this["insurance_statuses"]?.splitAndMap { BeneficiaryViewInsuranceStatus.valueOf(it) },
            beneficiaryType = this["type"]?.splitAndMap { BeneficiaryType.valueOf(it) },
            onboardingStatus = this["onboarding_phase"]?.splitAndMap {
                BeneficiaryOnboardingPhaseType.valueOf(it).toOnboardingStatus()
            }?.filterNotNull(),
            activeOnboardingStatus = this["onboarding_phase"]?.splitAndMap {
                BeneficiaryOnboardingPhaseType.valueOf(it).toActiveOnboardingStatus()
            }?.filterNotNull(),
            productIds = this["product_ids"]?.splitAndMap { it.toUUID() },
            beneficiaryActivatedAt = this.getRangeDate("activated"),
            beneficiaryCanceledAt = this.getRangeDate("canceled"),
            search = this["search_token"]?.cleanSymbols(),
            sortBy = sortParams?.first?.orNull(),
            sortOrder = sortParams?.second?.orNull(),
            range = range,
        )

    private fun <R> String.splitAndMap(transform: (String) -> R): List<R> = this.split(",").map(transform)

    private fun Parameters.getRangeDate(key: String): BeneficiaryCompiledViewFilters.LocalDateTimeRange =
        BeneficiaryCompiledViewFilters.LocalDateTimeRange(
            start = this["${key}_at_start"]?.let(::parseFromParam),
            end = this["${key}_at_end"]?.let(::parseFromParam),
        )

    private fun parseFromParam(date: String) = LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE).atStartOfDay()

    fun BeneficiaryCompiledView.toResponseInfo(companies: List<Company>) = BeneficiaryCompiledViewResponse(
        id = this.id,
        companyId = this.companyId,
        companyName = companies.first { it.id == this.companyId }.name,
        personId = this.personId.toUUID(),
        personNationalId = this.personNationalId,
        personFullSocialName = this.personFullSocialName,
        personEmail = this.personEmail,
        beneficiaryId = this.beneficiaryId,
        beneficiaryType = this.beneficiaryType,
        beneficiaryContractType = this.beneficiaryContractType,
        beneficiaryActivatedAt = this.beneficiaryActivatedAt,
        beneficiaryCanceledAt = this.beneficiaryCanceledAt,
        productId = this.productId,
        productTitle = this.productTitle,
        productDisplayName = this.productDisplayName,
        memberId = this.memberId,
        memberStatus = this.memberStatus,
        memberCanceledAt = this.memberCanceledAt,
        immersionStatus = this.immersionStatus,
        insuranceStatus = this.insuranceStatus,
        onboardingStatus = this.lastOnboardingPhase,
        flowType = this.flowType,
        version = this.version
    )

    fun BeneficiaryOnboarding.toBeneficiaryOnboardingResponse() = BeneficiaryOnboardingResponse(
        id = this.id,
        beneficiaryId = this.beneficiaryId,
        flowType = this.flowType,
        phases = this.phases.sortedBy { it.transactedAt }.map {
            BeneficiaryOnboardingPhaseResponse(
                phase = it.phase,
                transactedAt = it.transactedAt,
                finishedAt = it.finishedAt,
                isCurrentPhase = this.currentPhase?.id == it.id
            )
        }.plus(allPhases
            .filterNot { phase -> this.phases.any { it.phase == phase } }
            .map {
                BeneficiaryOnboardingPhaseResponse(
                    phase = it,
                    transactedAt = null,
                    finishedAt = null,
                    isCurrentPhase = false
                )
            })
    )
}
