package br.com.alice.common.codegen

import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.DeleterList
import br.com.alice.common.service.data.client.UpdaterList

object ClientCodeTemplate {

    private const val WRITE_LIST_LIMIT = 50
    private const val COLLECTION_PACKAGE = "kotlin.collections"
    private val FUNCTION_NAMES_TO_VALIDATE_LIST_PARAMETER = listOf(
        UpdaterList<*>::updateList.name,
        AdderList<*>::addList.name,
        DeleterList<*>::deleteList.name
    )

    fun clientCodeTemplate(service: RemoteServiceInterface) = with(service) {
        """package $clientPackageName

import br.com.alice.common.rfc.ClientServiceProxy
import br.com.alice.common.rfc.Invoker
import $clientPackageName.$className
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map

class ${className}Client(invoker: Invoker) : $className {

    private val clientProxy = ClientServiceProxy(invoker, this)

${clientCodeFunctions(clientPackageName, className, functions)}

}
"""
    }

    private fun clientCodeFunctions(clientPackageName: String, className: String, functions: List<RemoteServiceMethod>) =
        functions.joinToString(separator = "\n\n") { clientCodeFunction(clientPackageName, className, it) }

    private fun clientCodeFunction(clientPackageName: String, className: String, method: RemoteServiceMethod): String {
        val paramsString = method.params.joinToString { "${it.first}: ${it.second}" }

        return if (method.isListFunction() && method.hasPackageListParameter(clientPackageName)) {
            val index = method.indexOfPackageListParameter(clientPackageName)
            val paramsNameString = method.params.mapIndexed { i, param ->
                if (i == index)
                    Pair("it", param.second)
                else param
            }.joinToString { it.first }

            """    override suspend fun ${method.name}($paramsString) =
              |        models.chunked($WRITE_LIST_LIMIT).map { clientProxy.invoke($className::${method.name}, $paramsNameString) }.lift().map { it.flatten() }""".trimMargin()
        } else {
            val paramsNameString = if (method.params.isNotEmpty()) {
                ", ${method.params.joinToString { it.first }}"
            } else ""

            "    override suspend fun ${method.name}($paramsString) = clientProxy.invoke($className::${method.name}$paramsNameString)"
        }
    }

    private fun RemoteServiceMethod.isListFunction() =
        FUNCTION_NAMES_TO_VALIDATE_LIST_PARAMETER.contains(name)

    private fun RemoteServiceMethod.hasPackageListParameter(clientPackageName: String) =
        indexOfPackageListParameter(clientPackageName) >= 0

    private fun RemoteServiceMethod.indexOfPackageListParameter(clientPackageName: String) =
        params.indexOfFirst { it.second.isPackageListParameter(clientPackageName) }

    private fun String.isPackageListParameter(clientPackageName: String) =
        startsWith(COLLECTION_PACKAGE) && contains("<$clientPackageName.")
}
