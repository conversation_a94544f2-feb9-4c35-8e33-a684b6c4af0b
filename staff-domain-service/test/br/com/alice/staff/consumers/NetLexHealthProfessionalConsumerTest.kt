package br.com.alice.staff.consumers

import br.com.alice.data.layer.models.StaffSignupRequest
import br.com.alice.data.layer.models.StaffSignupRequestStatus
import br.com.alice.data.layer.models.StaffSignupRequestType
import br.com.alice.staff.client.StaffSignupRequestService
import br.com.alice.staff.converters.NetLexHealthProfessionalConverter
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.staff.models.Address
import br.com.alice.staff.models.Contact
import br.com.alice.staff.models.HealthProfessional
import br.com.alice.staff.models.NetLexHealthProfessionalPayload
import br.com.alice.staff.models.Phone
import br.com.alice.staff.models.Provider
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.BeforeTest


class NetLexHealthProfessionalConsumerTest : ConsumerTest() {
    private val staffSignupRequestService: StaffSignupRequestService = mockk()
    private val consumer = NetLexHealthProfessionalConsumer(staffSignupRequestService)

    @BeforeTest
    fun setUp() {
        super.before()
    }

    @Test
    fun `createHealthProfessional should process event and create health professional`() = runBlocking<Unit> {
        val requestPayload = payload()
        val event = NetLexCreateHealthProfessionalEvent(data = requestPayload)

        val signupRequest = StaffSignupRequest(
            status = StaffSignupRequestStatus.PENDING,
            integrationContent = NetLexHealthProfessionalConverter.convert(requestPayload),
            type = StaffSignupRequestType.HEALTH_PROFESSIONAL
        )

        coEvery {
            staffSignupRequestService.create(match {
                it.status == StaffSignupRequestStatus.PENDING &&
                        it.type == StaffSignupRequestType.HEALTH_PROFESSIONAL &&
                        it.integrationContent == NetLexHealthProfessionalConverter.convert(requestPayload)
            })
        } returns signupRequest.success()

        val result = consumer.createHealthProfessional(event)

        assertThat(result).isSuccessWithData(signupRequest)

        coVerifyOnce { staffSignupRequestService.create(any()) }
    }

    @Test
    fun `createHealthProfessional should return failure if service throws exception`() = runBlocking {
        val requestPayload = payload()
        val event = NetLexCreateHealthProfessionalEvent(data = requestPayload)

        coEvery { staffSignupRequestService.create(any()) } throws Exception("Erro ao criar profissional")

        val result = consumer.createHealthProfessional(event)

        assertThat(result).isFailure()

        coVerifyOnce { staffSignupRequestService.create(any()) }
    }

    private fun payload() = NetLexHealthProfessionalPayload(
        healthProfessional = HealthProfessional(
            fullName = "João Silva",
            email = "<EMAIL>",
            nationalId = "12345678900",
            birthdate = "1980-01-01",
            gender = "MALE",
            profileImageUrl = "",
            profileBio = "",
            education = "Formado em Medicina pela USP",
            curiosity = "Gosto de trilhas",
            councilType = "CRM",
            councilNumber = "123456",
            councilState = "SP",
            specialty = "Clínico Geral",
            subSpecialties = emptyList(),
            contacts = listOf(
                Contact(
                    address = Address(
                        street = "Rua das Flores",
                        number = "123",
                        complement = "Apto 45",
                        neighborhood = "Jardim das Rosas",
                        city = "São Paulo",
                        state = "SP",
                        zipcode = "01234567",
                        country = "BR"
                    ),
                    phones = listOf(
                        Phone(type = "WHATSAPP", number = "**********"),
                        Phone(type = "MOBILE", number = "***********")
                    ),
                    modality = "PRESENTIAL"
                )
            )
        ),
        provider = Provider(
            name = "Saúde - Unidade Centro",
            cnpj = "12.345.678/0002-90",
            cnes = "1234567",
            bankCode = "001",
            agencyNumber = "1234-x",
            accountNumber = "56789-0",
            phones = listOf(
                Phone(type = "MOBILE", number = "***********"),
                Phone(type = "WHATSAPP", number = "***********")
            ),
            address = Address(
                street = "Av. Paulista",
                number = "1000",
                complement = "10º andar",
                neighborhood = "Bela Vista",
                city = "São Paulo",
                state = "SP",
                zipcode = "01310-100",
                country = "BR"
            )
        )
    )
}
