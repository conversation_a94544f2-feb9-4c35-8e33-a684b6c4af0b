package br.com.alice.staff.converters

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.data.layer.helpers.TestModelFactory
import org.junit.jupiter.api.Test
import org.assertj.core.api.Assertions.assertThat

class StaffSignupRequestToStaffConverterTest {
    @Test
    fun `should convert StaffSignupRequestHealthProfessional to Staff`() {
        val staffSignupRequest = TestModelFactory.buildStaffSignupRequest()
        val hp = staffSignupRequest.integrationContent.healthProfessional

        val result = StaffSignupRequestToStaffConverter.convert(
            source = hp
        )

        assertThat(result.email).isEqualTo(hp.email)
        assertThat(result.firstName).isEqualTo("firstName")
        assertThat(result.lastName).isEqualTo("lastName")
        assertThat(result.gender.name).isEqualTo(hp.gender)
        assertThat(result.nationalId).isEqualTo(hp.nationalId)
        assertThat(result.birthdate).isEqualTo(hp.birthdate?.toLocalDate())
        assertThat(result.profileImageUrl).isEqualTo(hp.profileImageUrl)
        assertThat(result.active).isTrue()
        assertThat(result.role).isEqualTo(Role.HEALTH_COMMUNITY)
        assertThat(result.type).isEqualTo(StaffType.COMMUNITY_SPECIALIST)
    }
}
