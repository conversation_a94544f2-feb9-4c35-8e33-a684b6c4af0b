package br.com.alice.staff.converters

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.StaffSignupRequestAdditionalInfo
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

class StaffSignupRequestToHealthProfessionalConverterTest {
    @Test
    fun `should convert StaffSignupRequestHealthProfessional to HealthProfessional`() {
        val staffId = UUID.randomUUID()
        val staffSignupRequest = TestModelFactory.buildStaffSignupRequest(
            additionalInfo = StaffSignupRequestAdditionalInfo(
                tier = "EXPERT",
                theoristTier = "EXPERT",
                showOnApp = true,
                specialtyId = UUID.randomUUID(),
                subSpecialtyIds = listOf(UUID.randomUUID())
            )
        )
        val additionalInfo = staffSignupRequest.additionalInfo
        val healthProfessional = staffSignupRequest.integrationContent.healthProfessional


        val result = StaffSignupRequestToHealthProfessionalConverter.convert(
            source = healthProfessional,
            staffId = staffId,
            additionalInfo = additionalInfo!!
        )

        assertThat(result.id).isEqualTo(staffId)
        assertThat(result.staffId).isEqualTo(staffId)
        assertThat(result.profileBio).isEqualTo(healthProfessional.profileBio)
        assertThat(result.council.type?.name).isEqualTo(healthProfessional.councilType)
        assertThat(result.council.number).isEqualTo(healthProfessional.councilNumber)
        assertThat(result.council.state.name).isEqualTo(healthProfessional.councilState)
        assertThat(result.quote).isEqualTo(healthProfessional.curiosity)
        assertThat(result.education).containsExactly(healthProfessional.education)
        assertThat(result.tier?.name).isEqualTo(additionalInfo.tier)
        assertThat(result.theoristTier?.name).isEqualTo(additionalInfo.theoristTier)
        assertThat(result.showOnApp).isEqualTo(additionalInfo.showOnApp)
        assertThat(result.email).isEqualTo(healthProfessional.email)
        assertThat(result.name).isEqualTo(healthProfessional.fullName)
        assertThat(result.gender?.name).isEqualTo(healthProfessional.gender)
        assertThat(result.nationalId).isEqualTo(healthProfessional.nationalId)
        assertThat(result.role).isEqualTo(Role.COMMUNITY)
        assertThat(result.type).isEqualTo(StaffType.COMMUNITY_SPECIALIST)
        assertThat(result.specialtyId).isEqualTo(additionalInfo.specialtyId)
        assertThat(result.subSpecialtyIds).isEqualTo(additionalInfo.subSpecialtyIds)
    }
}
