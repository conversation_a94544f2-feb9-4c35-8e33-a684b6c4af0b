package br.com.alice.staff.converters

import br.com.alice.data.layer.models.*
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class StaffSignupRequestConverterTest {
    val uuid = UUID.randomUUID()
    val now = LocalDateTime.now()

    private val staffSignupRequest = StaffSignupRequest(
        id = uuid,
        version = 1,
        createdAt = now,
        updatedAt = now,
        status = StaffSignupRequestStatus.PENDING,
        rejectionReason = null,
        reviewedBy = null,
        staffId = null,
        integrationContent = StaffSignupRequestIntegrationContent(
            healthProfessional = StaffSignupRequestHealthProfessional(
                fullName = "João Silva",
                email = "<EMAIL>",
                nationalId = "12345678900",
                birthdate = "1990-01-01",
                gender = "MALE",
                profileImageUrl = null,
                profileBio = null,
                education = "Medicina UNICAMP",
                curiosity = "Gosta de leitura",
                councilType = "CRM",
                councilNumber = "123456",
                councilState = "SP",
                specialty = "Cardiologia",
                subSpecialties = listOf("Eletrofisiologia"),
                contacts = listOf(
                    StaffSignupRequestContact(
                        address = StaffSignupRequestAddress(
                            street = "Rua A",
                            number = "123",
                            complement = null,
                            neighborhood = "Centro",
                            state = "SP",
                            city = "São Paulo",
                            zipcode = "01000-000",
                            country = "BR"
                        ),
                        phones = listOf(
                            StaffSignupRequestPhone("MOBILE", "***********")
                        ),
                        modality = "PRESENTIAL"
                    )
                )
            ),
            provider = StaffSignupRequestProvider(
                name = "Clínica Teste",
                cnpj = "12.345.678/0001-00",
                cnes = "1234567",
                bankCode = "001",
                agencyNumber = "1234",
                accountNumber = "56789-0",
                phones = listOf(
                    StaffSignupRequestPhone("WHATSAPP", "***********")
                ),
                address = StaffSignupRequestAddress(
                    street = "Av. B",
                    number = "1000",
                    complement = null,
                    neighborhood = "Bairro B",
                    state = "SP",
                    city = "São Paulo",
                    zipcode = "02000-000",
                    country = "BR"
                )
            )
        ),
        type = StaffSignupRequestType.HEALTH_PROFESSIONAL,
        additionalInfo = StaffSignupRequestAdditionalInfo(
            tier = "A",
            theoristTier = "B",
            showOnApp = true,
            specialtyId = UUID.randomUUID(),
            subSpecialtyIds = listOf(UUID.randomUUID())
        )
    )

    private val staffSignupRequestModel = StaffSignupRequestModel(
        id = staffSignupRequest.id,
        version = staffSignupRequest.version,
        createdAt = staffSignupRequest.createdAt,
        updatedAt = staffSignupRequest.updatedAt,
        status = StaffSignupRequestStatusModel.PENDING,
        rejectionReason = staffSignupRequest.rejectionReason,
        reviewedBy = staffSignupRequest.reviewedBy,
        staffId = staffSignupRequest.staffId,
        integrationContent = StaffSignupRequestIntegrationContentModel(
            healthProfessional = StaffSignupRequestHealthProfessionalModel(
                fullName = staffSignupRequest.integrationContent.healthProfessional.fullName,
                email = staffSignupRequest.integrationContent.healthProfessional.email,
                nationalId = staffSignupRequest.integrationContent.healthProfessional.nationalId,
                birthdate = staffSignupRequest.integrationContent.healthProfessional.birthdate,
                gender = staffSignupRequest.integrationContent.healthProfessional.gender,
                profileImageUrl = staffSignupRequest.integrationContent.healthProfessional.profileImageUrl,
                profileBio = staffSignupRequest.integrationContent.healthProfessional.profileBio,
                education = staffSignupRequest.integrationContent.healthProfessional.education,
                curiosity = staffSignupRequest.integrationContent.healthProfessional.curiosity,
                councilType = staffSignupRequest.integrationContent.healthProfessional.councilType,
                councilNumber = staffSignupRequest.integrationContent.healthProfessional.councilNumber,
                councilState = staffSignupRequest.integrationContent.healthProfessional.councilState,
                specialty = staffSignupRequest.integrationContent.healthProfessional.specialty,
                subSpecialties = staffSignupRequest.integrationContent.healthProfessional.subSpecialties,
                contacts = staffSignupRequest.integrationContent.healthProfessional.contacts.map {
                    StaffSignupRequestContactModel(
                        address = StaffSignupRequestAddressModel(
                            street = it.address.street,
                            number = it.address.number,
                            complement = it.address.complement,
                            neighborhood = it.address.neighborhood,
                            state = it.address.state,
                            city = it.address.city,
                            zipcode = it.address.zipcode,
                            country = it.address.country
                        ),
                        phones = it.phones.map { phone ->
                            StaffSignupRequestPhoneModel(
                                type = phone.type,
                                number = phone.number
                            )
                        },
                        modality = it.modality
                    )
                }
            ),
            provider = StaffSignupRequestProviderModel(
                name = staffSignupRequest.integrationContent.provider.name,
                cnpj = staffSignupRequest.integrationContent.provider.cnpj,
                cnes = staffSignupRequest.integrationContent.provider.cnes,
                bankCode = staffSignupRequest.integrationContent.provider.bankCode,
                agencyNumber = staffSignupRequest.integrationContent.provider.agencyNumber,
                accountNumber = staffSignupRequest.integrationContent.provider.accountNumber,
                phones = staffSignupRequest.integrationContent.provider.phones.map { phone ->
                    StaffSignupRequestPhoneModel(
                        type = phone.type,
                        number = phone.number
                    )
                },
                address = StaffSignupRequestAddressModel(
                    street = staffSignupRequest.integrationContent.provider.address.street,
                    number = staffSignupRequest.integrationContent.provider.address.number,
                    complement = staffSignupRequest.integrationContent.provider.address.complement,
                    neighborhood = staffSignupRequest.integrationContent.provider.address.neighborhood,
                    state = staffSignupRequest.integrationContent.provider.address.state,
                    city = staffSignupRequest.integrationContent.provider.address.city,
                    zipcode = staffSignupRequest.integrationContent.provider.address.zipcode,
                    country = staffSignupRequest.integrationContent.provider.address.country
                )
            )
        ),
        type = StaffSignupRequestTypeModel.HEALTH_PROFESSIONAL,
        additionalInfo =  StaffSignupRequestAdditionalInfoModel(
            tier = staffSignupRequest.additionalInfo!!.tier,
            theoristTier = staffSignupRequest.additionalInfo!!.theoristTier,
            showOnApp = staffSignupRequest.additionalInfo!!.showOnApp,
            specialtyId = staffSignupRequest.additionalInfo!!.specialtyId,
            subSpecialtyIds = staffSignupRequest.additionalInfo!!.subSpecialtyIds
        )
    )

    @Test
    fun testToModel() {
        assertEquals(staffSignupRequestModel, staffSignupRequest.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(staffSignupRequest, staffSignupRequestModel.toTransport())
    }

}
