package br.com.alice.staff.models

data class NetLexHealthProfessionalPayload(
    val healthProfessional: HealthProfessional,
    val provider: Provider
)

data class HealthProfessional(
    val fullName: String,
    val email: String,
    val nationalId: String?,
    val birthdate: String?,
    val gender: String,
    val profileImageUrl: String?,
    val profileBio: String?,
    val education: String?,
    val curiosity: String?,
    val councilType: String,
    val councilNumber: String,
    val councilState: String,
    val specialty: String?,
    val subSpecialties: List<String> = emptyList(),
    val contacts: List<Contact>
)

data class Contact(
    val address: Address,
    val phones: List<Phone>,
    val modality: String
)

data class Phone(
    val type: String,
    val number: String
)

data class Address(
    val street: String,
    val number: String,
    val complement: String?,
    val neighborhood: String,
    val state: String,
    val city: String,
    val zipcode: String,
    val country: String?
)

data class Provider(
    val name: String,
    val cnpj: String,
    val cnes: String?,
    val bankCode: String?,
    val agencyNumber: String?,
    val accountNumber: String?,
    val phones: List<Phone> = emptyList(),
    val address: Address
)
