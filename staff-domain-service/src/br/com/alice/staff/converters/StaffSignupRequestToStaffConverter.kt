package br.com.alice.staff.converters

import br.com.alice.common.Converter
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StaffSignupRequestHealthProfessional
import br.com.alice.common.core.extensions.getFirstAndLastName

object StaffSignupRequestToStaffConverter : Converter<StaffSignupRequestHealthProfessional, Staff>(
    StaffSignupRequestHealthProfessional::class, Staff::class
) {
    fun convert(source: StaffSignupRequestHealthProfessional): Staff {
        val (firstName, lastName) = source.fullName.getFirstAndLastName()

        return Staff(
            id = RangeUUID.generate(),
            email = source.email,
            firstName = firstName,
            lastName = lastName,
            gender = Gender.valueOf(source.gender),
            nationalId = source.nationalId,
            birthdate = source.birthdate?.toLocalDate(),
            profileImageUrl = source.profileImageUrl,
            role = Role.HEALTH_COMMUNITY,
            type = StaffType.COMMUNITY_SPECIALIST,
            active = true
        )
    }
}
