package br.com.alice.staff.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.StaffSignupRequestAddress
import br.com.alice.data.layer.models.StaffSignupRequestContact
import br.com.alice.data.layer.models.StaffSignupRequestHealthProfessional
import br.com.alice.data.layer.models.StaffSignupRequestIntegrationContent
import br.com.alice.data.layer.models.StaffSignupRequestPhone
import br.com.alice.data.layer.models.StaffSignupRequestProvider
import br.com.alice.staff.models.NetLexHealthProfessionalPayload

object NetLexHealthProfessionalConverter :
    Converter<NetLexHealthProfessionalPayload, StaffSignupRequestIntegrationContent>(
        NetLexHealthProfessionalPayload::class, StaffSignupRequestIntegrationContent::class
    ) {
    fun convert(source: NetLexHealthProfessionalPayload): StaffSignupRequestIntegrationContent {
        return StaffSignupRequestIntegrationContent(
            healthProfessional = StaffSignupRequestHealthProfessional(
                fullName = source.healthProfessional.fullName,
                email = source.healthProfessional.email,
                nationalId = source.healthProfessional.nationalId,
                birthdate = source.healthProfessional.birthdate,
                gender = source.healthProfessional.gender,
                profileImageUrl = source.healthProfessional.profileImageUrl,
                profileBio = source.healthProfessional.profileBio,
                education = source.healthProfessional.education,
                curiosity = source.healthProfessional.curiosity,
                councilType = source.healthProfessional.councilType,
                councilNumber = source.healthProfessional.councilNumber,
                councilState = source.healthProfessional.councilState,
                specialty = source.healthProfessional.specialty,
                subSpecialties = source.healthProfessional.subSpecialties,
                contacts = source.healthProfessional.contacts.map {
                    StaffSignupRequestContact(
                        address = StaffSignupRequestAddress(
                            street = it.address.street,
                            number = it.address.number,
                            complement = it.address.complement,
                            neighborhood = it.address.neighborhood,
                            state = it.address.state,
                            city = it.address.city,
                            zipcode = it.address.zipcode,
                            country = it.address.country
                        ),
                        phones = it.phones.map { phone ->
                            StaffSignupRequestPhone(
                                type = phone.type,
                                number = phone.number
                            )
                        },
                        modality = it.modality
                    )
                }
            ),
            provider = StaffSignupRequestProvider(
                name = source.provider.name,
                cnpj = source.provider.cnpj,
                cnes = source.provider.cnes,
                bankCode = source.provider.bankCode,
                agencyNumber = source.provider.agencyNumber,
                accountNumber = source.provider.accountNumber,
                phones = source.provider.phones.map {
                    StaffSignupRequestPhone(type = it.type, number = it.number)
                },
                address = StaffSignupRequestAddress(
                    street = source.provider.address.street,
                    number = source.provider.address.number,
                    complement = source.provider.address.complement,
                    neighborhood = source.provider.address.neighborhood,
                    state = source.provider.address.state,
                    city = source.provider.address.city,
                    zipcode = source.provider.address.zipcode,
                    country = source.provider.address.country
                )
            )
        )
    }
}
