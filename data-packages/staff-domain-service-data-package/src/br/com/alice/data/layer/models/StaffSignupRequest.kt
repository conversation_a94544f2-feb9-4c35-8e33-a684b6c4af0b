package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class StaffSignupRequest(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val status: StaffSignupRequestStatus,
    val rejectionReason: String? = null,
    val reviewedBy: UUID? = null,
    val staffId: UUID? = null,
    val integrationContent: StaffSignupRequestIntegrationContent,
    val type: StaffSignupRequestType,
    val additionalInfo: StaffSignupRequestAdditionalInfo? = null,
    val searchTokens: String? = null,
)

data class StaffSignupRequestIntegrationContent(
    val healthProfessional: StaffSignupRequestHealthProfessional,
    val provider: StaffSignupRequestProvider
) : JsonSerializable

data class StaffSignupRequestHealthProfessional(
    val fullName: String,
    val email: String,
    val nationalId: String?,
    val birthdate: String?,
    val gender: String,
    val profileImageUrl: String?,
    val profileBio: String?,
    val education: String?,
    val curiosity: String?,
    val councilType: String,
    val councilNumber: String,
    val councilState: String,
    val specialty: String?,
    val subSpecialties: List<String> = emptyList(),
    val contacts: List<StaffSignupRequestContact>
) : JsonSerializable

data class StaffSignupRequestProvider(
    val name: String,
    val cnpj: String,
    val cnes: String?,
    val bankCode: String?,
    val agencyNumber: String?,
    val accountNumber: String?,
    val phones: List<StaffSignupRequestPhone> = emptyList(),
    val address: StaffSignupRequestAddress
) : JsonSerializable

data class StaffSignupRequestContact(
    val address: StaffSignupRequestAddress,
    val phones: List<StaffSignupRequestPhone>,
    val modality: String
) : JsonSerializable

data class StaffSignupRequestPhone(
    val type: String,
    val number: String
) : JsonSerializable

data class StaffSignupRequestAddress(
    val street: String,
    val number: String,
    val complement: String?,
    val neighborhood: String,
    val state: String,
    val city: String,
    val zipcode: String,
    val country: String?
) : JsonSerializable

data class StaffSignupRequestAdditionalInfo(
    val tier: String,
    val theoristTier: String,
    val showOnApp: Boolean,
    val specialtyId: UUID,
    val subSpecialtyIds: List<UUID>,
    val imageUrl: String? = null
) : JsonSerializable

enum class StaffSignupRequestType(val description: String) {
    HEALTH_PROFESSIONAL("Profissional de Saúde")
}

enum class StaffSignupRequestStatus(val description: String) {
    PENDING("Pendente"),
    APPROVED("Aprovado"),
    REJECTED("Rejeitado")
}


