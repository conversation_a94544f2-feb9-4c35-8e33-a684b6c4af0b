package br.com.alice.data.layer.models

import br.com.alice.common.helpers.MockedTestHelper
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class StaffSignupRequestContactTest : MockedTestHelper() {
    @Test
    fun `should create a valid StaffSignupRequest`() {
        val request = StaffSignupRequest(
            status = StaffSignupRequestStatus.PENDING,
            type = StaffSignupRequestType.HEALTH_PROFESSIONAL,
            integrationContent = StaffSignupRequestIntegrationContent(
                healthProfessional = StaffSignupRequestHealthProfessional(
                    fullName = "firstName lastName",
                    email = "email",
                    nationalId = "nationalId",
                    birthdate = "birthdate",
                    gender = "gender",
                    profileImageUrl = "profileImageUrl",
                    profileBio = "profileBio",
                    education = "education",
                    curiosity = "curiosity",
                    councilType = "councilType",
                    councilNumber = "councilNumber",
                    councilState = "councilState",
                    specialty = "specialty",
                    subSpecialties = emptyList(),
                    contacts = emptyList()
                ),
                provider = StaffSignupRequestProvider(
                    name = "name",
                    cnpj = "cnpj",
                    cnes = "cnes",
                    bankCode = "bankCode",
                    agencyNumber = "agencyNumber",
                    accountNumber = "accountNumber",
                    phones = emptyList(),
                    address = StaffSignupRequestAddress(
                        street = "street",
                        number = "number",
                        complement = "complement",
                        neighborhood = "neighborhood",
                        state = "state",
                        city = "city",
                        zipcode = "zipcode",
                        country = "country"
                    )
                )
            ),
        )

        assertThat(request.status).isEqualTo(StaffSignupRequestStatus.PENDING)
        assertThat(request.type).isEqualTo(StaffSignupRequestType.HEALTH_PROFESSIONAL)
        assertThat(request.additionalInfo).isNull()
        assertThat(request.rejectionReason).isNull()
        assertThat(request.reviewedBy).isNull()
        assertThat(request.staffId).isNull()
        assertThat(request.id).isNotNull()

        val hp = request.integrationContent.healthProfessional
        assertThat(hp.fullName).isEqualTo("firstName lastName")
        assertThat(hp.email).isEqualTo("email")
        assertThat(hp.contacts).isEmpty()

        val provider = request.integrationContent.provider
        assertThat(provider.name).isEqualTo("name")
        assertThat(provider.address.city).isEqualTo("city")
        assertThat(provider.phones).isEmpty()
    }
}
