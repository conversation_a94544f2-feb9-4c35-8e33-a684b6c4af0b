package br.com.alice.data.layer

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.StaffSignupRequestAdditionalInfoModel
import br.com.alice.data.layer.models.StaffSignupRequestAddressModel
import br.com.alice.data.layer.models.StaffSignupRequestHealthProfessionalModel
import br.com.alice.data.layer.models.StaffSignupRequestIntegrationContentModel
import br.com.alice.data.layer.models.StaffSignupRequestModel
import br.com.alice.data.layer.models.StaffSignupRequestProviderModel
import br.com.alice.data.layer.models.StaffSignupRequestStatusModel
import br.com.alice.data.layer.models.StaffSignupRequestTypeModel
import java.time.LocalDateTime
import java.util.UUID

fun buildStaffSignupRequestModel(
    id: UUID = RangeUUID.generate(),
    version: Int = 0,
    createdAt: LocalDateTime = LocalDateTime.now(),
    updatedAt: LocalDateTime = LocalDateTime.now(),
    status: StaffSignupRequestStatusModel = StaffSignupRequestStatusModel.PENDING,
    rejectionReason: String? = null,
    reviewedBy: UUID? = null,
    staffId: UUID? = null,
    integrationContent: StaffSignupRequestIntegrationContentModel = StaffSignupRequestIntegrationContentModel(
        healthProfessional = StaffSignupRequestHealthProfessionalModel(
            fullName = "firstName lastName",
            email = "email",
            nationalId = "nationalId",
            birthdate = "birthdate",
            gender = "gender",
            profileImageUrl = "profileImageUrl",
            profileBio = "profileBio",
            education = "education",
            curiosity = "curiosity",
            councilType = "councilType",
            councilNumber = "councilNumber",
            councilState = "councilState",
            specialty = "specialty",
            subSpecialties = emptyList(),
            contacts = emptyList()
        ),
        provider = StaffSignupRequestProviderModel(
            name = "name",
            cnpj = "cnpj",
            cnes = "cnes",
            bankCode = "bankCode",
            agencyNumber = "agencyNumber",
            accountNumber = "accountNumber",
            phones = emptyList(),
            address = StaffSignupRequestAddressModel(
                street = "street",
                number = "number",
                complement = "complement",
                neighborhood = "neighborhood",
                state = "state",
                city = "city",
                zipcode = "zipcode",
                country = "country"
            )
        )
    ),
    type: StaffSignupRequestTypeModel = StaffSignupRequestTypeModel.HEALTH_PROFESSIONAL,
    additionalInfo: StaffSignupRequestAdditionalInfoModel? = null,
) = StaffSignupRequestModel(
    id = id,
    version = version,
    createdAt = createdAt,
    updatedAt = updatedAt,
    status = status,
    rejectionReason = rejectionReason,
    reviewedBy = reviewedBy,
    staffId = staffId,
    integrationContent = integrationContent,
    type = type,
    additionalInfo = additionalInfo
)
