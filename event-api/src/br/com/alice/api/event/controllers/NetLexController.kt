package br.com.alice.api.event.controllers

import br.com.alice.api.event.events.netlex.NetLexCreateHealthProfessionalEvent
import br.com.alice.api.event.model.NetLexHealthProfessionalRequest
import br.com.alice.api.event.services.NetLexAuthService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.models.Gender
import br.com.alice.common.models.State

import br.com.alice.common.core.extensions.isValidCpf
import br.com.alice.common.core.extensions.isValidCnpj
import br.com.alice.common.core.extensions.isEmail
import io.ktor.http.HttpStatusCode

class NetLexController(
    private val authService: NetLexAuthService,
    private val kafkaProducerService: KafkaProducerService
) : Controller() {
    fun authorize(request: AuthRequest): Response =
        authService.authorize(request.clientId, request.clientSecret)
            ?.let { Response(HttpStatusCode.OK, AuthResponse(it)) }
            ?: Response(HttpStatusCode.Unauthorized)

    suspend fun createHealthProfessional(request: NetLexHealthProfessionalRequest): Response =
        try {
            validate(request)
            kafkaProducerService.produce(NetLexCreateHealthProfessionalEvent(request))
            Response.OK
        } catch (ex: IllegalArgumentException) {
            logger.warn("Validation error creating Health Professional", ex)
            Response(HttpStatusCode.BadRequest, mapOf("error" to ex.message))
        } catch (ex: Exception) {
            logger.error("Error creating Health Professional", ex)
            Response(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to create Health Professional"))
        }

    private fun validate(request: NetLexHealthProfessionalRequest) {
        val hp = request.healthProfessional
        val provider = request.provider

        // Validate required fields for health professional
        require(hp.fullName.isNotBlank()) { "fullName é obrigatório" }
        require(hp.email.isNotBlank()) { "email é obrigatório" }
        require(hp.email.isEmail()) { "email inválido" }
        require(hp.gender.isNotBlank()) { "gender é obrigatório" }
        require(hp.councilType.isNotBlank()) { "councilType é obrigatório" }
        require(hp.councilNumber.isNotBlank()) { "councilNumber é obrigatório" }
        require(hp.councilState.isNotBlank()) { "councilState é obrigatório" }

        // Validate CPF if provided
        hp.nationalId?.let { cpf ->
            if (cpf.isNotBlank()) {
                require(cpf.isValidCpf()) { "CPF inválido" }
            }
        }

        // Validate enums for health professional
        try {
            Gender.valueOf(hp.gender.uppercase())
        } catch (ex: IllegalArgumentException) {
            throw IllegalArgumentException("gender inválido. Valores aceitos: MALE, FEMALE, NON_BINARY, NO_ANSWER")
        }

        val validCouncilTypes = listOf("CRAS", "COREN", "CRF", "CRFA", "CREFITO", "CRM", "CRN", "CRO", "CRP", "CREFONO", "CRESS", "OUT")
        require(hp.councilType.uppercase() in validCouncilTypes) {
            "councilType inválido. Valores aceitos: ${validCouncilTypes.joinToString()}"
        }

        try {
            State.valueOf(hp.councilState.uppercase())
        } catch (ex: IllegalArgumentException) {
            throw IllegalArgumentException("councilState inválido. Valores aceitos: ${State.values().joinToString()}")
        }

        // Validate contacts
        hp.contacts.forEach { contact ->
            val validModalities = listOf("PRESENTIAL", "REMOTE")
            require(contact.modality.uppercase() in validModalities) {
                "modality inválido. Valores aceitos: ${validModalities.joinToString()}"
            }

            contact.phones.forEach { phone ->
                val validPhoneTypes = listOf("PHONE", "MOBILE", "WHATSAPP")
                require(phone.type.uppercase() in validPhoneTypes) {
                    "phone type inválido. Valores aceitos: ${validPhoneTypes.joinToString()}"
                }
            }

            try {
                State.valueOf(contact.address.state.uppercase())
            } catch (ex: IllegalArgumentException) {
                throw IllegalArgumentException("address state inválido. Valores aceitos: ${State.values().joinToString()}")
            }
        }

        // Validate required fields for provider
        require(provider.name.isNotBlank()) { "provider name é obrigatório" }
        require(provider.cnpj.isNotBlank()) { "provider cnpj é obrigatório" }
        require(provider.cnpj.isValidCnpj()) { "provider cnpj inválido" }

        val providerAddr = provider.address
        require(providerAddr.street.isNotBlank()) { "provider address street é obrigatório" }
        require(providerAddr.number.isNotBlank()) { "provider address number é obrigatório" }
        require(providerAddr.neighborhood.isNotBlank()) { "provider address neighborhood é obrigatório" }
        require(providerAddr.city.isNotBlank()) { "provider address city é obrigatório" }
        require(providerAddr.state.isNotBlank()) { "provider address state é obrigatório" }
        require(providerAddr.zipcode.isNotBlank()) { "provider address zipcode é obrigatório" }

        try {
            State.valueOf(providerAddr.state.uppercase())
        } catch (ex: IllegalArgumentException) {
            throw IllegalArgumentException("provider address state inválido. Valores aceitos: ${State.values().joinToString()}")
        }

        provider.phones.forEach { phone ->
            val validPhoneTypes = listOf("PHONE", "MOBILE", "WHATSAPP")
            require(phone.type.uppercase() in validPhoneTypes) {
                "provider phone type inválido. Valores aceitos: ${validPhoneTypes.joinToString()}"
            }
        }
    }

}

data class AuthRequest(
    val clientId: String,
    val clientSecret: String
)

data class AuthResponse(
    val token: String
)
