package br.com.alice.api.scheduler.controllers.v1

import br.com.alice.api.scheduler.ControllerTestHelper
import br.com.alice.api.scheduler.ServiceConfig
import br.com.alice.api.scheduler.controllers.model.BulkStaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.BulkStaffScheduleUpsertResponse
import br.com.alice.api.scheduler.controllers.model.StaffScheduleEventTypeAssociationRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleProviderResponse
import br.com.alice.api.scheduler.controllers.model.StaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleResponse
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesLastUpdatedBy
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesResponse
import br.com.alice.api.scheduler.controllers.model.toProviderResponse
import br.com.alice.api.scheduler.converters.StaffScheduleResponseConverter
import br.com.alice.common.RangeUUID
import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DataEventPayload
import br.com.alice.common.client.DataEventType
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.fromUTCToSaoPauloTimeZone
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toLocalTime
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.models.Staff
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.StaffScheduleStatus
import br.com.alice.data.layer.models.StaffScheduleType
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.model.StaffScheduleEditOption
import br.com.alice.schedule.model.exceptions.StaffScheduleOverlapException
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.util.date.WeekDay
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalTime
import kotlin.test.BeforeTest
import kotlin.test.Ignore
import kotlin.test.Test

class StaffScheduleControllerTest : ControllerTestHelper() {

    private val token = RangeUUID.generate().toString()
    private val apiVersion = "v1"
    private val staffScheduleService: StaffScheduleService = mockk()
    private val staffService: StaffService = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val dataEventClient: DataEventClient = mockk()

    private val startHour = "09:00".toLocalTime()
    private val untilHour = "12:00".toLocalTime()
    private val weekDay = WeekDay.valueOf("WEDNESDAY")
    private val providerUnitId = RangeUUID.generate()
    private val alsoDigital = false

    private val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId)

    private val appointmentScheduleEventTypeId = RangeUUID.generate()

    private val staffSchedules = listOf(
        TestModelFactory.buildStaffSchedule(
            staff.id,
            startHour,
            untilHour,
            weekDay,
            lastUpdatedBy = staff.id,
            exceptionEventTypes = listOf(appointmentScheduleEventTypeId),
            carveOutHours = 24
        )
    )

    private val staffSchedulesValidForEventType = listOf(
        TestModelFactory.buildStaffSchedule(
            staff.id,
            startHour,
            untilHour,
            weekDay,
            lastUpdatedBy = staff.id,
        )
    )
    private val staffSchedule = staffSchedules.first()
    private val staffForAuth = staff.convertTo(Staff::class)

    @BeforeTest
    override fun setup() {
        super.setup()

        this.module.single { staffScheduleService }
        this.module.single { staffService }
        this.module.single { appointmentScheduleEventTypeService }
        this.module.single { providerUnitService }
        this.module.single { dataEventClient }
        this.module.single { StaffScheduleController(get(), get(), get(), get(), get()) }
    }

    @Test
    fun `#get all staff schedules should return 200 OK with staff schedules when it exists`() {
        coEvery { staffScheduleService.getStaffSchedules(staffSchedule.staffId) } returns staffSchedules.success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        val staffSchedulesResponse = staffSchedules.map { it.convertTo(StaffScheduleResponse::class) }.map {
            it.copy(
                startHour = it.startHour.fromUTCToSaoPauloTimeZone(),
                untilHour = it.untilHour.fromUTCToSaoPauloTimeZone(),
                carveOutDays = 1,
            )
        }.sortedBy { it.startHour }

        val staffScheduleProviderResponse = staffSchedulesResponse.map { it.toProviderResponse(emptyMap()) }

        val expectedResponse = StaffSchedulesResponse(
            staffSchedules = staffScheduleProviderResponse,
            updatedAt = staffSchedule.updatedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
            lastUpdatedBy = StaffSchedulesLastUpdatedBy(
                staff.id,
                staff.fullName,
            )
        )

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedules") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#get all staff schedules should return 200 OK with staff schedules that are valid for event type when there is the query param`() {
        coEvery {
            staffScheduleService.getStaffSchedules(staffSchedule.staffId)
        } returns staffSchedules.plus(staffSchedulesValidForEventType).success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        val staffSchedulesResponse =
            staffSchedulesValidForEventType.map { it.convertTo(StaffScheduleResponse::class) }.map {
                it.copy(
                    startHour = it.startHour.fromUTCToSaoPauloTimeZone(),
                    untilHour = it.untilHour.fromUTCToSaoPauloTimeZone(),
                )
            }.sortedBy { it.startHour }

        val staffScheduleProviderResponse = staffSchedulesResponse.map { it.toProviderResponse(emptyMap()) }

        val expectedResponse = StaffSchedulesResponse(
            staffSchedules = staffScheduleProviderResponse,
            updatedAt = staffSchedule.updatedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
            lastUpdatedBy = StaffSchedulesLastUpdatedBy(
                staff.id,
                staff.fullName,
            )
        )

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedules?appointmentScheduleEventTypeId=${appointmentScheduleEventTypeId}") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }
    @Test
    fun `#get all staff schedules with provider unit name should return 200 OK with staff schedules when it exists`() {
        val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId)

        val staffScheduleProvider = staffSchedules[0].copy(providerUnitId = providerUnitId)

        coEvery { staffScheduleService.getStaffSchedules(staffSchedule.staffId) } returns listOf(staffScheduleProvider).success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        coEvery { providerUnitService.getByIds(listOf(providerUnitId), false) } returns listOf(providerUnit).success()

        val staffSchedulesResponse = listOf(staffScheduleProvider).map { it.convertTo(StaffScheduleResponse::class) }.map {
            it.copy(
                startHour = it.startHour.fromUTCToSaoPauloTimeZone(),
                untilHour = it.untilHour.fromUTCToSaoPauloTimeZone(),
                carveOutDays = 1
            )
        }.sortedBy { it.startHour }

        val staffScheduleProviderResponse = staffSchedulesResponse.map { it.toProviderResponse(mapOf(providerUnitId to providerUnit)) }

        val expectedResponse = StaffSchedulesResponse(
            staffSchedules = staffScheduleProviderResponse,
            updatedAt = staffSchedule.updatedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
            lastUpdatedBy = StaffSchedulesLastUpdatedBy(
                staff.id,
                staff.fullName,
            ),
        )

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedules") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#get staff schedule by ID should return 200 OK with staff schedule when it exists`() {
        coEvery { staffScheduleService.getStaffScheduleById(staffSchedule.id) } returns staffSchedule.success()

        val expectedResponse = StaffScheduleResponseConverter.convert(staffSchedule)

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }


    @Test
    fun `#create staff availability should return 200 OK with newly created staff schedule`() {
        coEvery {
            staffScheduleService.create(
                staff.id,
                startHour.fromSaoPauloToUTCTimeZone(),
                untilHour.fromSaoPauloToUTCTimeZone(),
                weekDay,
                type = StaffScheduleType.HAD,
                providerUnitId = null,
                alsoDigital = true,
                carveOutHours = 24,
            )
        } returns staffSchedule.success()

        coEvery {
            staffScheduleService.update(
                staffSchedule.copy(
                    startHour = startHour.fromSaoPauloToUTCTimeZone(),
                    untilHour = untilHour.fromSaoPauloToUTCTimeZone(),
                    type = StaffScheduleType.HAD,
                    carveOutHours = 24
                )
            )
        } returns staffSchedule.success()

        val request = StaffScheduleRequest(
            startHour = staffSchedule.startHour,
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            type = StaffScheduleType.HAD,
            providerUnitId = null,
            alsoDigital = true,
            carveOutDays = 1,
        )

        val expectedResponse = StaffScheduleResponseConverter.convert(staffSchedule)

        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#create staff availability with provider should return 200 OK with newly created staff schedule`() {
        coEvery {
            staffScheduleService.create(
                staff.id,
                startHour.fromSaoPauloToUTCTimeZone(),
                untilHour.fromSaoPauloToUTCTimeZone(),
                weekDay,
                type = StaffScheduleType.HAD,
                providerUnitId = providerUnitId,
                alsoDigital = true,
            )
        } returns staffSchedule.success()

        coEvery {
            staffScheduleService.update(
                staffSchedule.copy(
                    startHour = startHour.fromSaoPauloToUTCTimeZone(),
                    untilHour = untilHour.fromSaoPauloToUTCTimeZone(),
                    type = StaffScheduleType.HAD,
                )
            )
        } returns staffSchedule.success()

        coEvery {
            providerUnitService.getByIds(listOf(providerUnitId), false)
        } returns listOf(providerUnit).success()

        val request = StaffScheduleRequest(
            startHour = staffSchedule.startHour,
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            type = StaffScheduleType.HAD,
            providerUnitId = providerUnitId,
            alsoDigital = true,
        )

        val expectedResponse = StaffScheduleResponseConverter.convert(staffSchedule).toProviderResponse(mapOf(providerUnitId to providerUnit))

        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Ignore
    @Test
    fun `#create staff availability should return 400 when there is overlap`() {
        coEvery {
            staffScheduleService.create(
                staff.id,
                startHour.fromSaoPauloToUTCTimeZone(),
                untilHour.fromSaoPauloToUTCTimeZone(),
                weekDay,
                providerUnitId,
                alsoDigital
            )
        } returns StaffScheduleOverlapException(
            staffSchedule.startHour.toString(),
            staffSchedule.untilHour.toString(),
            staffSchedule.weekDay,
            staffSchedule.startDate,
            staffSchedule.expirationDate
        ).failure()

        coEvery {
            staffScheduleService.update(
                staffSchedule.copy(
                    startHour = startHour.fromSaoPauloToUTCTimeZone(),
                    untilHour = untilHour.fromSaoPauloToUTCTimeZone(),
                )
            )
        } returns staffSchedule.success()

        val request = StaffScheduleRequest(
            startHour = staffSchedule.startHour,
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            providerUnitId = staffSchedule.providerUnitId,
            alsoDigital = staffSchedule.alsoDigital,
        )
        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isBadRequest()
            }
        }
    }

    @Test
    fun `#create staff availability should return 400 when time range is invalid`() {
        val request = StaffScheduleRequest(
            startHour = staffSchedule.untilHour.plusHours(1),
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            providerUnitId = staffSchedule.providerUnitId,
            alsoDigital = staffSchedule.alsoDigital,
        )
        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isBadRequest()
            }
        }

        coVerify { staffScheduleService wasNot called }
    }

    @Test
    fun `#update staffSchedule returns 200 OK with new data`() {
        val staffSchedule = staffSchedules.first().copy(providerUnitId = providerUnitId)

        val request = StaffScheduleRequest(
            startHour = staffSchedule.startHour.plusHours(1),
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            providerUnitId = staffSchedule.providerUnitId,
            alsoDigital = staffSchedule.alsoDigital,
            carveOutDays = 1
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = WeekDay.valueOf(request.weekDay),
            carveOutHours = 24
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns updatedStaffSchedule.success()
        coEvery { staffScheduleService.update(updatedStaffSchedule) } returns updatedStaffSchedule.success()
        coEvery { providerUnitService.getByIds(listOf(providerUnitId), false) } returns listOf(providerUnit).success()

        val expectedResponse = StaffScheduleProviderResponse(
            staffId = staffSchedule.staffId,
            providerUnitId = staffSchedule.providerUnitId,
            weekDay = staffSchedule.weekDay,
            startHour = request.startHour,
            untilHour = request.untilHour,
            carveOutDays = request.carveOutDays,
            id = staffSchedule.id,
            type = staffSchedule.type,
            alsoDigital = staffSchedule.alsoDigital,
            providerUnitName = providerUnit.name,
            exceptionEventTypes = staffSchedule.exceptionEventTypes
        )

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 200 OK with new data updating carve out hours`() = mockRangeUuidAndDateTime { uuid, _ ->
        val token = RangeUUID.generate().toString()
        val staffSchedule = staffSchedules.first().copy(carveOutHours = null)

        val request = StaffScheduleRequest(
            startHour = staffSchedule.startHour.plusHours(1),
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            providerUnitId = staffSchedule.providerUnitId,
            alsoDigital = staffSchedule.alsoDigital,
            carveOutDays = 1
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = WeekDay.valueOf(request.weekDay),
            carveOutHours = 24
        )

        val eventPayload = DataEventPayload(
            staffId = staffSchedule.staffId.toString(),
            sessionId = uuid.toString(),
            action = "update_carve_out",
            namespace = "health.staff_schedule",
            type = DataEventType.EVENT.description,
            properties = mapOf(
                "carve_out_enabled" to true.toString(),
                "carve_out_hours" to updatedStaffSchedule.carveOutHours.toString(),
            ),
            version = "1.0"
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns staffSchedule.success()
        coEvery { staffScheduleService.update(updatedStaffSchedule) } returns updatedStaffSchedule.success()
        coEvery {
            dataEventClient.sendEventAsync(
                url = ServiceConfig.dataEventApiUrl,
                payload = eventPayload
            )
        } returns mockk()

        val expectedResponse = StaffScheduleResponseConverter.convert(updatedStaffSchedule)

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 400 when there is staff schedule overlap`() {
        val staffSchedule = staffSchedules.first()

        val request = StaffScheduleRequest(
            startHour = staffSchedule.startHour.plusHours(1),
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            providerUnitId = staffSchedule.providerUnitId,
            alsoDigital = staffSchedule.alsoDigital,
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = WeekDay.valueOf(request.weekDay),
            carveOutHours = null
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns updatedStaffSchedule.success()
        coEvery { staffScheduleService.update(updatedStaffSchedule) } returns StaffScheduleOverlapException(
            staffSchedule.startHour.toString(),
            staffSchedule.untilHour.toString(),
            staffSchedule.weekDay,
            staffSchedule.startDate,
            staffSchedule.expirationDate
        ).failure()

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isBadRequest()
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 400 when time range is invalid`() {
        val staffSchedule = staffSchedules.first()

        val request = StaffScheduleRequest(
            startHour = staffSchedule.untilHour,
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            providerUnitId = staffSchedule.providerUnitId,
            alsoDigital = staffSchedule.alsoDigital,
        )

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isBadRequest()
            }
        }

        coVerify { staffScheduleService wasNot called }
    }

    @Test
    fun `#delete staffSchedule returns 200 OK with deleted staff schedule`() {
        val staffSchedule = staffSchedules.first()

        val updatedStaffSchedule = staffSchedule.copy(
            status = StaffScheduleStatus.INACTIVE
        )

        coEvery {
            staffScheduleService.delete(
                staffSchedule.id,
                staff.id,
            )
        } returns updatedStaffSchedule.success()

        val expectedResponse = StaffScheduleResponseConverter.convert(updatedStaffSchedule)

        authenticatedAs(token, staffForAuth) {
            delete("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Ignore
    @Test
    fun `#bulkUpsert should add new staff schedules and update existing ones`() {
        val staffSchedule = staffSchedules.first()
        val startHour = LocalTime.now()
        val untilHour = startHour.plusHours(1)
        val staffScheduleToCreate = TestModelFactory.buildStaffSchedule(
            startHour = startHour,
            untilHour = untilHour
        )

        val request = BulkStaffScheduleRequest(
            listOf(
                StaffScheduleRequest(
                    id = staffSchedule.id,
                    startHour = staffSchedule.startHour,
                    untilHour = staffSchedule.untilHour,
                    weekDay = staffSchedule.weekDay.toString(),
                    providerUnitId = staffSchedule.providerUnitId,
                    alsoDigital = staffSchedule.alsoDigital,
                ),
                StaffScheduleRequest(
                    startHour = staffScheduleToCreate.startHour,
                    untilHour = staffScheduleToCreate.untilHour,
                    weekDay = staffScheduleToCreate.weekDay.toString(),
                    providerUnitId = staffSchedule.providerUnitId,
                    alsoDigital = staffSchedule.alsoDigital,
                ),
                StaffScheduleRequest(
                    startHour = staffScheduleToCreate.untilHour.plusHours(1),
                    untilHour = staffScheduleToCreate.untilHour,
                    weekDay = staffScheduleToCreate.weekDay.toString(),
                    providerUnitId = staffSchedule.providerUnitId,
                    alsoDigital = staffSchedule.alsoDigital,
                ),
                StaffScheduleRequest(
                    startHour = staffScheduleToCreate.untilHour,
                    untilHour = staffScheduleToCreate.untilHour,
                    weekDay = staffScheduleToCreate.weekDay.toString(),
                    providerUnitId = staffSchedule.providerUnitId,
                    alsoDigital = staffSchedule.alsoDigital,
                )
            )
        )

        coEvery {
            staffScheduleService.addList(match {
                it.first().weekDay == staffScheduleToCreate.weekDay &&
                        it.first().type == StaffScheduleType.HAD &&
                        it.first().startHour == staffScheduleToCreate.startHour.fromSaoPauloToUTCTimeZone() &&
                        it.first().untilHour == staffScheduleToCreate.untilHour.fromSaoPauloToUTCTimeZone()
            })
        } returns listOf(staffScheduleToCreate).success()

        coEvery {
            staffScheduleService.updateList(match {
                it.first().weekDay == staffSchedule.weekDay &&
                        it.first().type == StaffScheduleType.HAD &&
                        it.first().startHour == staffSchedule.startHour.fromSaoPauloToUTCTimeZone() &&
                        it.first().untilHour == staffSchedule.untilHour.fromSaoPauloToUTCTimeZone()
            })
        } returns listOf(staffSchedule).success()

        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedules", request) { response ->
                val content: BulkStaffScheduleUpsertResponse = response.bodyAsJson()
                val staffSchedule01 = StaffScheduleResponseConverter.convert(staffSchedule)
                val staffSchedule02 = StaffScheduleResponseConverter.convert(staffScheduleToCreate)
                assertThat(content.successfulUpsertedStaffSchedules).containsExactlyInAnyOrder(
                    staffSchedule01,
                    staffSchedule02
                )
            }
        }
    }

    @Test
    fun `#deleteAll should delete all staff schedules from staff`() {
        val staffSchedule = staffSchedules.first()

        coEvery {
            staffScheduleService.getStaffSchedules(staff.id)
        } returns listOf(staffSchedule).success()

        coEvery {
            staffScheduleService.updateList(match {
                it.first().weekDay == staffSchedule.weekDay &&
                        it.first().startHour == staffSchedule.startHour &&
                        it.first().untilHour == staffSchedule.untilHour &&
                        it.first().status == StaffScheduleStatus.INACTIVE
            })
        } returns listOf(staffSchedule).success()

        val expectedResponse = listOf(
            StaffScheduleResponseConverter.convert(staffSchedule)
        )

        authenticatedAs(token, staffForAuth) {
            delete("${apiVersion}/staff/${staff.id}/schedules") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#disassociateStaffScheduleFromEventType should call method to disassociate staff schedule from event type`() {
        val staffSchedule = staffSchedules.first()
        val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()

        coEvery {
            staffScheduleService.updateStaffScheduleEventTypeAssociation(
                staffScheduleId = staffSchedule.id,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                isDisassociation = true,
            )
        } returns staffSchedule.success()

        coEvery {
            appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType.success()

        coEvery {
            appointmentScheduleEventTypeService.update(
                appointmentScheduleEventType.copy(lastUpdatedBy = staff.id)
            )
        } returns appointmentScheduleEventType.success()

        authenticatedAs(token, staffForAuth) {
            put(
                "${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}/event_type_association",
                StaffScheduleEventTypeAssociationRequest(
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    isDisassociation = true,
                ),
            ) {
                assertThat(it).isOKWithData(staffSchedule)
            }
        }
    }

    @Test
    fun `#getStaffScheduleEditOptions should return staff schedules enum options`(){
        authenticatedAs(token, staffForAuth) {
            get(
                "${apiVersion}/staff_schedule/edit_options",
            ) {
                assertThat(it).isOKWithData(StaffScheduleEditOption.toResponseList())
            }
        }
    }

}

