package br.com.alice.api.scheduler.controllers.v2

import br.com.alice.api.scheduler.ControllerTestHelper
import br.com.alice.api.scheduler.ServiceConfig
import br.com.alice.api.scheduler.controllers.model.EditStaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleResponse
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesLastUpdatedBy
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesResponse
import br.com.alice.api.scheduler.controllers.model.toProviderResponse
import br.com.alice.api.scheduler.converters.StaffScheduleResponseConverter
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DataEventPayload
import br.com.alice.common.client.DataEventType
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.fromUTCToSaoPauloTimeZone
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toLocalTime
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.models.Staff
import br.com.alice.common.service.serialization.isoDateGson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.model.StaffScheduleEditOption
import br.com.alice.schedule.model.exceptions.StaffScheduleOverlapException
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.util.date.WeekDay
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class StaffScheduleV2ControllerTest : ControllerTestHelper() {

    private val token = RangeUUID.generate().toString()
    private val apiVersion = "v2"
    private val staffScheduleService: StaffScheduleService = mockk()
    private val staffService: StaffService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val dataEventClient: DataEventClient = mockk()

    private val startHour = "09:00".toLocalTime()
    private val untilHour = "12:00".toLocalTime()
    private val weekDay = WeekDay.valueOf("WEDNESDAY")
    private val providerUnitId = RangeUUID.generate()
    private val selectedDate = LocalDate.of(2025, 2, 5)
    private val appointmentScheduleEventTypeId = RangeUUID.generate()

    private val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId)
    private val staffForAuth = staff.convertTo(Staff::class)
    val staffSchedule = TestModelFactory.buildStaffSchedule(
        staffId = staff.id,
        carveOutHours = 24,
        alsoDigital = true,
        startHour = startHour.fromSaoPauloToUTCTimeZone(),
        untilHour = untilHour.fromSaoPauloToUTCTimeZone(),
        weekDay = weekDay,
        lastUpdatedBy = staff.id,
        exceptionEventTypes = listOf(appointmentScheduleEventTypeId),
    )
    val staffSchedules = listOf(staffSchedule)
    private val staffSchedulesValidForEventType = listOf(
        TestModelFactory.buildStaffSchedule(
            staff.id,
            startHour,
            untilHour,
            weekDay,
            lastUpdatedBy = staff.id,
        )
    )


    @BeforeTest
    override fun setup() {
        super.setup()

        this.module.single { staffScheduleService }
        this.module.single { staffService }
        this.module.single { providerUnitService }
        this.module.single { dataEventClient }
        this.module.single { StaffScheduleV2Controller(get(), get(), get(), get()) }
    }

    @Test
    fun `#get all staff schedules should return 200 OK with staff schedules when it exists`() {
        coEvery { staffScheduleService.getStaffSchedules(staffSchedule.staffId) } returns staffSchedules.success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        val staffSchedulesResponse = staffSchedules.map { it.convertTo(StaffScheduleResponse::class) }.map {
            it.copy(
                startHour = it.startHour.fromUTCToSaoPauloTimeZone(),
                untilHour = it.untilHour.fromUTCToSaoPauloTimeZone(),
                carveOutDays = 1,
            )
        }.sortedBy { it.startHour }

        val staffScheduleProviderResponse = staffSchedulesResponse.map { it.toProviderResponse(emptyMap()) }

        val expectedResponse = StaffSchedulesResponse(
            staffSchedules = staffScheduleProviderResponse,
            updatedAt = staffSchedule.updatedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
            lastUpdatedBy = StaffSchedulesLastUpdatedBy(
                staff.id,
                staff.fullName,
            )
        )

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedules") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#get all staff schedules should return 200 OK with staff schedules with startDate and endDate when it exists`() {
        val startDate = LocalDate.now()
        val endDate = LocalDate.now()
        coEvery { staffScheduleService.getStaffSchedules(staffSchedule.staffId, startDate, endDate) } returns staffSchedules.success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        val staffSchedulesResponse = staffSchedules.map { it.convertTo(StaffScheduleResponse::class) }.map {
            it.copy(
                startHour = it.startHour.fromUTCToSaoPauloTimeZone(),
                untilHour = it.untilHour.fromUTCToSaoPauloTimeZone(),
                carveOutDays = 1,
            )
        }.sortedBy { it.startHour }

        val staffScheduleProviderResponse = staffSchedulesResponse.map { it.toProviderResponse(emptyMap()) }

        val expectedResponse = StaffSchedulesResponse(
            staffSchedules = staffScheduleProviderResponse,
            updatedAt = staffSchedule.updatedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
            lastUpdatedBy = StaffSchedulesLastUpdatedBy(
                staff.id,
                staff.fullName,
            )
        )

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedules?startDate=$startDate&endDate=$endDate") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#get all staff schedules should return 200 OK with staff schedules that are valid for event type when there is the query param`() {
        val startDate = LocalDate.now()
        val endDate = LocalDate.now()
        coEvery {
            staffScheduleService.getStaffSchedules(staffSchedule.staffId, startDate, endDate)
        } returns staffSchedules.plus(staffSchedulesValidForEventType).success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        val staffSchedulesResponse =
            staffSchedulesValidForEventType.map { it.convertTo(StaffScheduleResponse::class) }.map {
                it.copy(
                    startHour = it.startHour.fromUTCToSaoPauloTimeZone(),
                    untilHour = it.untilHour.fromUTCToSaoPauloTimeZone(),
                )
            }.sortedBy { it.startHour }

        val staffScheduleProviderResponse = staffSchedulesResponse.map { it.toProviderResponse(emptyMap()) }

        val expectedResponse = StaffSchedulesResponse(
            staffSchedules = staffScheduleProviderResponse,
            updatedAt = staffSchedule.updatedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
            lastUpdatedBy = StaffSchedulesLastUpdatedBy(
                staff.id,
                staff.fullName,
            )
        )

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedules?appointmentScheduleEventTypeId=${appointmentScheduleEventTypeId}&startDate=$startDate&endDate=$endDate") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }
    @Test
    fun `#get all staff schedules with provider unit name should return 200 OK with staff schedules when it exists`() {
        val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId)

        val staffScheduleProvider = staffSchedules[0].copy(providerUnitId = providerUnitId)

        coEvery { staffScheduleService.getStaffSchedules(staffSchedule.staffId) } returns listOf(staffScheduleProvider).success()

        coEvery { staffService.get(staff.id) } returns staff.success()

        coEvery { providerUnitService.getByIds(listOf(providerUnitId), false) } returns listOf(providerUnit).success()

        val staffSchedulesResponse = listOf(staffScheduleProvider).map { it.convertTo(StaffScheduleResponse::class) }.map {
            it.copy(
                startHour = it.startHour.fromUTCToSaoPauloTimeZone(),
                untilHour = it.untilHour.fromUTCToSaoPauloTimeZone(),
                carveOutDays = 1
            )
        }.sortedBy { it.startHour }

        val staffScheduleProviderResponse = staffSchedulesResponse.map { it.toProviderResponse(mapOf(providerUnitId to providerUnit)) }

        val expectedResponse = StaffSchedulesResponse(
            staffSchedules = staffScheduleProviderResponse,
            updatedAt = staffSchedule.updatedAt.toSaoPauloTimeZone().toBrazilianDateTimeFormat(),
            lastUpdatedBy = StaffSchedulesLastUpdatedBy(
                staff.id,
                staff.fullName,
            ),
        )

        authenticatedAs(token, staffForAuth) {
            get("${apiVersion}/staff/${staff.id}/schedules") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#create staff availability should return 200 OK with newly created staff schedule`() =
        mockRangeUuidAndDateTime { uuid, date ->
            val staffSchedule = staffSchedule.copy(createdAt = date, updatedAt = date, id = uuid, lastUpdatedBy = null, exceptionEventTypes = emptyList())
            val request = EditStaffScheduleRequest(
                staffSchedule = StaffScheduleRequest(
                    startHour = startHour,
                    untilHour = untilHour,
                    weekDay = weekDay.toString(),
                    type = staffSchedule.type,
                    providerUnitId = null,
                    alsoDigital = true,
                    carveOutDays = 1,
                ),
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
            )
            coEvery {
                staffScheduleService.createV2(
                    staff.id,
                    staffSchedule,
                    selectedDate,
                    StaffScheduleEditOption.THIS_SLOT_AND_NEXT
                )
            } returns staffSchedule.success()


            val expectedResponse = StaffScheduleResponseConverter.convert(staffSchedule)

            authenticatedAs(token, staffForAuth) {
                post("${apiVersion}/staff/${staff.id}/schedule", request) {
                    assertThat(it).isOKWithData(expectedResponse)
                }
            }
        }

    @Test
    fun `#create staff availability with provider should return 200 OK with newly created staff schedule`() = mockRangeUuidAndDateTime { uuid, date ->
        val staffSchedule = staffSchedule.copy(
            createdAt = date,
            updatedAt = date,
            id = uuid,
            providerUnitId = providerUnitId,
            lastUpdatedBy = null,
            exceptionEventTypes = emptyList()
        )
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = startHour,
                untilHour = untilHour,
                weekDay = weekDay.toString(),
                type = staffSchedule.type,
                alsoDigital = true,
                carveOutDays = 1,
                providerUnitId = providerUnitId,
            ),
            selectedDate = selectedDate,
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )
        coEvery {
            staffScheduleService.createV2(
                staff.id,
                staffSchedule,
                selectedDate,
                StaffScheduleEditOption.THIS_SLOT_AND_NEXT
            )
        } returns staffSchedule.success()

        coEvery {
            providerUnitService.getByIds(listOf(providerUnitId), false)
        } returns listOf(providerUnit).success()

        val expectedResponse = StaffScheduleResponseConverter.convert(staffSchedule)
            .toProviderResponse(mapOf(providerUnitId to providerUnit))

        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#create staff availability should return 400 when time range is invalid`() = mockRangeUuidAndDateTime { uuid, date ->
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.untilHour.plusHours(1),
                untilHour = staffSchedule.untilHour,
                weekDay = weekDay.toString(),
                type = staffSchedule.type,
                alsoDigital = true,
                carveOutDays = 1,
                providerUnitId = providerUnitId,
            ),
            selectedDate = selectedDate,
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )
        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isBadRequest()
            }
        }

        coVerify { staffScheduleService wasNot called }
    }

    @Test
    fun `#create staff availability should return error when there is overlap`() = mockRangeUuidAndDateTime { uuid, date ->
        val staffSchedule = staffSchedule.copy(
            createdAt = date,
            updatedAt = date,
            id = uuid,
            providerUnitId = providerUnitId,
            lastUpdatedBy = null,
            exceptionEventTypes = emptyList()
        )
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = startHour,
                untilHour = untilHour,
                weekDay = weekDay.toString(),
                type = staffSchedule.type,
                alsoDigital = true,
                carveOutDays = 1,
                providerUnitId = providerUnitId,
            ),
            selectedDate = selectedDate,
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )
        coEvery {
            staffScheduleService.createV2(
                staff.id,
                staffSchedule,
                selectedDate,
                StaffScheduleEditOption.THIS_SLOT_AND_NEXT
            )
        } returns StaffScheduleOverlapException(
            staffSchedule.startHour.toString(),
            staffSchedule.untilHour.toString(),
            staffSchedule.weekDay,
            staffSchedule.startDate,
            staffSchedule.expirationDate
        ).failure()

        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isBadRequest()
                val content: ErrorResponse = isoDateGson.fromJson(it.bodyAsText())
                Assertions.assertThat(content.code).isEqualTo("staff_schedule_overlap")
                Assertions.assertThat(content.message).isEqualTo("Já existe um slot cadastrado que coincide com o horário inserido. Início: 12:00, Fim: 15:00, Dia da semana: Quarta-feira, Data de início: -, Data de expiração: -")
            }
        }
    }

    @Test
    fun `#create staff availability should return generic error when occur not identify error`() = mockRangeUuidAndDateTime { uuid, date ->
        val staffSchedule = staffSchedule.copy(
            createdAt = date,
            updatedAt = date,
            id = uuid,
            providerUnitId = providerUnitId,
            lastUpdatedBy = null,
            exceptionEventTypes = emptyList()
        )
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = startHour,
                untilHour = untilHour,
                weekDay = weekDay.toString(),
                type = staffSchedule.type,
                alsoDigital = true,
                carveOutDays = 1,
                providerUnitId = providerUnitId,
            ),
            selectedDate = selectedDate,
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )
        coEvery {
            staffScheduleService.createV2(
                staff.id,
                staffSchedule,
                selectedDate,
                StaffScheduleEditOption.THIS_SLOT_AND_NEXT
            )
        } returns Exception().failure()

        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isBadRequest()
                val content: ErrorResponse = isoDateGson.fromJson(it.bodyAsText())
                Assertions.assertThat(content.code).isEqualTo("staff_schedule_failed_on_create")
                Assertions.assertThat(content.message).isEqualTo("An error occurred while creating")
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 200 OK with new data`() {
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.startHour.plusHours(1),
                untilHour = staffSchedule.untilHour,
                weekDay = staffSchedule.weekDay.toString(),
                providerUnitId = staffSchedule.providerUnitId,
                alsoDigital = staffSchedule.alsoDigital,
                carveOutDays = 1,
                id = staffSchedule.id,
            ),
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
            selectedDate = selectedDate
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.staffSchedule.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.staffSchedule.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = staffSchedule.weekDay,
            carveOutHours = 24,
            providerUnitId = staffSchedule.providerUnitId
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns staffSchedule.success()
        coEvery { staffScheduleService.updateV2(updatedStaffSchedule, selectedDate, selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT, staffForAuth.id) } returns updatedStaffSchedule.success()
        coEvery { providerUnitService.getByIds(listOf(providerUnitId), false) } returns listOf(providerUnit).success()

        val expectedResponse = StaffScheduleResponseConverter.convert(updatedStaffSchedule)
            .toProviderResponse(mapOf(providerUnitId to providerUnit))

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 200 OK with new data updating carve out hours`() = mockRangeUuidAndDateTime { uuid, _ ->
        val token = RangeUUID.generate().toString()
        val staffSchedule = staffSchedule.copy(carveOutHours = null)

        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.startHour.plusHours(1),
                untilHour = staffSchedule.untilHour,
                weekDay = staffSchedule.weekDay.toString(),
                providerUnitId = staffSchedule.providerUnitId,
                alsoDigital = staffSchedule.alsoDigital,
                carveOutDays = 1,
                id = staffSchedule.id,
            ),
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
            selectedDate = selectedDate
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.staffSchedule.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.staffSchedule.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = staffSchedule.weekDay,
            carveOutHours = 24,
            providerUnitId = staffSchedule.providerUnitId
        )

        val eventPayload = DataEventPayload(
            staffId = staffSchedule.staffId.toString(),
            sessionId = uuid.toString(),
            action = "update_carve_out",
            namespace = "health.staff_schedule",
            type = DataEventType.EVENT.description,
            properties = mapOf(
                "carve_out_enabled" to true.toString(),
                "carve_out_hours" to updatedStaffSchedule.carveOutHours.toString(),
            ),
            version = "1.0"
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns staffSchedule.success()
        coEvery { staffScheduleService.updateV2(updatedStaffSchedule, selectedDate, selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT, staffForAuth.id) } returns updatedStaffSchedule.success()
        coEvery {
            dataEventClient.sendEventAsync(
                url = ServiceConfig.dataEventApiUrl,
                payload = eventPayload
            )
        } returns mockk()

        val expectedResponse = StaffScheduleResponseConverter.convert(updatedStaffSchedule)

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 400 when time range is invalid`() {
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.untilHour,
                untilHour = staffSchedule.untilHour,
                weekDay = staffSchedule.weekDay.toString(),
                providerUnitId = staffSchedule.providerUnitId,
                alsoDigital = staffSchedule.alsoDigital,
                carveOutDays = 1,
                id = staffSchedule.id,
            ),
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
            selectedDate = selectedDate
        )

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isBadRequest()
            }
        }

        coVerify { staffScheduleService wasNot called }
    }

    @Test
    fun `#update staff availability should return error when there is overlap`() {
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.startHour,
                untilHour = staffSchedule.untilHour,
                weekDay = staffSchedule.weekDay.toString(),
                providerUnitId = staffSchedule.providerUnitId,
                alsoDigital = staffSchedule.alsoDigital,
                carveOutDays = 1,
                id = staffSchedule.id,
            ),
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
            selectedDate = selectedDate
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.staffSchedule.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.staffSchedule.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = staffSchedule.weekDay,
            carveOutHours = 24,
            providerUnitId = staffSchedule.providerUnitId
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns staffSchedule.success()
        coEvery { staffScheduleService.updateV2(updatedStaffSchedule, selectedDate, selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT, staffForAuth.id) }  returns StaffScheduleOverlapException(
            staffSchedule.startHour.toString(),
            staffSchedule.untilHour.toString(),
            staffSchedule.weekDay,
            staffSchedule.startDate,
            staffSchedule.expirationDate
        ).failure()

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isBadRequest()
                val content: ErrorResponse = isoDateGson.fromJson(it.bodyAsText())
                Assertions.assertThat(content.code).isEqualTo("staff_schedule_overlap")
                Assertions.assertThat(content.message).isEqualTo("Já existe um slot cadastrado que coincide com o horário inserido. Início: 12:00, Fim: 15:00, Dia da semana: Quarta-feira, Data de início: -, Data de expiração: -")
            }
        }
    }

    @Test
    fun `#delete staffSchedule returns 200 OK with deleted staff schedule`() {
        val selectedDate = LocalDate.of(2025,6,6)
        val selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        val updatedStaffSchedule = staffSchedule.copy(
            expirationDate = LocalDate.of(2025,6,5)
        )

        coEvery {
            staffScheduleService.deleteV2(
                staffSchedule.id,
                staff.id,
                selectedDate,
                selectedOption
            )
        } returns updatedStaffSchedule.success()

        val expectedResponse = StaffScheduleResponseConverter.convert(updatedStaffSchedule)

        authenticatedAs(token, staffForAuth) {
            delete("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}?selectedDate=${selectedDate}&selectedOption=${selectedOption}") {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

}

