package br.com.alice.api.scheduler.controllers.v2

import br.com.alice.api.scheduler.ServiceConfig
import br.com.alice.api.scheduler.controllers.StaffController
import br.com.alice.api.scheduler.controllers.model.EditStaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleResponse
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesLastUpdatedBy
import br.com.alice.api.scheduler.controllers.model.StaffSchedulesResponse
import br.com.alice.api.scheduler.controllers.model.toProviderResponse
import br.com.alice.api.scheduler.converters.StaffScheduleRequestConverter
import br.com.alice.api.scheduler.converters.StaffScheduleResponseConverter
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DataEventPayload
import br.com.alice.common.client.DataEventType
import br.com.alice.common.coFoldResponse
import br.com.alice.common.core.extensions.toBrazilianDateTimeFormat
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmapNotNull
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.model.StaffScheduleEditOption
import br.com.alice.schedule.model.exceptions.ErrorsCode
import br.com.alice.schedule.model.exceptions.StaffScheduleInvalidException
import br.com.alice.schedule.model.exceptions.StaffScheduleOverlapException
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.time.LocalTime
import java.util.UUID
import kotlin.collections.map
import kotlin.reflect.KClass

class StaffScheduleV2Controller(
    private val staffService: StaffService,
    private val staffScheduleService: StaffScheduleService,
    private val providerUnitService: ProviderUnitService,
    private val dataEventClient: DataEventClient,
) : StaffController(staffService) {

    suspend fun getAll(staffId: String, queryParams: Parameters): Response =
        span("StaffScheduleController::getAll") { span ->
            span.setAttribute("staff_id", staffId)
            span.setAttribute("requester", currentStaffId())
            val appointmentScheduleEventTypeId = queryParams["appointmentScheduleEventTypeId"]
            span.setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId ?: "")
            val startDate = queryParams["startDate"]?.toLocalDate()
            span.setAttribute("start_date", startDate ?: "")
            val endDate = queryParams["endDate"]?.toLocalDate()
            span.setAttribute("end_date", endDate ?: "")

            var lastStaffScheduleUpdated: StaffSchedule? = null

            staffScheduleService.getStaffSchedules(staffId.toUUID(), startDate, endDate)
                .map { staffSchedules ->
                    lastStaffScheduleUpdated = staffSchedules.maxByOrNull { it.updatedAt }
                    staffSchedules.pmapNotNull { staffSchedule ->
                        if (isValidEventForStaffSchedule(appointmentScheduleEventTypeId, staffSchedule))
                            StaffScheduleResponseConverter.convert(staffSchedule)
                        else null
                    }
                }
                .map { staffSchedules -> staffSchedules.sortedBy { staffSchedule -> staffSchedule.startHour } }
                .map { staffSchedules ->
                    val providerUnitMap = getProviderUnitName(staffSchedules)

                    staffSchedules.map { staffSchedule ->
                        staffSchedule.toProviderResponse(providerUnitMap)
                    }
                }
                .map { staffSchedules ->
                    val updatedAt = lastStaffScheduleUpdated?.updatedAt
                    val lastUpdatedBy = lastStaffScheduleUpdated?.lastUpdatedBy
                    val lastUpdatedByStaff = lastUpdatedBy?.let { staffService.get(it).get() }

                    StaffSchedulesResponse(
                        staffSchedules = staffSchedules,
                        updatedAt = updatedAt?.toSaoPauloTimeZone()?.toBrazilianDateTimeFormat(),
                        lastUpdatedBy = lastUpdatedByStaff?.let {
                            StaffSchedulesLastUpdatedBy(
                                id = it.id,
                                name = it.fullName,
                            )
                        }
                    )
                }
                .foldResponse()
        }

    suspend fun create(staffId: String, request: EditStaffScheduleRequest): Response {
        logger.info(
            "StaffScheduleController::create",
            "staff_id" to staffId,
            "request" to request,
            "requester" to currentStaffId()
        )

        if (isInvalidTimeRange(request.staffSchedule.startHour, request.staffSchedule.untilHour)) {
            val startHour = request.staffSchedule.startHour.toString()
            val untilHour = request.staffSchedule.untilHour.toString()
            return Response(
                status = HttpStatusCode.BadRequest,
                message = "The end time ($untilHour) of a staff schedule must be after the start time ($startHour)"
            )
        }

        val staffSchedule = StaffScheduleRequestConverter.convert(request.staffSchedule, staffId)

        return staffScheduleService.createV2(
            staffId.toUUID(),
            staffSchedule,
            request.selectedDate,
            request.selectedOption,
        ).coFoldResponse(
            {
                val staffScheduleResponse = StaffScheduleResponseConverter.convert(it)
                val providerUnitMap = getProviderUnitName(listOf(staffScheduleResponse))
                staffScheduleResponse.toProviderResponse(providerUnitMap)
            },
            *mapFailures.plus(
                Exception::class to {
                    ErrorResponse(
                        "staff_schedule_failed_on_create",
                        it.message ?: "An error occurred while creating"
                    )
                })
        )
    }

    suspend fun update(id: String, request: EditStaffScheduleRequest): Response {
        logger.info(
            "StaffScheduleController::update",
            "staff_schedule_id" to id,
            "request" to request,
            "requester" to currentStaffId()
        )

        if (isInvalidTimeRange(request.staffSchedule.startHour, request.staffSchedule.untilHour)) {
            val startHour = request.staffSchedule.startHour.toString()
            val untilHour = request.staffSchedule.untilHour.toString()
            return Response(
                status = HttpStatusCode.BadRequest,
                message = "The end time ($untilHour) of a staff schedule must be after the start time ($startHour)"
            )
        }

        return staffScheduleService.getStaffScheduleById(id.toUUID())
            .flatMap {
                val staffSchedule = StaffScheduleRequestConverter.convert(request.staffSchedule, it.staffId.toString())
                sendEventIfCarveoutIsChanged(it, staffSchedule)
                staffScheduleService.updateV2(
                    it.copy(
                        startHour = staffSchedule.startHour,
                        untilHour = staffSchedule.untilHour,
                        carveOutHours = staffSchedule.carveOutHours,
                        providerUnitId = staffSchedule.providerUnitId
                    ),
                    request.selectedDate,
                    request.selectedOption,
                    currentStaffId()
                )
            }.coFoldResponse({
                val staffScheduleResponse = StaffScheduleResponseConverter.convert(it)
                val providerUnitMap = getProviderUnitName(listOf(staffScheduleResponse))
                staffScheduleResponse.toProviderResponse(providerUnitMap)
            },
                *mapFailures.plus(
                    Exception::class to {
                        ErrorResponse(
                            "staff_schedule_failed_on_update",
                            it.message ?: "An error occurred while updating"
                        )
                    })
            )
    }

    suspend fun delete(id: UUID, queryParams: Parameters): Response {
        logger.info(
            "StaffScheduleController::delete",
            "staff_schedule_id" to id,
            "requester" to currentStaffId()
        )
        val selectedDate = queryParams["selectedDate"]?.toLocalDate()
        val selectedOption = queryParams["selectedOption"]?.let { StaffScheduleEditOption.valueOf(it) }

        if (selectedDate == null || selectedOption == null) {
            return Response(
                status = HttpStatusCode.BadRequest,
                message = "Selected date and selected option must be provided"
            )
        }
        return staffScheduleService.deleteV2(id, currentStaffId(), selectedDate, selectedOption)
            .map { StaffScheduleResponseConverter.convert(it) }
            .thenError { throwable ->
                logger.error(
                    "StaffScheduleController::delete error",
                    throwable
                )
            }
            .foldResponse()
    }

    private fun isInvalidTimeRange(startHour: LocalTime, untilHour: LocalTime): Boolean {
        val startHourIsAfterUntilHour = startHour.isAfter(untilHour)
        val startAndUntilHoursAreEqual = startHour == untilHour
        val invalid = startHourIsAfterUntilHour || startAndUntilHoursAreEqual
        logger.info("StaffScheduleController::isInvalidTimeRange the end time ($untilHour) of a staff schedule must be after the start time ($startHour)")
        return invalid
    }

    private suspend fun getProviderUnitName(staffSchedules: List<StaffScheduleResponse>): Map<UUID, ProviderUnit> {
        val providerUnitIds = staffSchedules.mapNotNull { it.providerUnitId }.distinct()
        val providerUnitMap = takeIf { providerUnitIds.isNotEmpty() }?.let {
            providerUnitService.getByIds(providerUnitIds, false).get().associateBy { it.id }
        } ?: emptyMap()

        return providerUnitMap
    }

    private suspend fun isValidEventForStaffSchedule(
        appointmentScheduleEventTypeId: String?,
        staffSchedule: StaffSchedule
    ) = span("isValidEventForStaffSchedule") { span ->
        appointmentScheduleEventTypeId?.let {
            val isExceptionEventForThisStaffSchedule =
                staffSchedule.exceptionEventTypes.contains(appointmentScheduleEventTypeId.toUUID())
            span.setAttribute("staff_schedule", staffSchedule.id)
            span.setAttribute("is_exception_event_for_this_staff_schedule", isExceptionEventForThisStaffSchedule)
            isExceptionEventForThisStaffSchedule.not()
        } ?: true
    }

    private suspend fun sendEventIfCarveoutIsChanged(
        staffScheduleOld: StaffSchedule,
        staffScheduleEdited: StaffSchedule
    ) =
        takeIf { staffScheduleOld.carveOutHours != staffScheduleEdited.carveOutHours }?.let {
            dataEventClient.sendEventAsync(
                url = ServiceConfig.dataEventApiUrl,
                payload = DataEventPayload(
                    staffId = staffScheduleOld.staffId.toString(),
                    sessionId = RangeUUID.generate().toString(),
                    action = "update_carve_out",
                    namespace = "health.staff_schedule",
                    type = DataEventType.EVENT.description,
                    properties = mapOf(
                        "carve_out_enabled" to (staffScheduleEdited.carveOutHours != null).toString(),
                        "carve_out_hours" to staffScheduleEdited.carveOutHours.toString(),
                    ),
                    version = "1.0"
                )
            )
        }

    private val mapFailures: Array<Pair<KClass<*>, suspend (Throwable) -> ErrorResponse>> = arrayOf(
        StaffScheduleOverlapException::class to { ErrorResponse(ErrorsCode.STAFF_SCHEDULE_OVERLAP, it.message.toString()) },
        StaffScheduleInvalidException::class to { ErrorResponse(ErrorsCode.STAFF_SCHEDULE_INVALID, it.message.toString()) },
    )
}
