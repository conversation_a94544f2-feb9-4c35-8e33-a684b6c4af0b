package br.com.alice.ehr.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.HealthMeasurementType
import br.com.alice.data.layer.models.HealthMeasurementTypeModel

object HealthMeasurementTypeConverter : Converter<HealthMeasurementTypeModel, HealthMeasurementType>(
    HealthMeasurementTypeModel::class,
    HealthMeasurementType::class,
)

fun HealthMeasurementType.toModel() = HealthMeasurementTypeConverter.unconvert(this)
fun HealthMeasurementTypeModel.toTransport() = HealthMeasurementTypeConverter.convert(this)
