package br.com.alice.data.layer.helpers

import br.com.alice.authentication.UserType
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.Disease
import br.com.alice.common.Disease.Type.CIAP_2
import br.com.alice.common.Disease.Type.CID_10
import br.com.alice.common.Disease.Type.GOAL
import br.com.alice.common.MvUtil
import br.com.alice.common.MvUtil.Prestador
import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.RangeUUID.PERSON_ID_RANGE
import br.com.alice.common.UUIDv7
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.Role.Companion.healthProfessionalStaffs
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.asMap
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.extensions.money
import br.com.alice.common.logging.logger
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.Sex
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.common.service.data.client.JsonRaw
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.events.FailureReason
import br.com.alice.data.layer.models.*
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.HEALTH_PLAN_TASK
import br.com.alice.data.layer.models.Appointment.ReferenceLinkModel.SCHEDULE
import br.com.alice.data.layer.models.AppointmentType.DEFAULT
import br.com.alice.data.layer.models.FeatureNamespace.EHR
import br.com.alice.data.layer.models.HLActionRecommendation.HLActionRecommendationAction
import br.com.alice.data.layer.models.HLActionRecommendation.HLActionRecommendationModel
import br.com.alice.data.layer.models.HLActionRecommendation.HLActionRecommendationOrigin
import br.com.alice.data.layer.models.HLActionRecommendation.HLActionRecommendationStatus
import br.com.alice.data.layer.models.HLActionRecommendation.HLActionRecommendationType
import br.com.alice.data.layer.models.HealthMeasurementInternalType.*
import br.com.alice.data.layer.models.MedicineUnit.CAPSULE
import br.com.alice.data.layer.models.MemberOnboardingCheckpoint.MemberOnboardingCheckpointStatus
import br.com.alice.data.layer.models.OutcomeRequestScheduling.SchedulingStatus
import br.com.alice.data.layer.models.ServiceScriptNavigationSourceType.CHANNEL
import br.com.alice.data.layer.models.StarredAction.FOLLOW_UP
import br.com.alice.data.layer.services.PersonPII
import com.google.cloud.Timestamp
import io.ktor.http.HttpMethod
import io.ktor.util.date.WeekDay
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.random.Random
import kotlin.reflect.KClass
import kotlin.reflect.KParameter
import kotlin.reflect.full.createType
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.jvm.jvmErasure

object TestModelFactory {

    private val CHAR_POOL: List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')
    private val MIN_DATE_TIME: LocalDateTime = LocalDateTime.of(1970, 1, 1, 0, 0, 0)
    private val MIN_DATE: LocalDate = MIN_DATE_TIME.toLocalDate()

    fun buildPersonInternalReference(
        personId: PersonId = PersonId(),
        internalCode: String = PersonInternalReference.generateInternalCode(),
        channelPersonId: UUID = RangeUUID.generate()
    ) =
        PersonInternalReference(
            personId = personId,
            internalCode = internalCode,
            channelPersonId = channelPersonId
        )

    fun buildPersonHealthEvent(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        staffId: UUID? = RangeUUID.generate(),
        healthcareTeamId: UUID? = null,
        healthcareAdditionalTeamId: UUID? = null,
        healthcareAdditionalTeamType: HealthcareAdditionalTeamType? = null,
        category: PersonHealthEventCategory = PersonHealthEventCategory.APPOINTMENT_IMMERSION,
        title: String = "Task title",
        description: String = "Task description",
        dueDate: LocalDateTime? = null,
        eventDate: LocalDateTime = LocalDateTime.now(),
        status: PersonHealthEventStatus = PersonHealthEventStatus.NOT_STARTED,
        attachments: List<Attachment> = emptyList(),
        referencedModelId: String? = null,
        referencedModelClass: PersonHealthEventReferenceModel? = null,
        createdByStaffId: UUID? = null,
        updatedByStaffIds: List<PersonHealthEventUpdatedBy> = emptyList(),
        finishedAt: LocalDateTime? = null,
        referencedLinks: List<ReferencedLink> = emptyList(),
        automaticFollowUp: Boolean = false,
        automaticFollowUpMessage: String? = null,
        automaticFollowUpContent: AutomaticFollowUpContent? = null,
        priority: PersonHealthEventPriority? = null,
        channelId: String? = null
    ) =
        PersonHealthEvent(
            id = id,
            personId = personId,
            staffId = staffId,
            healthcareTeamId = healthcareTeamId,
            healthcareAdditionalTeamId = healthcareAdditionalTeamId,
            healthcareAdditionalTeamType = healthcareAdditionalTeamType,
            category = category,
            title = title,
            description = description,
            dueDate = dueDate,
            eventDate = eventDate,
            status = status,
            attachments = attachments,
            referencedModelId = referencedModelId,
            referencedModelClass = referencedModelClass,
            createdByStaffId = createdByStaffId,
            finishedAt = finishedAt,
            referencedLinks = referencedLinks,
            updatedByStaffIds = updatedByStaffIds,
            automaticFollowUp = automaticFollowUp,
            automaticFollowUpMessage = automaticFollowUpMessage,
            automaticFollowUpContent = automaticFollowUpContent,
            priority = priority,
            channelId = channelId,
        )

    fun buildTestCode(
        code: String = "98040001",
        description: String = "Hemograma",
        internalDescription: String = "Hemograma Casa Alice",
        priority: Boolean = false,
        sensitiveResult: Boolean = false,
        resultExpirationInDays: Int = 180,
        synonyms: List<String> = listOf("synonyms"),
        preparationId: UUID? = null,
        active: Boolean = true,
    ) =
        TestCode(
            code = code,
            description = description,
            internalDescription = internalDescription,
            priority = priority,
            sensitiveResult = sensitiveResult,
            resultExpirationInDays = resultExpirationInDays,
            synonyms = synonyms,
            preparationId = preparationId,
            active = active,
        )

    fun buildTestPreparation(
        code: String = "PREP_SAN_1",
        title: String = "Preparação para Exame de Sangue",
        instructions: String = "Fazer jejum de 8 horas",
    ) =
        TestPreparation(code = code, title = title, instructions = instructions)

    fun buildProviderTestCode(
        testCodeId: UUID = RangeUUID.generate(),
        providerId: UUID = RangeUUID.generate(),
        providerCode: String = "HEMO",
    ) =
        ProviderTestCode(
            testCodeId = testCodeId,
            providerId = providerId,
            providerCode = providerCode,
            providerBrand = ProviderTestCode.Brand.DB
        )

    fun buildProviderUnitTestCode(
        testCodeId: UUID = RangeUUID.generate(),
        providerUnitId: UUID = RangeUUID.generate(),
        title: String? = null,
    ) =
        ProviderUnitTestCode(
            testCodeId = testCodeId,
            providerUnitId = providerUnitId,
            title = title
        )

    fun buildAddress(
        state: State = State.SP,
        city: String = "São Paulo",
        street: String = "Rua Canada",
        number: String = Random.nextInt(0, 10000).toString(),
        complement: String? = "ap ${Random.nextInt(0, 100)}",
        neighbourhood: String? = "Jardim Europa",
        postalCode: String? = "01448-040",
        lat: Double? = -23.57228442,
        lng: Double? = -46.69272687
    ) = Address(
        state = state,
        city = city,
        street = street,
        number = number,
        complement = complement,
        neighbourhood = neighbourhood,
        postalCode = postalCode,
        lat = lat,
        lng = lng
    )

    fun buildPerson(
        personId: PersonId = PersonId(),
        firstName: String = "José",
        lastName: String = "da Silva",
        nickName: String? = "Zé",
        socialName: String? = null,
        socialFirstName: String? = null,
        socialLastName: String? = null,
        nationalId: String = "609.048.950-68",
        email: String = "<EMAIL>",
        addresses: List<Address> = listOf(buildAddress()),
        sex: Sex? = null,
        gender: Gender? = null,
        dateOfBirth: LocalDateTime? = null,
        tags: List<String>? = null,
        identityDocument: String? = "111111112",
        identityDocumentIssuingBody: String? = "SSP-SP",
        opportunityId: UUID? = null,
        leadId: UUID? = null,
        userType: UserType = UserType.MEMBER,
        phoneNumber: String? = null,
        profilePicture: AliceFile? = null,
        acceptedTermsAt: LocalDateTime? = null,
        pronoun: Pronoun? = null,
        mothersName: String? = "Mother name",
        productInfo: ProductInfo? = null,
        sexualOrientation: SexualOrientation? = null,
        customPronoun: String? = null,
        colorAndRace: ColorAndRace? = null,
        genderIdentity: GenderIdentity? = null,
        updatedBy: UpdatedBy? = null,
        piiInternalCode: String = Person.generatePiiInternalCode(),
    ) = Person(
        id = personId,
        firstName = firstName,
        lastName = lastName,
        nickName = nickName,
        socialName = socialName,
        socialFirstName = socialFirstName,
        socialLastName = socialLastName,
        nationalId = nationalId,
        email = email,
        addresses = addresses,
        sex = sex,
        gender = gender,
        dateOfBirth = dateOfBirth,
        tags = tags,
        identityDocument = identityDocument,
        identityDocumentIssuingBody = identityDocumentIssuingBody,
        opportunityId = opportunityId,
        leadId = leadId,
        userType = userType,
        phoneNumber = phoneNumber,
        profilePicture = profilePicture,
        acceptedTermsAt = acceptedTermsAt,
        pronoun = pronoun,
        mothersName = mothersName,
        productInfo = productInfo,
        sexualOrientation = sexualOrientation,
        customPronoun = customPronoun,
        colorAndRace = colorAndRace,
        genderIdentity = genderIdentity,
        updatedBy = updatedBy,
        piiInternalCode = piiInternalCode
    )

    fun buildPersonLogin(
        personId: PersonId = PersonId(),
        nationalId: String = "609.048.950-68",
        accessCode: String = "18277182",
        saltAccessCode: String = "344343",
        expirationDate: LocalDateTime = LocalDateTime.now().plusMinutes(20),
        expireReason: ExpireReason? = ExpireReason.NEW_ACCESS,
        email: String? = null,
        firstAccess: Boolean = false,
    ) = PersonLogin(
        personId = personId,
        nationalId = nationalId,
        accessCode = accessCode,
        saltAccessCode = saltAccessCode,
        expirationDate = expirationDate,
        expireReason = expireReason,
        email = email,
        firstAccess = firstAccess,
    )

    fun buildPersonGracePeriod(
        personId: PersonId = PersonId(),
        value: String = "E10",
        startDate: LocalDateTime? = null,
        endDate: LocalDateTime? = null,
        status: PersonGracePeriodStatus = PersonGracePeriodStatus.ACTIVE,
        deletedAt: LocalDateTime? = null,
    ) = PersonGracePeriod(
        personId = personId,
        startDate = startDate ?: LocalDateTime.now().minusDays(1),
        endDate = endDate ?: LocalDateTime.now().plusDays(364),
        value = value,
        type = PersonGracePeriodType.CPT_CID_10,
        status = status,
        deletedAt = deletedAt
    )

    fun buildPersonDefaulter(
        personId: PersonId = PersonId(),
        value: BigDecimal = BigDecimal("10.0"),
        type: PersonDefaulterType = PersonDefaulterType.TARGET,
    ) = PersonDefaulter(
        personId = personId,
        value = value,
        type = type
    )

    fun buildPersonIdentityValidation(
        personId: PersonId = PersonId(),
        transactionId: String = "1234",
        score: BigDecimal? = BigDecimal("0.5"),
        requestValidationCreatedAt: LocalDateTime = LocalDateTime.now(),
        scoreCreatedAt: LocalDateTime? = LocalDateTime.now(),
        type: PersonIdentityValidationType = PersonIdentityValidationType.BIOMETRIC_SCORE,
        status: PersonIdentityValidationStatus = PersonIdentityValidationStatus.PROCESSING,
        fileVaultId: UUID? = null,
        match: Boolean? = null,
    ) = PersonIdentityValidation(
        personId = personId,
        requestValidationCreatedAt = requestValidationCreatedAt,
        scoreCreatedAt = scoreCreatedAt,
        score = score,
        type = type,
        status = status,
        fileVaultId = fileVaultId,
        match = match,
        transactionId = transactionId,
    )

    fun buildMember(
        personId: PersonId = PersonId(),
        activationDate: LocalDateTime? = null,
        productId: UUID = RangeUUID.generate(),
        productTitle: String = "Conforto + (Reembolso)",
        productType: ProductType = ProductType.B2C,
        product: Product = buildProduct(id = productId, title = productTitle, type = productType),
        id: UUID = RangeUUID.generate(),
        selectedProduct: MemberProduct = MemberProduct(product.id, product.prices, type = productType),
        canceledAt: LocalDateTime? = null,
        status: MemberStatus = MemberStatus.PENDING,
        cassiMember: CassiMember? = null,
        statusHistory: List<MemberStatusHistoryEntry>? = listOf(
            MemberStatusHistoryEntry(MemberStatus.PENDING, LocalDateTime.now().minusDays(10).toString()),
            MemberStatusHistoryEntry(MemberStatus.ACTIVE, LocalDateTime.now().toString())
        ),
        brand: Brand? = Brand.ALICE,
        externalBrandAccountNumber: String? = null,
        parentMember: UUID? = null,
        parentPerson: PersonId? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedBy: UpdatedBy? = null,
        beneficiaryId: UUID? = null,
        companyId: UUID? = null,
        companySubContractId: UUID? = null,
        beneficiary: MemberBeneficiary? = null,
    ) = Member(
        id = id,
        personId = personId,
        contract = Contract("https://localhost/contrato.pdf"),
        selectedProduct = selectedProduct,
        activationDate = if (status == MemberStatus.ACTIVE && activationDate == null) LocalDateTime.now()
            .minusYears(1) else activationDate,
        status = status,
        statusHistory = statusHistory,
        canceledAt = canceledAt,
        cassiMember = cassiMember,
        brand = brand,
        externalBrandAccountNumber = externalBrandAccountNumber,
        parentPerson = parentPerson,
        parentMember = parentMember,
        createdAt = createdAt,
        updatedBy = updatedBy,
        beneficiaryId = beneficiaryId,
        companyId = companyId,
        companySubContractId = companySubContractId,
        beneficiary = beneficiary,
    )

    fun buildMemberLightweight(
        personId: PersonId = PersonId(),
        status: MemberStatus = MemberStatus.ACTIVE,
        id: UUID = RangeUUID.generate(),
        archived: Boolean = false,
    ) = MemberLightweight(
        id = id,
        personId = personId,
        status = status,
        archived = archived,
    )

    fun buildMemberInvoice(
        member: Member = buildMember(),
        id: UUID = RangeUUID.generate(),
        status: InvoiceStatus = InvoiceStatus.OPEN,
        totalAmount: BigDecimal = BigDecimal("10.5"),
        dueDate: LocalDateTime = LocalDateTime.now().plusDays(100),
        copay: BigDecimal = BigDecimal(4.8),
        productPrice: BigDecimal = BigDecimal(15.16),
        discount: BigDecimal = BigDecimal(23.42),
        addition: BigDecimal = BigDecimal(12.34),
        proration: BigDecimal = BigDecimal(56.78),
        invoiceItems: List<InvoiceItem> = listOf(buildInvoiceItem()),
        referenceDate: LocalDate = LocalDate.now(),
        memberInvoiceGroupId: UUID? = null,
        preActivationPaymentId: UUID? = null,
        type: MemberInvoiceType? = null,
    ) = buildMemberInvoice(
        id = id,
        memberId = member.id,
        personId = member.personId,
        referenceDate = referenceDate,
        dueDate = dueDate,
        status = status,
        copay = copay,
        productPrice = productPrice,
        discount = discount,
        addition = addition,
        proration = proration,
        totalAmount = totalAmount,
        invoiceItems = invoiceItems,
        memberInvoiceGroupId = memberInvoiceGroupId,
        preActivationPaymentId = preActivationPaymentId,
        type = type,
    )

    fun buildMemberInvoice(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        status: InvoiceStatus = InvoiceStatus.OPEN,
        totalAmount: BigDecimal = BigDecimal("10.5"),
        dueDate: LocalDateTime = LocalDateTime.now().plusDays(100),
        copay: BigDecimal = BigDecimal(4.8),
        productPrice: BigDecimal = BigDecimal(15.16),
        discount: BigDecimal = BigDecimal(23.42),
        addition: BigDecimal = BigDecimal(12.34),
        proration: BigDecimal = BigDecimal(56.78),
        invoiceItems: List<InvoiceItem> = listOf(buildInvoiceItem()),
        referenceDate: LocalDate = LocalDate.now(),
        invoiceBreakdown: InvoiceBreakdown = InvoiceBreakdown(
            copay = copay.money,
            productPrice = productPrice.money,
            discount = discount.money,
            addition = addition.money,
            proRation = proration.money,
            totalAmount = totalAmount.money,
        ),
        memberInvoiceGroupId: UUID? = null,
        preActivationPaymentId: UUID? = null,
        type: MemberInvoiceType? = null,
    ) = MemberInvoice(
        id = id,
        memberId = memberId,
        personId = personId,
        totalAmount = totalAmount,
        referenceDate = referenceDate,
        dueDate = dueDate,
        status = status,
        invoiceBreakdown = invoiceBreakdown,
        invoiceItems = invoiceItems,
        memberInvoiceGroupId = memberInvoiceGroupId,
        preActivationPaymentId = preActivationPaymentId,
        type = type
    )

    fun buildMemberInvoiceGroup(
        id: UUID = RangeUUID.generate(),
        memberInvoiceIds: List<UUID> = listOf(RangeUUID.generate()),
        billingAccountablePartyId: UUID = RangeUUID.generate(),
        referenceDate: LocalDate = LocalDate.now(),
        dueDate: LocalDate = LocalDate.now(),
        status: MemberInvoiceGroupStatus = MemberInvoiceGroupStatus.PROCESSING,
        type: MemberInvoiceType = MemberInvoiceType.B2B_REGULAR_PAYMENT,
        totalAmount: BigDecimal = BigDecimal(1000.50),
        globalItems: List<InvoiceItem>? = null,
        externalId: String? = RangeUUID.generate().toString(),
        companyId: UUID? = RangeUUID.generate(),
        companySubcontractId: UUID? = RangeUUID.generate(),
        quantityMemberInvoices: Int? = null,
        invoiceLiquidationIds: List<UUID>? = null,
        preActivationPaymentId: UUID? = null,
        discount: BigDecimal = BigDecimal.ZERO,
        validityPeriodStart: LocalDate? = null,
        validityPeriodEnd: LocalDate? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = MemberInvoiceGroup(
        id = id,
        externalId = externalId,
        memberInvoiceIds = memberInvoiceIds,
        billingAccountablePartyId = billingAccountablePartyId,
        referenceDate = referenceDate,
        dueDate = dueDate,
        status = status,
        type = type,
        totalAmount = totalAmount,
        globalItems = globalItems,
        companyId = companyId,
        companySubcontractId = companySubcontractId,
        quantityMemberInvoices = quantityMemberInvoices,
        invoiceLiquidationIds = invoiceLiquidationIds,
        preActivationPaymentId = preActivationPaymentId,
        discount = discount,
        validityPeriodStart = validityPeriodStart,
        validityPeriodEnd = validityPeriodEnd,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildPreActivationPayment(
        id: UUID = RangeUUID.generate(),
        companyId: UUID? = null,
        companySubContractId: UUID? = null,
        memberInvoiceIds: List<UUID> = listOf(RangeUUID.generate()),
        billingAccountablePartyId: UUID = RangeUUID.generate(),
        referenceDate: LocalDate = LocalDate.now(),
        dueDate: LocalDate = LocalDate.now(),
        status: PreActivationPaymentStatus = PreActivationPaymentStatus.PROCESSING,
        externalId: String? = RangeUUID.generate().toString(),
        globalItems: List<InvoiceItem> = emptyList(),
        type: PreActivationPaymentType = PreActivationPaymentType.B2B,
        totalAmount: BigDecimal = BigDecimal(1000.50),
        memberInvoiceGroupId: UUID? = null,
    ) = PreActivationPayment(
        id = id,
        externalId = externalId,
        memberInvoiceIds = memberInvoiceIds,
        billingAccountablePartyId = billingAccountablePartyId,
        referenceDate = referenceDate,
        dueDate = dueDate,
        status = status,
        totalAmount = totalAmount,
        type = type,
        companyId = companyId,
        companySubContractId = companySubContractId,
        globalItems = globalItems,
        memberInvoiceGroupId = memberInvoiceGroupId,
    )

    fun buildStaff(
        firstName: String = "José",
        lastName: String = "Silva",
        email: String = "<EMAIL>",
        gender: Gender = Gender.MALE,
        id: UUID = RangeUUID.generate(),
        role: Role = Role.MANAGER_PHYSICIAN,
        nationalId: String? = "***********",
        type: StaffType = StaffType.PITAYA,
        profileImageUrl: String? = null,
        birthDate: LocalDate? = null
    ) = Staff(
        id = id,
        email = email,
        firstName = firstName,
        lastName = lastName,
        gender = gender,
        role = role,
        nationalId = nationalId,
        type = type,
        profileImageUrl = profileImageUrl,
        birthdate = birthDate
    )

    fun buildStaffChannelHistory(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        status: String = "BUSY",
        changedDate: LocalDateTime = LocalDateTime.now(),
        onCall: Boolean? = null,
        updatedBy: StaffChannelHistoryUpdatedBy? = null
    ) = StaffChannelHistory(
        id = id,
        staffId = staffId,
        status = status,
        changedDate = changedDate,
        onCall = onCall,
        updatedBy = updatedBy
    )

    fun buildHealthProfessional(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        profileBio: String? = "bio",
        council: Council = Council(
            "1234",
            State.SP,
            CouncilType.CRM
        ),
        specialtyId: UUID? = RangeUUID.generate(),
        subSpecialtyIds: List<UUID> = listOf(RangeUUID.generate()),
        internalSpecialtyId: UUID? = null,
        internalSubSpecialtyIds: List<UUID> = emptyList(),
        quote: String? = "quote",
        urlSlug: String? = "url-slug",
        imageUrl: String? = null,
        staff: Staff? = null,
        onCall: Boolean = false,
        providerUnitIds: List<UUID> = emptyList(),
        email: String = "<EMAIL>",
        name: String = "João",
        gender: Gender? = null,
        nationalId: String? = null,
        type: StaffType = StaffType.PITAYA,
        role: Role? = null,
        education: List<String> = emptyList(),
        qualifications: List<Qualification> = emptyList(),
        appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
        addressesStructured: List<StructuredAddress>? = null,
        contactIds: List<UUID> = emptyList(),
        healthSpecialistScore: HealthSpecialistScoreEnum? = null,
        theoristTier: SpecialistTier? = null,
        tier: SpecialistTier? = SpecialistTier.EXPERT,
        onVacationUntil: LocalDateTime? = null,
        onVacationStart: LocalDateTime? = null
    ) = HealthProfessional(
        id = id,
        staffId = staffId,
        profileBio = profileBio,
        council = council,
        specialtyId = specialtyId,
        subSpecialtyIds = subSpecialtyIds,
        internalSpecialtyId = internalSpecialtyId,
        internalSubSpecialtyIds = internalSubSpecialtyIds,
        quote = quote,
        education = education,
        qualifications = qualifications,
        urlSlug = urlSlug,
        imageUrl = imageUrl,
        staff = staff,
        onCall = onCall,
        providerUnitIds = providerUnitIds,
        email = email,
        name = name,
        gender = gender,
        nationalId = nationalId,
        type = type,
        role = role,
        appointmentTypes = appointmentTypes,
        addressesStructured = addressesStructured,
        contactIds = contactIds,
        healthSpecialistScore = healthSpecialistScore,
        theoristTier = theoristTier,
        tier = tier,
        onVacationUntil = onVacationUntil,
        onVacationStart = onVacationStart
    )

    fun buildHealthProfessionalTierHistory(
        staffId: UUID = RangeUUID.generate(),
        tier: SpecialistTier? = null,
        theoristTier: SpecialistTier? = null,
        activatedAt: LocalDateTime = LocalDateTime.now(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = HealthProfessionalTierHistory(
        staffId = staffId,
        tier = tier,
        theoristTier = theoristTier,
        activatedAt = activatedAt,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildSchedulePreference(
        weeklyHours: Int = 10,
        intervalBetweenEvents: Int = 10,
        staffId: UUID,
        googleRefreshToken: String? = "",
        zoomLink: String? = null,
        zoomRefreshToken: String? = null,
        isDoingFullSync: Boolean? = false,
        googleCalendarWebhookExpiration: LocalDateTime = LocalDateTime.now()
    ) = SchedulePreference(
        weeklyHours = weeklyHours,
        intervalBetweenEvents = intervalBetweenEvents,
        staffId = staffId,
        googleRefreshToken = googleRefreshToken,
        zoomLink = zoomLink,
        zoomRefreshToken = zoomRefreshToken,
        googleCalendarWebhookExpiration = googleCalendarWebhookExpiration,
        isDoingFullSync = isDoingFullSync ?: false
    )

    fun buildStaffSchedulePreference(
        staffName: String = "",
        staffRole: String = "",
        staffId: UUID = RangeUUID.generate(),
        schedulePreferenceId: UUID = RangeUUID.generate(),
        staffEmail: String = "",
        staffPhoto: String = "",
        hasStaffSchedules: Boolean = false,
    ) = StaffSchedulePreference(
        staffName = staffName,
        staffRole = staffRole,
        staffId = staffId,
        schedulePreferenceId = schedulePreferenceId,
        staffEmail = staffEmail,
        staffPhoto = staffPhoto,
        hasStaffSchedules = hasStaffSchedules,
    )

    fun buildStaffSchedule(
        staffId: UUID = RangeUUID.generate(),
        startHour: LocalTime = LocalTime.now(),
        untilHour: LocalTime = LocalTime.now(),
        weekDay: WeekDay = WeekDay.MONDAY,
        providerUnitId: UUID? = null,
        alsoDigital: Boolean = false,
        type: StaffScheduleType = StaffScheduleType.HAD,
        exceptionEventTypes: List<UUID> = emptyList(),
        lastUpdatedBy: UUID? = null,
        carveOutHours: Int? = null,
        startDate: LocalDate? = null,
        status: StaffScheduleStatus = StaffScheduleStatus.ACTIVE
    ) = StaffSchedule(
        staffId = staffId,
        startHour = startHour,
        untilHour = untilHour,
        weekDay = weekDay,
        providerUnitId = providerUnitId,
        alsoDigital = alsoDigital,
        type = type,
        exceptionEventTypes = exceptionEventTypes,
        lastUpdatedBy = lastUpdatedBy,
        carveOutHours = carveOutHours,
        startDate = startDate,
        status = status,
    )

    fun buildDeIdentifiedHiViewer() = Staff(
        email = "<EMAIL>",
        firstName = "José",
        lastName = "Pereira",
        gender = Gender.MALE,
        role = Role.DE_IDENTIFIED_HI_VIEWER,
        type = StaffType.PITAYA
    )

    fun buildProductTech() = Staff(
        email = "<EMAIL>",
        firstName = "José",
        lastName = "Pereira",
        gender = Gender.MALE,
        role = Role.PRODUCT_TECH,
        type = StaffType.PITAYA
    )

    fun buildRiskIntermittentNurse() = Staff(
        email = "<EMAIL>",
        firstName = "José",
        lastName = "Pereira",
        gender = Gender.MALE,
        role = Role.RISK_INTERMITTENT_NURSE,
        type = StaffType.PITAYA
    )

    fun buildHealthcareTeamPhysician() = buildStaff()

    fun buildChiefPhysician() = Staff(
        email = "<EMAIL>",
        firstName = "Marcos",
        lastName = "Pereira",
        gender = Gender.FEMALE,
        role = Role.CHIEF_PHYSICIAN,
        type = StaffType.PITAYA
    )

    fun buildHealthcareTeamNurse() = Staff(
        email = "<EMAIL>",
        firstName = "Joana",
        lastName = "Pereira",
        gender = Gender.FEMALE,
        role = Role.HEALTHCARE_TEAM_NURSE,
        type = StaffType.PITAYA
    )

    fun buildDigitalCareNurse() = Staff(
        email = "<EMAIL>",
        firstName = "Fabiana",
        lastName = "Pereira",
        gender = Gender.FEMALE,
        role = Role.DIGITAL_CARE_NURSE,
        type = StaffType.PITAYA
    )

    fun buildDigitalCarePhysician() = Staff(
        email = "<EMAIL>",
        firstName = "Joana",
        lastName = "Mesquita",
        gender = Gender.FEMALE,
        role = Role.DIGITAL_CARE_PHYSICIAN,
        type = StaffType.PITAYA
    )

    fun buildNutritionist() = Staff(
        email = "<EMAIL>",
        firstName = "Joana",
        lastName = "Mesquita",
        gender = Gender.FEMALE,
        role = Role.NUTRITIONIST,
        type = StaffType.PITAYA
    )

    fun buildPsychologist() = Staff(
        email = "<EMAIL>",
        firstName = "Joana",
        lastName = "Mesquita",
        gender = Gender.FEMALE,
        role = Role.PSYCHOLOGIST,
        type = StaffType.PITAYA
    )

    fun buildHealthPlan(
        personId: PersonId = PersonId(),
        description: String = "Melhorar alimentação, sono e praticar exercícios.",
        id: UUID = RangeUUID.generate(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) =
        HealthPlan(
            id = id,
            personId = personId,
            description = description,
            healthGoal = "viver melhor",
            updatedAt = updatedAt
        )

    fun buildHealthPlanTaskTestRequest(
        healthPlanId: UUID = RangeUUID.generate(), personId: PersonId = PersonId(),
        description: String? = "some description",
        groupId: UUID? = null,
        appointmentId: UUID? = null,
        title: String = "some title",
        content: Map<String, Any> = mapOf("code" to "testCode", "memberGuidance" to "XPTO"),
    ) = HealthPlanTask(
        personId = personId,
        healthPlanId = healthPlanId,
        title = title,
        description = description,
        dueDate = LocalDate.now(),
        status = HealthPlanTaskStatus.ACTIVE,
        lastRequesterStaffId = RangeUUID.generate(),
        requestersStaffIds = emptySet(),
        type = HealthPlanTaskType.TEST_REQUEST,
        releasedAt = LocalDateTime.now(),
        start = Start(
            type = StartType.IMMEDIATE,
            date = LocalDateTime.now()
        ),
        content = content,
        groupId = groupId,
        favorite = false,
        appointmentId = appointmentId
    )

    fun buildHealthPlanTaskFollowUpRequest(
        personId: PersonId = PersonId(),
        description: String? = "some description",
        groupId: UUID? = null,
        appointmentId: UUID? = null,
        providerType: FollowUpProviderType? = null,
        followUpInterval: FollowUpInterval? = null,

        ): HealthPlanTask {
        val content = mutableMapOf<String, Any>()

        providerType?.let { content["providerType"] = it }
        followUpInterval?.let { content["followUpInterval"] = it.asMap() }

        return HealthPlanTask(
            personId = personId,
            title = "some title",
            description = description,
            dueDate = LocalDate.now(),
            status = HealthPlanTaskStatus.ACTIVE,
            lastRequesterStaffId = RangeUUID.generate(),
            requestersStaffIds = emptySet(),
            type = HealthPlanTaskType.FOLLOW_UP_REQUEST,
            releasedAt = LocalDateTime.now(),
            start = Start(
                type = StartType.IMMEDIATE,
                date = LocalDateTime.now()
            ),
            content = content,
            groupId = groupId,
            favorite = false,
            appointmentId = appointmentId
        )
    }

    fun buildHealthPlanTaskSurgeryPrescription(
        healthPlanId: UUID = RangeUUID.generate(), personId: PersonId = PersonId(),
        description: String? = "some description",
        appointmentId: UUID? = null,
    ): HealthPlanTask {
        val content = mutableMapOf(
            "expectedDate" to LocalDate.now().toString(),
            "reason" to "Muitas dores",
            "provider" to Hospital("Hospital da Criança", RangeUUID.generate()),
            "procedures" to listOf(
                SurgicalProcedure("Corte", "tussCode12412")
            )
        )
        return HealthPlanTask(
            personId = personId,
            healthPlanId = healthPlanId,
            title = "some title",
            description = description,
            dueDate = LocalDate.now(),
            status = HealthPlanTaskStatus.ACTIVE,
            lastRequesterStaffId = RangeUUID.generate(),
            requestersStaffIds = emptySet(),
            type = HealthPlanTaskType.SURGERY_PRESCRIPTION,
            releasedAt = LocalDateTime.now(),
            start = Start(
                type = StartType.IMMEDIATE,
                date = LocalDateTime.now()
            ),
            content = content,
            favorite = false,
            appointmentId = appointmentId
        )
    }

    fun buildHealthPlanTaskReferral(
        healthPlanId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        description: String = "some description",
        diagnosticHypothesis: String = "está com problema endócrino",
        lastRequesterStaffId: UUID = RangeUUID.generate(),
        releasedByStaffId: UUID? = null,
        title: String = "Nutricionista",
        groupId: UUID? = RangeUUID.generate(),
        specialty: ReferralSpecialty? = null,
        subSpecialty: ReferralSpecialty? = null,
        suggestedSpecialist: SuggestedSpecialist? = null,
        sessionsQuantity: Int? = null,
        followUpMaxQuantity: Int? = null,
        deadline: Deadline? = null,
        dueDate: LocalDate? = LocalDate.now(),
        appointmentId: UUID? = null,
        isAdvancedAccess: Boolean? = null,
        caseId: UUID? = null,
        shortId: String? = null,
        token: String? = null,
    ): HealthPlanTask =
        Referral(
            suggestedSpecialist = suggestedSpecialist ?: SuggestedSpecialist(
                name = "João",
                id = RangeUUID.generate(),
                type = SpecialistType.STAFF,
                tier = SpecialistTier.TALENTED,
                memberTier = ReferralMemberTier(
                    isInMemberTier = false,
                    memberProduct = ReferralMemberProduct(
                        id = RangeUUID.generate(),
                        name = "Alice Plan"
                    ),
                    suggestedReason = ReferralSuggestedSpecialistReason(
                        reason = ReferralSuggestedSpecialistReasonType.OTHER,
                        description = ""
                    )
                )
            ),
            diagnosticHypothesis = diagnosticHypothesis,
            specialty = specialty,
            subSpecialty = subSpecialty,
            sessionsQuantity = sessionsQuantity,
            followUpMaxQuantity = followUpMaxQuantity,
            isAdvancedAccess = isAdvancedAccess,
            shortId = shortId,
            token = token,
            task = HealthPlanTask(
                personId = personId,
                healthPlanId = healthPlanId,
                title = title,
                groupId = groupId,
                description = description,
                dueDate = dueDate,
                deadline = deadline,
                status = HealthPlanTaskStatus.ACTIVE,
                lastRequesterStaffId = lastRequesterStaffId,
                releasedByStaffId = releasedByStaffId,
                requestersStaffIds = emptySet(),
                type = HealthPlanTaskType.REFERRAL,
                releasedAt = LocalDateTime.now(),
                start = Start(
                    type = StartType.IMMEDIATE,
                    date = LocalDateTime.now()
                ),
                favorite = false,
                appointmentId = appointmentId,
                caseId = caseId
            ),
        ).generalize()

    fun buildExternalReferral(
        personId: PersonId = PersonId(),
        reason: String = "Reason",
        specialty: ExternalReferral.Specialty = ExternalReferral.Specialty(RangeUUID.generate(), "especialista"),
        referredAt: LocalDateTime = LocalDateTime.now(),
        referredByCouncilName: String = "CRM",
        referredByCouncilState: State = State.SP,
        referredByCouncilNumber: String = "0123456",
        referredByName: String = "Alice Da Lisboa",
        dueDate: LocalDateTime = LocalDateTime.now().plusMonths(3L),
    ) = ExternalReferral(
        personId = personId,
        reason = reason,
        specialty = specialty,
        referredAt = referredAt,
        referredByCouncilName = referredByCouncilName,
        referredByCouncilState = referredByCouncilState,
        referredByCouncilNumber = referredByCouncilNumber,
        referredByName = referredByName,
        dueDate = dueDate,
    )

    fun buildHealthPlanTaskEmergency(
        healthPlanId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        description: String = "some description",
        diagnosticHypothesis: String = "está com problema endócrino",
        lastRequesterStaffId: UUID = RangeUUID.generate(),
        title: String = "Cardiologia",
        groupId: UUID? = RangeUUID.generate(),
        specialty: EmergencySpecialty? = null,
        appointmentId: UUID? = null,
        caseRecordDetails: List<CaseRecordDetails> = emptyList(),
    ): HealthPlanTask {
        val content = mutableMapOf<String, Any>(
            "diagnosticHypothesis" to diagnosticHypothesis
        )

        specialty?.let { content["specialty"] = it.asMap() }

        return HealthPlanTask(
            personId = personId,
            healthPlanId = healthPlanId,
            title = title,
            groupId = groupId,
            description = description,
            dueDate = LocalDate.now(),
            status = HealthPlanTaskStatus.ACTIVE,
            lastRequesterStaffId = lastRequesterStaffId,
            requestersStaffIds = emptySet(),
            type = HealthPlanTaskType.EMERGENCY,
            releasedAt = LocalDateTime.now(),
            start = Start(
                type = StartType.IMMEDIATE,
                date = LocalDateTime.now()
            ),
            content = content,
            favorite = false,
            appointmentId = appointmentId,
            caseRecordDetails = caseRecordDetails
        )
    }

    fun buildAppointment(
        staffId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        description: String = "O paciente estava com problemas estomacais",
        createdAt: LocalDateTime = LocalDateTime.now(),
        type: AppointmentType = DEFAULT,
        discardedType: AppointmentDiscardedType? = null,
        channelId: String? = null,
        caseRecordDetails: List<CaseRecordDetails>? = emptyList(),
        status: AppointmentStatus = AppointmentStatus.DRAFT,
        draftGroupStaffIds: List<UUID>? = emptyList(),
        event: AppointmentEventDetail? = null,
        name: String? = null,
        subjective: String? = null,
        components: List<AppointmentComponent>? = emptyList(),
        emptyEventReason: String? = null,
        contractualRisks: List<AppointmentContractualRisk>? = emptyList(),
        startedAt: LocalDateTime? = null,
        medicalDischarge: Boolean? = null,
        primaryAttentionRequest: String? = null,
        appointmentDate: LocalDate? = null,
        anesthetist: Anesthetist? = null,
    ) =
        Appointment(
            type = type,
            status = status,
            staffId = staffId,
            personId = personId,
            description = description,
            guidance = "Coma mais fibras",
            excuseNotes = listOf(
                ExcuseNote(
                    description = "presente no período da manhã para consulta",
                    id = "0c56bf1b-bfa6-4cff-964b-99473917a36e"
                )
            ),
            completedAt = LocalDateTime.now(),
            createdAt = createdAt,
            subjectiveCodes = listOf(Disease(CIAP_2, "A03", "Febre - CIAP A03")),
            objective = "Suspeita de covid",
            plan = "Foi recomendado ficar em repouso.",
            ownerStaffIds = setOf(staffId),
            referencedLinks = listOf(
                Appointment.ReferencedLink(RangeUUID.generate(), SCHEDULE),
                Appointment.ReferencedLink(RangeUUID.generate(), HEALTH_PLAN_TASK)
            ),
            discardedType = discardedType,
            channelId = channelId,
            caseRecordDetails = caseRecordDetails,
            draftGroupStaffIds = draftGroupStaffIds,
            event = event,
            name = name,
            subjective = subjective,
            components = components,
            emptyEventReason = emptyEventReason,
            externalFiles = listOf(
                ExternalFile(
                    id = RangeUUID.generate(),
                    type = ExternalFileType.IMAGE,
                    origin = ExternalFileOrigin.CHANNELS
                ),
                ExternalFile(
                    id = RangeUUID.generate(),
                    type = ExternalFileType.VIDEO,
                    origin = ExternalFileOrigin.CHANNELS
                )
            ),
            contractualRisks = contractualRisks,
            startedAt = startedAt,
            medicalDischarge = medicalDischarge,
            primaryAttentionRequest = primaryAttentionRequest,
            appointmentDate = appointmentDate,
            anesthetist = anesthetist
        )

    fun buildAppointmentEvent(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        name: String = "Event",
        clinicalRecordName: String = "Appointment Event",
        referenceModel: AppointmentEventReferenceModel = AppointmentEventReferenceModel.CHANNEL,
        referenceModelId: String = "channel_id",
        referenceModelDate: LocalDateTime = LocalDateTime.now()
    ) = AppointmentEvent(
        id = id,
        personId = personId,
        name = name,
        clinicalRecordName = clinicalRecordName,
        referenceModel = referenceModel,
        referenceModelId = referenceModelId,
        referenceModelDate = referenceModelDate
    )

    fun buildAppointmentMacro(
        id: UUID = RangeUUID.generate(),
        title: String = "Título da Macro",
        content: String = "Conteúdo da macro",
        componentType: AppointmentComponentType = AppointmentComponentType.SUBJECTIVE,
        active: Boolean = true
    ) = AppointmentMacro(
        id = id,
        title = title,
        content = content,
        componentType = componentType,
        active = active
    )

    fun buildAppointmentSchedule(
        personId: PersonId = PersonId(),
        status: AppointmentScheduleStatus = AppointmentScheduleStatus.SCHEDULED,
        type: AppointmentScheduleType = AppointmentScheduleType.HEALTH_DECLARATION,
        staffId: UUID? = null,
        healthcareTeamId: UUID = buildHealthcareTeam().id,
        startTime: LocalDateTime = LocalDateTime.of(2020, 1, 1, 10, 10),
        endTime: LocalDateTime? = null,
        taskId: UUID? = null,
        eventName: String = "Primeira consulta com seu time de saúde",
        currentUserType: UserType? = null,
        location: String? = "Rua Rebouças, 3506",
        cancelledByType: AppointmentScheduleCancelledByType? = null,
        providerUnitId: UUID? = null,
        appointmentScheduleEventTypeId: UUID? = null,
        id: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) =
        AppointmentSchedule(
            id = id,
            personId = personId,
            eventId = "C4L3NDLY3V3NT",
            eventName = eventName,
            healthcareTeamId = healthcareTeamId,
            startTime = startTime,
            endTime = endTime,
            status = status,
            type = type,
            staffId = staffId,
            healthPlanTaskId = taskId,
            currentUserType = currentUserType,
            location = location,
            eventUuid = null,
            cancelledByType = cancelledByType,
            providerUnitId = providerUnitId,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            createdAt = createdAt,
            updatedAt = updatedAt
        )

    fun buildAppointmentScheduleCheckIn(
        personId: PersonId = PersonId(),
        appointmentScheduleId: UUID = RangeUUID.generate(),
        status: AppointmentScheduleCheckInStatus = AppointmentScheduleCheckInStatus.SENT,
        id: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = AppointmentScheduleCheckIn(
        personId = personId,
        appointmentScheduleId = appointmentScheduleId,
        status = status,
        id = id,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildAppointmentScheduleOption(
        title: String = "Ale Carreiro",
        description: String? = "Nutrição comportamental",
        calendarUrl: String? = "https://calendar.alice.com.br/ale_carreiro",
        imageUrl: String = "https://assets.alice.com.br/ale_carreiro.jpg",
        type: AppointmentScheduleType = AppointmentScheduleType.NUTRITIONIST,
        staffId: UUID? = RangeUUID.generate(),
        active: Boolean = true,
        specialtyId: UUID? = null,
        specialistId: UUID? = null,
        subSpecialtyIds: List<UUID>? = emptyList(),
        showOnApp: Boolean = true,
        userType: UserType = UserType.MEMBER,
        calendarProviderUnits: List<CalendarProviderUnit>? = null,
        shouldUseInternalScheduler: Boolean = false,
        appointmentScheduleEventTypeId: UUID? = null,
        healthcareModelType: HealthcareModelType = HealthcareModelType.V2,
        id: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) =
        AppointmentScheduleOption(
            id = id,
            title = title,
            description = description,
            calendarUrl = calendarUrl,
            imageUrl = imageUrl,
            type = type,
            staffId = staffId,
            active = active,
            specialtyId = specialtyId,
            specialistId = specialistId,
            showOnApp = showOnApp,
            availabilityLevel = 0,
            userType = userType,
            calendarProviderUnits = calendarProviderUnits,
            shouldUseInternalScheduler = shouldUseInternalScheduler,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            healthcareModelType = healthcareModelType,
            subSpecialtyIds = subSpecialtyIds,
            createdAt = createdAt,
            updatedAt = updatedAt
        )

    fun buildAppointmentScheduleEventType(
        id: UUID = RangeUUID.generate(),
        title: String = "Ale Carreiro",
        description: String = "Nutrição comportamental",
        specialtyId: UUID? = RangeUUID.generate(),
        subSpecialtyIds: List<UUID> = emptyList(),
        showOnApp: Boolean = true,
        category: AppointmentScheduleType = AppointmentScheduleType.OTHER,
        duration: Int = 30,
        locationType: AppointmentScheduleEventTypeLocation = AppointmentScheduleEventTypeLocation.REMOTE,
        minimumTimeToScheduleBeforeAppointmentTime: Int = 1,
        isMultiProfessionalReferral: Boolean = false,
        numberOfDaysFromNowToAllowScheduling: Int = 90,
        status: Status = Status.ACTIVE,
        membersRisk: List<AppointmentScheduleEventTypeMemberRisk> = emptyList(),
        internalObservation: String? = null,
        availableWeekDays: List<Weekday> = Weekday.values().toList(),
        lastUpdatedBy: UUID? = null,
        healthcareModelType: HealthcareModelType = HealthcareModelType.V3,
        groupByType: AppointmentScheduleType = AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST,
        ageRating: AgeRatingType = AgeRatingType.BOTH,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) =
        AppointmentScheduleEventType(
            id = id,
            title = title,
            description = description,
            specialtyId = specialtyId,
            showOnApp = showOnApp,
            category = category,
            duration = duration,
            locationType = locationType,
            minimumTimeToScheduleBeforeAppointmentTime = minimumTimeToScheduleBeforeAppointmentTime,
            isMultiProfessionalReferral = isMultiProfessionalReferral,
            numberOfDaysFromNowToAllowScheduling = numberOfDaysFromNowToAllowScheduling,
            status = status,
            internalObservation = internalObservation,
            subSpecialtyIds = subSpecialtyIds,
            membersRisk = membersRisk,
            availableWeekDays = availableWeekDays,
            lastUpdatedBy = lastUpdatedBy,
            healthcareModelType = healthcareModelType,
            groupByType = groupByType,
            ageRating = ageRating,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )

    fun buildHealthMeasurement(
        personId: PersonId = PersonId(),
        value: BigDecimal = BigDecimal("1.75"),
        type: HealthMeasurementInternalType? = WEIGHT,
        typeId: UUID? = null,
        active: Boolean = true,
        addedAt: LocalDateTime = LocalDateTime.now(),
        updatedByStaffId: UUID = RangeUUID.generate(),
        appointmentId: UUID = RangeUUID.generate(),
    ) = HealthMeasurement(
        personId = personId,
        value = value,
        type = type,
        typeId = typeId,
        active = active,
        addedAt = addedAt,
        updatedByStaffId = updatedByStaffId,
        appointmentId = appointmentId
    )

    fun buildHealthMeasurements(
        personId: PersonId = PersonId(),
        height: BigDecimal = BigDecimal("1.75"),
        weight: BigDecimal = BigDecimal("80.00"),
        skeletalMuscleMass: BigDecimal = BigDecimal("0.50"),
        bodyFatPercentage: BigDecimal = BigDecimal("0.30"),
        systolicPressure: BigDecimal = BigDecimal("7.00"),
        diastolicPressure: BigDecimal = BigDecimal("9.00"),
        heartRate: BigDecimal = BigDecimal("70"),
        staffId: UUID = RangeUUID.generate(),
        appointmentId: UUID? = null,
    ) = listOf(
        HealthMeasurement(
            personId = personId,
            value = height,
            type = HEIGHT,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = weight,
            type = WEIGHT,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = skeletalMuscleMass,
            type = SKELETAL_MUSCLE_MASS,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = bodyFatPercentage,
            type = BODY_FAT_PERCENTAGE,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = heartRate,
            type = HEART_RATE,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = systolicPressure,
            type = SYSTOLIC_PRESSURE,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = diastolicPressure,
            type = DIASTOLIC_PRESSURE,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = BigDecimal("1.0000"),
            type = EUROQOL,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = BigDecimal("1.0000"),
            type = GAD_7,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        ),
        HealthMeasurement(
            personId = personId,
            value = BigDecimal("1.0000"),
            type = PHQ_9,
            active = true,
            addedAt = LocalDateTime.now(),
            updatedByStaffId = staffId,
            appointmentId = appointmentId
        )
    )

    fun buildPersonClinicalAccount(
        personId: PersonId = PersonId(),
        healthcareTeamId: UUID = RangeUUID.generate(),
        channelId: String? = null,
        referenceNursesGroupId: UUID? = null,
        multiStaffIds: List<UUID> = emptyList()
    ) =
        PersonClinicalAccount(
            personId = personId,
            healthcareTeamId = healthcareTeamId,
            channelId = channelId,
            referenceNursesGroupId = referenceNursesGroupId,
            multiStaffIds = multiStaffIds
        )

    fun buildHealthcareTeam(
        id: UUID = RangeUUID.generate(),
        physicianStaffId: UUID = buildHealthcareTeamPhysician().id,
        nurseStaffId: UUID = buildHealthcareTeamPhysician().id,
        digitalCareNurseStaffIds: List<UUID> = emptyList(),
        careCoordNurseStaffId: UUID? = null,
        type: HealthcareTeam.Type = HealthcareTeam.Type.STANDARD,
        segment: HealthcareTeam.Segment = HealthcareTeam.Segment.PEDIATRIC,
        maxMemberAssociation: Int? = null,
        address: StructuredAddress? = null,
        physicianStaffGender: Gender? = null
    ) =
        HealthcareTeam(
            id = id,
            physicianStaffId = physicianStaffId,
            nurseStaffId = nurseStaffId,
            digitalCareNurseStaffIds = digitalCareNurseStaffIds,
            careCoordNurseStaffId = careCoordNurseStaffId,
            type = type,
            segment = segment,
            maxMemberAssociation = maxMemberAssociation,
            address = address,
            physicianStaffGender = physicianStaffGender
        )

    fun buildHealthcareAdditionalTeam(
        id: UUID = RangeUUID.generate(),
        staffIds: List<UUID> = listOf(RangeUUID.generate()),
        type: HealthcareAdditionalTeamType = HealthcareAdditionalTeamType.SCREENING,
        healthcareTeamIds: List<UUID> = listOf(RangeUUID.generate()),
        active: Boolean = true,
    ) =
        HealthcareAdditionalTeam(
            id = id,
            staffIds = staffIds,
            type = type,
            healthcareTeamIds = healthcareTeamIds,
            active = active
        )

    fun buildCpt(
        condition: String = "gases",
        cids: List<String> = listOf("A79", "Z42"),
    ) = Cpt(
        condition = condition,
        cids = cids,
    )

    private fun buildCpts() = listOf(
        Cpt(
            condition = "diarréia",
            cids = listOf("J11")
        ),
        Cpt(
            condition = "gases",
            cids = listOf("A79", "Z42")
        )
    )

    fun buildHealthDeclaration(
        personId: PersonId = PersonId(),
        cpts: List<Cpt> = buildCpts(),
        surgeries: List<Surgery> = emptyList(),
        weight: BigDecimal = BigDecimal("70"),
        height: BigDecimal = BigDecimal("1.70"),
        finishedAt: LocalDateTime? = LocalDateTime.now(),
        answers: List<HealthDeclarationAnswer> = listOf(
            buildHealthDeclarationAnswer(),
            HealthDeclarationAnswer(
                question = "Já foi internado ou submetido a alguma cirurgia?",
                answer = "false"
            ),
        ),
    ) = HealthDeclaration(
        id = RangeUUID.generate(),
        personId = personId,
        weight = weight,
        height = height,
        finishedAt = finishedAt,
        signedAt = LocalDateTime.now(),
        signatureInfo = SignatureInfo("Device Name"),
        answers = answers,
        cpts = cpts,
        surgeries = surgeries,
    )

    fun buildProviders() = listOf(
        Provider(
            name = "Hosp. Israelita Albert Einstein",
            type = ProviderType.HOSPITAL,
            flagship = true,
            imageUrl = "einstein_img",
            site = "www.hospital.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "hospital_einstein"
        ),
        Provider(
            name = "Hosp. Beneficência Portuguesa",
            type = ProviderType.HOSPITAL,
            imageUrl = "beneficiencia_img",
            site = "www.hospital.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "hospital_beneficencia_portuguesa"
        ),
        Provider(
            name = "Hosp. Alemão Oswaldo Cruz - Vergueiro",
            type = ProviderType.HOSPITAL,
            imageUrl = "vergueiro_img",
            site = "www.hospital.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "hospital_alemao_oswaldo_cruz_vergueiro",
        ),
        Provider(
            name = "Hosp. Alemão Oswaldo Cruz - Paulista",
            type = ProviderType.HOSPITAL,
            imageUrl = "paulista_img",
            site = "www.hospital.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "hospital_alemao_oswaldo_cruz_paulista"
        ),
        Provider(
            name = "Hosp. Infantil Sabara",
            type = ProviderType.CHILDREN,
            imageUrl = "sabara_img",
            site = "www.hospital.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "hospital_infantil_sabara"
        ),
        Provider(
            name = "A+ Medicina diagnóstica",
            type = ProviderType.LABORATORY,
            imageUrl = "a_img",
            site = "www.laboratorio.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "laboratorio_a_mais_medicina_diagnóstica"
        ),
        Provider(
            name = "Fleury Medicina e Saúde",
            type = ProviderType.LABORATORY,
            imageUrl = "fleury_img",
            site = "www.laboratorio.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "laboratorio_fleury"
        ),
        Provider(
            name = "Lab. Albert Einstein",
            type = ProviderType.LABORATORY,
            flagship = true,
            imageUrl = "lab_einstein_img",
            site = "www.laboratorio.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "laboratorio_einstein"
        ),
        Provider(
            name = "Sepaco",
            type = ProviderType.MATERNITY,
            imageUrl = "sepaco_img",
            site = "www.laboratorio.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "maternidade_sepaco"
        ),
        Provider(
            name = "Pro Matre",
            type = ProviderType.MATERNITY,
            imageUrl = "pro_matre_img",
            site = "www.laboratorio.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "maternidade_pro_matre"
        ),
        Provider(
            name = "Santa Joana",
            type = ProviderType.MATERNITY,
            imageUrl = "santa_joana_img",
            site = "www.laboratorio.org.br",
            cnpj = "**********",
            phones = listOf(
                PhoneNumber(phone = "***********"),
                PhoneNumber(title = "Secundário", phone = "***********")
            ),
            urlSlug = "maternidade_santa_joana"
        )
    )

    fun buildSiteAccreditedNetwork(
        id: UUID = RangeUUID.generate(),
        title: String? = null,
        brand: Brand? = Brand.ALICE,
        active: Boolean = false,
        productIds: List<UUID> = emptyList(),
        bundleIds: List<UUID> = emptyList(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = SiteAccreditedNetwork(
        id = id,
        title = title,
        brand = brand,
        active = active,
        productIds = productIds,
        bundleIds = bundleIds,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildSiteAccreditedNetworkProvider(
        id: UUID = RangeUUID.generate(),
        name: String = "Hospital Einstein",
        type: SiteAccreditedNetworkProviderType = SiteAccreditedNetworkProviderType.HOSPITAL,
        categories: List<String> = listOf("EMERGENCY_UNITY"),
        referencedModelId: UUID = RangeUUID.generate(),
        referencedModelClass: ReferencedModelClass = ReferencedModelClass.PROVIDER,
        active: Boolean = true,
        isFlagship: Boolean = true,
        createdAt: LocalDateTime = LocalDateTime.now(),
        addresses: List<ProviderAddress> = emptyList(),
        productBundleId: UUID = RangeUUID.generate()
    ) = SiteAccreditedNetworkProvider(
        id = id,
        name = name,
        type = type,
        categories = categories,
        referencedModelClass = referencedModelClass,
        referencedModelId = referencedModelId,
        active = active,
        isFlagship = isFlagship,
        createdAt = createdAt,
        addresses = addresses,
        productBundleId = productBundleId
    )

    fun buildSiteAccreditedNetworkAddress(
        id: UUID = RangeUUID.generate(),
        state: String = "São Paulo",
        city: String = "São Paulo",
        neighborhood: String = "Pinheiros",
        networkProviderType: SiteAccreditedNetworkProviderType = SiteAccreditedNetworkProviderType.HOSPITAL
    ) = SiteAccreditedNetworkAddress(
        id = id,
        state = state,
        city = city,
        neighborhood = neighborhood,
        networkProviderType = networkProviderType
    )

    fun buildProduct(
        title: String = "Alice 222 [AP.NAC.02.SC.CR.EMP] - Opcional - PME",
        prices: List<ProductPrice> = listOf(buildProductPrice()),
        bundleIds: List<UUID>? = null,
        id: UUID = RangeUUID.generate(),
        reference: Boolean = false,
        externalIds: List<ExternalId> = listOf(ExternalId(ExternalIdKey.MV_PLAN, "1")),
        anchor: ProductAnchor? = null,
        priceListing: PriceListing? = null,
        active: Boolean = true,
        type: ProductType = ProductType.B2C,
        subType: B2BSubTypes? = null,
        minEmployeeNumber: Int? = null,
        maxEmployeeNumber: Int? = null,
        displayName: String? = "Conforto +",
        complementName: String? = "(Reembolso)",
        previousDisplayName: String? = null,
        ansNumber: String? = null,
        accommodation: AccommodationType? = null,
        hasNationalCoverage: Boolean = false,
        isVisibleForSale: Boolean = false,
        brand: Brand? = Brand.ALICE,
        primaryAttention: PrimaryAttentionType? = PrimaryAttentionType.ALICE,
        tier: TierType? = TierType.TIER_1,
        refund: RefundType = RefundType.NONE,
        coPayment: CoPaymentType = CoPaymentType.FULL,
        characteristics: List<ProductCharacteristic> = emptyList()
    ) =
        Product(
            id = id,
            title = title,
            prices = prices,
            externalIds = externalIds,
            bundleIds = bundleIds,
            reference = reference,
            anchor = anchor,
            active = active,
            type = type,
            subType = subType,
            minEmployeeNumber = minEmployeeNumber,
            maxEmployeeNumber = maxEmployeeNumber,
            displayName = displayName,
            complementName = complementName,
            previousDisplayName = previousDisplayName,
            ansNumber = ansNumber,
            accommodation = accommodation,
            hasNationalCoverage = hasNationalCoverage,
            isVisibleForSale = isVisibleForSale,
            brand = brand,
            primaryAttention = primaryAttention,
            tier = tier,
            refund = refund,
            coPayment = coPayment,
            characteristics = characteristics
        ).withPriceListing(priceListing)

    fun buildProductGroup(
        id: UUID = RangeUUID.generate(),
        title: String = "Conforto max plus",
        accommodation: AccommodationType = AccommodationType.ROOM,
        active: Boolean = true,
        brand: Brand = Brand.ALICE,
        productIds: List<UUID> = emptyList(),
        bundleIds: List<UUID> = emptyList()
    ) =
        ProductGroup(
            id = id,
            title = title,
            active = active,
            accommodation = accommodation,
            brand = brand,
            productIds = productIds,
            bundleIds = bundleIds
        )

    fun buildProductPrice(
        id: String = "1",
        title: String = "Price Title",
        amount: BigDecimal = BigDecimal("900.00"),
        minAge: Int = 0,
        maxAge: Int = 99,
    ) = ProductPrice(
        id = id,
        title = title,
        amount = amount,
        minAge = minAge,
        maxAge = maxAge
    )

    fun buildProductOrder(
        personId: PersonId = PersonId(),
        product: Product = buildProduct(),
        promoCode: PromoCode? = null,
        selectedProduct: MemberProduct? = null,
        productPriceListingId: UUID? = null,
    ) =
        ProductOrder(
            personId = personId,
            price = BigDecimal("900.00"),
            selectedProduct = selectedProduct
                ?: MemberProduct(
                    product.id,
                    product.prices,
                    product.type
                ),
            productPriceListingId = productPriceListingId
        ).setPromoCode(promoCode)

    fun buildMemberProduct(
        id: UUID? = RangeUUID.generate(),
        type: ProductType = ProductType.B2C,
        prices: List<ProductPrice> = emptyList(),
    ) =
        MemberProduct(
            id = id!!,
            type = type,
            prices = prices,
        )

    fun buildPersonRegistration(personId: PersonId = PersonId()) = PersonRegistration(
        personId = personId,
        currentStep = PersonRegistrationStep.NAME_CONFIRMATION,
        answers = emptyList(),
        finishedAt = null
    )

    fun buildPersonOnboarding(
        personId: PersonId = PersonId(),
        currentPhase: OnboardingPhase = OnboardingPhase.SHOPPING,
        slaHealthDeclarationAppointmentCompleted: Long? = null,
        slaContractGenerated: Long? = null,
        healthDeclarationAppointmentScheduledAt: LocalDateTime? = null,
        healthDeclarationAppointmentCompletedAt: LocalDateTime? = null,
        healthDeclarationAppointmentDate: LocalDateTime? = null,
        expiresAt: LocalDateTime? = null,
    ) = PersonOnboarding(
        personId = personId,
        currentPhase = currentPhase,
        finishedAt = if (currentPhase == OnboardingPhase.FINISHED) LocalDateTime.now() else null,
        slaContractGenerated = slaContractGenerated,
        slaHealthDeclarationAppointmentCompleted = slaHealthDeclarationAppointmentCompleted,
        healthDeclarationAppointmentScheduledAt = healthDeclarationAppointmentScheduledAt,
        healthDeclarationAppointmentCompletedAt = healthDeclarationAppointmentCompletedAt,
        healthDeclarationAppointmentDate = healthDeclarationAppointmentDate,
        expiresAt = expiresAt ?: LocalDateTime.now(),
    )

    fun buildServiceScriptAction(
        id: UUID = RangeUUID.generate(),
        type: ServiceScriptAction.ActionType = ServiceScriptAction.ActionType.HEALTH_PLAN_TASK_GROUP_TEMPLATE,
        externalId: UUID = RangeUUID.generate(),
    ) =
        ServiceScriptAction(
            id = id,
            type = type,
            externalId = externalId,
            status = ServiceScriptActionStatus.ACTIVE
        )

    fun buildBudNode(
        name: String = "name",
        internalOrientation: String? = null,
        content: String = "content",
        type: BudNode.BudNodeType = BudNode.BudNodeType.SCRIPT,
        status: ServiceScriptStatus = ServiceScriptStatus.ACTIVE,
        privateOrientation: Boolean = false,
        actions: List<ScriptAction> = emptyList(),
        version: Int = 0,
        id: UUID = RangeUUID.generate(),
        option: Option? = null,
        memberFriendlyContent: String? = null,
        memberFriendlySubContent: String? = null,
        rootNodeId: UUID? = RangeUUID.generate(),
        serviceScriptActionIds: List<UUID> = emptyList()
    ) = BudNode(
        name = name,
        internalOrientation = internalOrientation,
        content = content,
        type = type,
        status = status,
        privateOrientation = privateOrientation,
        actions = actions,
        version = version,
        id = id,
        option = option,
        memberFriendlyContent = memberFriendlyContent,
        memberFriendlySubContent = memberFriendlySubContent,
        rootNodeId = rootNodeId,
        serviceScriptActionIds = serviceScriptActionIds
    )

    fun buildServiceScriptNode(
        name: String = "Fever",
        content: String = "Ask the patient to measure the fever",
        type: ServiceScriptNodeType = ServiceScriptNodeType.QUESTION,
        status: ServiceScriptStatus = ServiceScriptStatus.ACTIVE,
        actions: List<ScriptAction> = emptyList(),
        serviceScriptActionIds: List<UUID> = emptyList(),
        rootNodeId: UUID? = null,
        id: UUID = RangeUUID.generate(),
        externalSource: ExternalSource? = null
    ) =
        ServiceScriptNode(
            id = id,
            name = name,
            content = content,
            type = type,
            status = status,
            actions = actions,
            rootNodeId = rootNodeId,
            serviceScriptActionIds = serviceScriptActionIds,
            externalSource = externalSource
        )

    fun buildServiceScriptRelationship(
        id: UUID = RangeUUID.generate(),
        name: String = "37-38",
        parentId: UUID,
        childId: UUID? = null,
        status: ServiceScriptStatus = ServiceScriptStatus.ACTIVE,
        conditions: List<Condition> = emptyList(),
        priority: Int = 1
    ) =
        ServiceScriptRelationship(
            id = id,
            name = name,
            nodeParentId = parentId,
            nodeChildId = childId,
            status = status,
            conditions = conditions,
            priority = priority
        )

    fun buildServiceScriptNavigation(
        previousNodeId: UUID = RangeUUID.generate(),
        currentNodeId: UUID = RangeUUID.generate(),
        relationshipId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        groupId: UUID = RangeUUID.generate(),
        status: ServiceScriptNavigationStatus = ServiceScriptNavigationStatus.ACTIVE,
        type: ServiceScriptNavigationType = ServiceScriptNavigationType.MANUAL,
        questionAnswers: Option? = null
    ) =
        ServiceScriptNavigation(
            previousNodeId = previousNodeId,
            currentNodeId = currentNodeId,
            relationshipId = relationshipId,
            staffId = staffId,
            groupId = groupId,
            status = status,
            type = type,
            questionAnswers = questionAnswers
        )

    fun buildServiceScriptNavigationGroup(
        personId: PersonId = PersonId(),
        scriptNodeId: UUID = RangeUUID.generate(),
        startedAt: LocalDateTime = LocalDateTime.now(),
        finishedAt: LocalDateTime? = null,
        source: ServiceScriptNavigationSource = ServiceScriptNavigationSource(CHANNEL, "channelId"),
    ) =
        ServiceScriptNavigationGroup(
            personId = personId,
            scriptNodeId = scriptNodeId,
            startedAt = startedAt,
            finishedAt = finishedAt,
            source = source
        )

    fun buildTestResultFile(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        performedAt: LocalDateTime = LocalDateTime.now(),
        description: String = "aaaaa",
        staffId: UUID? = null,
        file: AliceFile? = null,
        uploadedBy: UploadedBy = UploadedBy.STAFF,
    ) =
        TestResultFile(
            id = id,
            personId = personId,
            performedAt = performedAt,
            description = description,
            staffId = staffId,
            fileExtension = "pdf",
            file = file,
            uploadedBy = uploadedBy
        )

    fun buildProvider(
        name: String = "Oswaldo Cruz Paulista",
        id: UUID = RangeUUID.generate(),
        type: ProviderType = ProviderType.HOSPITAL,
        cnpj: String? = "123",
        brand: Brand? = Brand.ALICE,
        flagship: Boolean = false,
        status: Status = Status.ACTIVE,
        about: String? = null,
    ) = Provider(
        id = id,
        name = name,
        site = "www.hospitaloswaldocruz.org.br",
        cnpj = cnpj,
        imageUrl = "http://pudim.com.br/logo.png",
        phones = listOf(
            PhoneNumber(phone = "***********"),
            PhoneNumber(title = "Secundário", phone = "***********")
        ),
        urlSlug = "url_slug",
        daysForPayment = 30,
        type = type,
        brand = brand,
        flagship = flagship,
        status = status,
        about = about,
    )

    fun buildUpdateAppRule(
        id: UUID = RangeUUID.generate(),
        acceptAll: Boolean = false,
        description: String? = "Teste",
        minimumAndroidVersion: String? = "2.80.0",
        minimumIosVersion: String? = "2.80.0",
        routes: List<UpdateAppRule.Route> = listOf(UpdateAppRule.Route(path = "*/cart", method = HttpMethod("PUT"))),
        active: Boolean = true,
    ) = UpdateAppRule(
        id = id,
        acceptAll = acceptAll,
        description = description,
        minimumAndroidVersion = minimumAndroidVersion,
        minimumIosVersion = minimumIosVersion,
        routes = routes,
        active = active,
    )

    fun buildStructuredAddress(
        id: UUID = RangeUUID.generate(),
        label: String = "Endereço Principal",
        street: String = "R. Treze de Maio",
        number: String = "1815",
        complement: String? = null,
        neighborhood: String = "Bela Vista",
        city: String = "São Paulo",
        state: String = "SP",
        zipcode: String = "01323-020",
        referencedModelId: UUID? = null,
        referencedModelClass: StructuredAddressReferenceModel? = null,
        active: Boolean = true,
        latitude: String? = null,
        longitude: String? = null,
    ) = StructuredAddress(
        id = id,
        label = label,
        street = street,
        number = number,
        complement = complement,
        neighborhood = neighborhood,
        city = city,
        state = state,
        zipcode = zipcode,
        referencedModelId = referencedModelId,
        referencedModelClass = referencedModelClass,
        active = active,
        latitude = latitude,
        longitude = longitude,
    )

    fun buildZipcodeAddress(
        id: UUID = RangeUUID.generate(),
        streetType: String? = "Rua",
        street: String? = "Treze de Maio, 78",
        place: String? = null,
        complement: String? = null,
        neighborhood: String? = "Bela Vista",
        city: String? = "São Paulo",
        cityCode: String? = "28",
        stateCode: String? = "11",
        state: String? = "São Paulo",
        federativeUnit: String? = "SP",
        zipcode: String? = "01323-020",
        latitude: Double? = -23.5503099,
        longitude: Double? = -46.6342009,
    ) = ZipcodeAddress(
        id = id,
        streetType = streetType,
        street = street,
        place = place,
        complement = complement,
        neighbourhood = neighborhood,
        city = city,
        state = state,
        zipcode = zipcode,
        cityCode = cityCode,
        stateCode = stateCode,
        federativeUnit = federativeUnit,
        latitude = latitude,
        longitude = longitude,
    )

    fun buildZipcodeAddressLight(
        id: UUID = RangeUUID.generate(),
        zipcode: String? = "01323-020",
        latitude: Double? = -23.5503099,
        longitude: Double? = -46.6342009,
    ) = ZipcodeAddressLight(
        id = id,
        zipcode = zipcode,
        latitude = latitude,
        longitude = longitude,
    )

    fun buildMemberOnboardingTemplate(
        name: String = "",
        type: MemberOnboardingTemplate.MemberOnboardingTemplateType = MemberOnboardingTemplate.MemberOnboardingTemplateType.B2C,
        id: UUID = RangeUUID.generate(),
        active: Boolean = true,
    ) = MemberOnboardingTemplate(
        name = name,
        type = type,
        id = id,
        active = active
    )

    fun buildMemberOnboardingStep(
        title: String = "",
        description: String = "",
        imageUrl: String = "",
        key: String = "",
        actions: List<UUID> = emptyList(),
        optIns: List<MemberOnboardingOptIn>? = emptyList(),
        next: UUID? = null,
        memberOnboardingTemplateId: UUID = RangeUUID.generate(),
        healthFormNavigation: MemberOnboardingStep.MemberOnboardingStepHealthFormEnum? = null,
        type: MemberOnboardingStep.MemberOnboardingStepType = MemberOnboardingStep.MemberOnboardingStepType.INIT,
        id: UUID = RangeUUID.generate(),
        event: MemberOnboardingStep.MemberOnboardingStepEvent? = null,
        widgetComponent: MemberOnboardingStep.MemberOnboardingWidgetComponent? = MemberOnboardingStep.MemberOnboardingWidgetComponent.SCORE_MAGENTA,
        format: MemberOnboardingStep.MemberOnboardingStepFormat = MemberOnboardingStep.MemberOnboardingStepFormat.TEXT,
        videoUrl: String? = null,
        videoCaptionUrl: String? = null,
        showEmergencyButton: Boolean = false,
        forceVideoFolder: Boolean = false,
        estimatedTimeText: String? = null,
        estimatedTimeIcon: String? = null,
        progress: List<MemberOnboardingProgress>? = null
    ) = MemberOnboardingStep(
        title = title,
        key = key,
        description = description,
        imageUrl = imageUrl,
        actions = actions,
        next = next,
        optIns = optIns,
        memberOnboardingTemplateId = memberOnboardingTemplateId,
        healthFormNavigation = healthFormNavigation,
        type = type,
        id = id,
        widgetComponent = widgetComponent,
        event = event,
        format = format,
        videoUrl = videoUrl,
        videoCaptionUrl = videoCaptionUrl,
        showEmergencyButton = showEmergencyButton,
        forceVideoFolder = forceVideoFolder,
        estimatedTimeIcon = estimatedTimeIcon,
        estimatedTimeText = estimatedTimeText,
        progress = progress,
    )

    fun buildMemberOnboardingAction(
        name: String = "",
        icon: String? = null,
        actionUrl: String = "",
        id: UUID = RangeUUID.generate(),
        label: String = "",
        isBackVisible: Boolean = true,
        listenAppState: MemberOnboardingAction.MemberOnboardingActionAppState? = null
    ) = MemberOnboardingAction(
        name = name,
        icon = icon,
        actionUrl = actionUrl,
        id = id,
        label = label,
        isBackVisible = isBackVisible,
        listenAppState = listenAppState,
    )

    fun buildMemberOnboardingCheckpoint(
        personId: PersonId = PersonId(),
        currentStepId: UUID = RangeUUID.generate(),
        memberOnboardingTemplateId: UUID = RangeUUID.generate(),
        id: UUID = RangeUUID.generate(),
        status: MemberOnboardingCheckpointStatus = MemberOnboardingCheckpointStatus.STARTED,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        riskDuringStratification: MemberOnboardingCheckpoint.MemberOnboardingCheckpointStratificationRisk? = null,
        referralTaskId: UUID? = null,
    ) = MemberOnboardingCheckpoint(
        personId = personId,
        currentStepId = currentStepId,
        memberOnboardingTemplateId = memberOnboardingTemplateId,
        id = id,
        status = status,
        createdAt = createdAt,
        updatedAt = updatedAt,
        riskDuringStratification = riskDuringStratification,
        referralTaskId = referralTaskId
    )

    fun buildProviderUnit(
        type: ProviderUnit.Type = ProviderUnit.Type.HOSPITAL,
        name: String = "Oswaldo Cruz Paulista",
        contractOrigin: ProviderUnit.Origin = ProviderUnit.Origin.ALICE,
        providerUnitGroupId: UUID? = RangeUUID.generate(),
        id: UUID = RangeUUID.generate(),
        providerId: UUID = RangeUUID.generate(),
        cnpj: String? = "**********",
        cnes: String? = null,
        bankCode: String? = "341",
        accountNumber: String? = "01212-3",
        agencyNumber: String? = "1720",
        brand: Brand = Brand.ALICE,
        medicalSpecialtyProfile: List<MedicalSpecialtyProfile>? = emptyList(),
        urlSlug: String? = null,
        status: Status = Status.ACTIVE,
        imageUrl: String? = "http://pudim.com.br/logo.png",
        attendanceTypes: List<ProviderUnit.AttendanceType>? = listOf(ProviderUnit.AttendanceType.EMERGENCY_CARE),
        hasHospitalHealthTeam: Boolean = false,
        clinicalStaffIds: List<UUID>? = emptyList(),
        phones: List<PhoneNumber> = listOf(
            PhoneNumber(phone = "***********"),
            PhoneNumber(title = "Secundário", phone = "***********")
        )
    ) = ProviderUnit(
        id = id,
        type = type,
        name = name,
        contractOrigin = contractOrigin,
        site = "www.hospitaloswaldocruz.org.br",
        workingPeriods = ProviderUnit.WorkingPeriod.ALWAYS_OPEN,
        cnpj = cnpj,
        imageUrl = imageUrl,
        phones = phones,
        qualifications = listOf(Qualification.ISO_9001),
        providerId = providerId,
        providerUnitGroupId = providerUnitGroupId,
        cnes = cnes,
        bankCode = bankCode,
        accountNumber = accountNumber,
        agencyNumber = agencyNumber,
        brand = brand,
        medicalSpecialtyProfile = medicalSpecialtyProfile,
        urlSlug = urlSlug,
        status = status,
        attendanceTypes = attendanceTypes,
        hasHospitalHealthTeam = hasHospitalHealthTeam,
        clinicalStaffIds = clinicalStaffIds
    )

    fun buildProviderUnitGroup(
        title: String = "Oswaldo Cruz Paulista",
        id: UUID = RangeUUID.generate(),
        cnpj: String = "1234"
    ) = ProviderUnitGroup(
        id = id,
        title = title,
        cnpj = cnpj
    )

    fun buildMedicine() =
        Medicine(
            ean = "*************",
            tissCmed = "**********",
            name = "ZIAGENAVIR",
            presentation = "300 mg. 60 caps.",
            concentration = "300",
            unit = "MG",
            pharmaceuticalForm = "CAPSULA",
            routeOfAdministration = "VO",
            quantityPerPackage = "60",
            drugId = 5000,
            drugName = "ABACAVIR",
            atcCode = "J05AF06",
            medicineType = "REFERENCIA",
            mip = false,
            portaria344 = "LISTA - C4"
        )

    fun buildDeadletterQueue(
        eventId: UUID = RangeUUID.generate(),
        topic: String = "topic",
        producer: String = "producer",
        payload: String = "payload",
    ) = DeadletterQueue(
        eventId = eventId,
        topic = topic,
        producer = producer,
        payload = payload,
        errorMessage = "error-message",
        stackTrace = "any stack trace",
    )

    fun buildAssistanceCare(appointmentId: UUID, personId: PersonId) =
        AssistanceCare(
            appointmentId = appointmentId,
            personId = personId,
            ticketId = 12345,
            clinicalEvaluation = "Good",
            clinicalEvaluationCodes = listOf(Disease(CIAP_2, "R21", "Disease R21 - CIAP R21")),
            outcome = Outcome.ATTENDANCE_OR_DIGITAL_PRESCRIPTION
        )

    fun buildAliceAgoraWorkingHours() =
        AliceAgoraWorkingHours(
            workingHours = listOf(
                WorkingHours(
                    dayOfTheWeek = WeekDay.MONDAY,
                    opens = LocalTime.of(9, 0),
                    closes = LocalTime.of(20, 0)
                ),
                WorkingHours(
                    dayOfTheWeek = WeekDay.TUESDAY,
                    opens = LocalTime.of(9, 0),
                    closes = LocalTime.of(20, 0)
                ),
                WorkingHours(
                    dayOfTheWeek = WeekDay.WEDNESDAY,
                    opens = LocalTime.of(9, 0),
                    closes = LocalTime.of(20, 0)
                ),
                WorkingHours(
                    dayOfTheWeek = WeekDay.THURSDAY,
                    opens = LocalTime.of(9, 0),
                    closes = LocalTime.of(20, 0)
                ),
                WorkingHours(
                    dayOfTheWeek = WeekDay.FRIDAY,
                    opens = LocalTime.of(9, 0),
                    closes = LocalTime.of(20, 0)
                ),
                WorkingHours(
                    dayOfTheWeek = WeekDay.SATURDAY,
                    opens = LocalTime.of(10, 0),
                    closes = LocalTime.of(16, 0)
                )
            ),
            chatDescription = ChatDescription(
                online = AvailabilityTexts(
                    hubAvailability = "Tempo de resposta: imediato",
                    hubDescription = "Emergências ou dúvidas sobre a sua saúde",
                    headerDescription = "Olá! Emergência ou mesmo alguma dúvida de saúde? Conta pra gente em que podemos te ajudar que vamos responder na mesma hora {ou seja, agora}."
                ),
                offline = AvailabilityTexts(
                    hubAvailability = "Funcionamos de segunda a sexta, das 7 as 21",
                    hubDescription = "Ops, não estamos em atendimento agora. Manda mensagem que respondemos assim que possível",
                    headerDescription = "Olá! Emergência ou mesmo alguma dúvida de saúde? Conta pra gente em que podemos te ajudar que respondemos assim que possível"
                )
            ),
            overrides = listOf(
                Overrides(
                    day = LocalDate.of(2020, 9, 7),
                    opens = LocalTime.of(10, 0),
                    closes = LocalTime.of(14, 0),
                    chatDescription = ChatDescription(
                        online = AvailabilityTexts(
                            hubAvailability = "Tempo de resposta: até 30 min",
                            hubDescription = "Emergências ou dúvidas sobre a sua saúde",
                            headerDescription = "Olá! Emergência ou mesmo alguma dúvida de saúde? Conta pra gente em que podemos te ajudar que vamos responder na mesma hora {ou seja, agora}."
                        ),
                        offline = AvailabilityTexts(
                            hubAvailability = "Funcionaremos a partir de segunda as 7",
                            hubDescription = "Ops, não estamos em atendimento agora. Manda mensagem que respondemos assim que possível",
                            headerDescription = "Conta pra gente em que podemos te ajudar que vamos te responder segunda {e desejamos um ótimo natal!}"
                        )
                    )
                )
            )
        )

    fun buildAdministrativeAliceAgoraWorkingHours() =
        AliceAgoraWorkingHours(
            category = CategoryType.ADMINISTRATIVE,
            workingHours = listOf(
                WorkingHours(
                    dayOfTheWeek = WeekDay.FRIDAY,
                    opens = LocalTime.of(9, 0),
                    closes = LocalTime.of(20, 0)
                ),
                WorkingHours(
                    dayOfTheWeek = WeekDay.SATURDAY,
                    opens = LocalTime.of(10, 0),
                    closes = LocalTime.of(16, 0)
                )
            ),
            chatDescription = ChatDescription(
                online = AvailabilityTexts(
                    hubAvailability = "Tempo de resposta: imediato",
                    hubDescription = "Emergências ou dúvidas sobre a sua saúde",
                    headerDescription = "Olá! Emergência ou mesmo alguma dúvida de saúde? Conta pra gente em que podemos te ajudar que vamos responder na mesma hora {ou seja, agora}."
                ),
                offline = AvailabilityTexts(
                    hubAvailability = "Funcionamos de segunda a sexta, das 7 as 21",
                    hubDescription = "Ops, não estamos em atendimento agora. Manda mensagem que respondemos assim que possível",
                    headerDescription = "Olá! Emergência ou mesmo alguma dúvida de saúde? Conta pra gente em que podemos te ajudar que respondemos assim que possível"
                )
            ),
            overrides = listOf(
                Overrides(
                    day = LocalDate.of(2020, 9, 7),
                    opens = LocalTime.of(10, 0),
                    closes = LocalTime.of(14, 0),
                    chatDescription = ChatDescription(
                        online = AvailabilityTexts(
                            hubAvailability = "Tempo de resposta: até 30 min",
                            hubDescription = "Emergências ou dúvidas sobre a sua saúde",
                            headerDescription = "Olá! Emergência ou mesmo alguma dúvida de saúde? Conta pra gente em que podemos te ajudar que vamos responder na mesma hora {ou seja, agora}."
                        ),
                        offline = AvailabilityTexts(
                            hubAvailability = "Funcionaremos a partir de segunda as 7",
                            hubDescription = "Ops, não estamos em atendimento agora. Manda mensagem que respondemos assim que possível",
                            headerDescription = "Conta pra gente em que podemos te ajudar que vamos te responder segunda {e desejamos um ótimo natal!}"
                        )
                    )
                )
            )
        )

    fun buildFeatureConfig(
        namespace: FeatureNamespace = EHR,
        key: String = "feature_config_key",
        type: FeatureType = FeatureType.BOOLEAN,
        value: String = "true",
        description: String = "feature_config_description",
        active: Boolean = true,
        isPublic: Boolean = false,
        id: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) =
        FeatureConfig(
            id = id,
            namespace = namespace,
            key = key,
            type = type,
            value = value,
            description = description,
            active = active,
            isPublic = isPublic,
            createdAt = createdAt,
            updatedAt = updatedAt
        )

    fun buildTrackPersonAB(
        personId: PersonId = PersonId(),
        featureConfigId: UUID = RangeUUID.generate(),
        abPath: String = "abPath",
        id: UUID = RangeUUID.generate(),
        startedAt: LocalDateTime = LocalDateTime.now()
    ) =
        TrackPersonAB(
            id = id,
            personId = personId,
            featureConfigId = featureConfigId,
            abPath = abPath,
            startedAt = startedAt
        )

    fun buildContractSignature(person: Person) = ContractSignature(
        id = RangeUUID.generate(),
        idToken = "238289372987323",
        fullName = person.fullRegisterName,
        nationalId = person.nationalId,
        ipAddress = "*********.1",
        signedAt = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME),
        productContract = ProductContract(
            name = "Product #1",
            ansNumber = "43212"
        )
    )

    fun buildOnboardingContract(
        personId: PersonId = PersonId(),
        certificateUrl: String? = null,
        product: Product = buildProduct(),
        documentUrl: String = "https://s3.alice.com.br/sample.pdf",
        memberProduct: MemberProduct = MemberProduct(product.id, product.prices, product.type)
    ) = OnboardingContract(
        personId = personId,
        selectedProduct = memberProduct,
        certificateUrl = certificateUrl,
        documentUrl = documentUrl,
    )

    fun buildDbLaboratoryTestResult(
        personId: PersonId = PersonId(),
        attendanceId: String = "attendanceId",
        dataHoraLiberacaoClinica: LocalDateTime? = LocalDateTime.now(),
        listaResultadoText: List<DbResultadoTexto> = listOf(),
        items: List<DbResultadoProcedimento> = listOf(
            buildDbResultadoProcedimento(
                dataHoraLiberacaoClinica,
                listaResultadoText
            )
        ),
    ) = DbLaboratoryTestResult(
        attendanceId = attendanceId,
        personId = personId,
        items = items
    )

    fun buildDbResultadoProcedimento(
        dataHoraLiberacaoClinica: LocalDateTime? = LocalDateTime.now(),
        listaResultadoText: List<DbResultadoTexto> = listOf(),
    ) = DbResultadoProcedimento(
        codigoExameDB = "testResultCode",
        versaoLaudo = "",
        descricaoMetodologia = "",
        descricaoRegiaoColeta = "",
        dataHoraLiberacaoClinica = dataHoraLiberacaoClinica,
        nomeLiberadorClinico = "nome_liberador_clinico",
        observacao1 = "",
        observacao2 = "",
        observacao3 = "",
        observacao4 = "",
        observacao5 = "",
        material = "",
        identificacaoExameApoiado = "",
        materialApoiado = "",
        descricaoMaterialApoiado = "",
        descricaoExameApoiado = "",
        listaResultadoText = listaResultadoText
    )

    fun buildDbResultadoTexto(
        codigoParametroDB: String = "codigoParametroDB",
        descricaoParametroDB: String? = null,
        valorResultado: String? = null,
        unidadeMedida: String? = null,
        valorReferencia: String? = null,
    ) = DbResultadoTexto(
        codigoParametroDB = codigoParametroDB,
        descricaoParametroDB = descricaoParametroDB,
        valorResultado = valorResultado,
        unidadeMedida = unidadeMedida,
        valorReferencia = valorReferencia
    )

    fun buildDbLaboratoryTestResultProcess(
        claimId: Int = 12345,
        status: DbLaboratoryTestResultProcessType = DbLaboratoryTestResultProcessType.PENDING,
        personId: PersonId = PersonId(),
        testRequestTags: List<String> = emptyList(),
        executionGroupId: UUID? = null,
    ) =
        DbLaboratoryTestResultProcess(
            attendanceId = "attendanceId",
            claimId = claimId,
            status = status,
            personId = personId,
            testRequestTags = testRequestTags,
            executionGroupId = executionGroupId
        )

    fun buildChannelTag(
        name: String = "Tag 1",
        categories: List<ChannelCategory>? = emptyList()
    ) = ChannelTag(
        name,
        categories
    )

    fun buildOnboardingBackgroundCheck(personId: PersonId) = OnboardingBackgroundCheck(
        personId = personId
    )

    fun buildChannelHealthConditionIgnore(
        healthConditionId: UUID = RangeUUID.generate(),
        code: String = "P013",
        codeType: HealthConditionCodeType = HealthConditionCodeType.CID_10,
    ) = ChannelHealthConditionIgnore(
        healthConditionId = healthConditionId,
        code = code,
        codeType = codeType
    )

    fun buildLead(
        nationalId: String? = "61918684057",
        email: String = "<EMAIL>",
        firstName: String = "José",
        lastName: String = "da Silva",
        phoneNumber: String = "11777333222",
        postalCode: String = "04222-202",
        dateOfBirth: LocalDateTime = LocalDateTime.now().minusYears(32),
        declaredAge: Int? = null,
        promoCodeId: UUID? = null,
        nickName: String? = "Zeca",
        source: LeadSource = LeadSource.SIMULATION,
        sourceId: UUID? = RangeUUID.generate(),
        id: UUID = RangeUUID.generate(),
        invitedAt: LocalDateTime? = null,
        simulationHistory: List<UUID> = emptyList(),
        trackingInfo: TrackingInfo? = null,
        zipcodeAddressLight: ZipcodeAddressLight? = null,
    ) = Lead(
        nationalId = nationalId,
        email = email,
        firstName = firstName,
        lastName = lastName,
        phoneNumber = phoneNumber,
        postalCode = postalCode,
        dateOfBirth = dateOfBirth,
        declaredAge = declaredAge,
        authorizeCommunication = true,
        promoCodeId = promoCodeId,
        nickName = nickName,
        source = source,
        sourceId = sourceId,
        id = id,
        invitedAt = invitedAt,
        simulationHistory = simulationHistory,
        trackingInfo = trackingInfo,
        zipcodeAddress = zipcodeAddressLight,
    )

    fun buildPersonAdditionalInfo(personId: PersonId) =
        PersonAdditionalInfo(
            personId = personId,
            emergencyContactName = "João",
            emergencyContactRelationship = "Pai",
            emergencyContactEmail = "<EMAIL>",
            emergencyContactPhone = "119*********",
            children = 0,
            education = "Superior completo",
            occupation = "Eng da F1",
            relationship = "Solteiro",
            livesWith = "Pais e Irmão"
        )

    fun buildCuriosityNote(personId: PersonId) =
        CuriosityNote(personId = personId, curiosity = "Joga xadrez")

    fun buildFleuryProcess(
        claimId: String? = "claimId",
        status: FleuryProcessType = FleuryProcessType.PENDING,
        personId: PersonId = PersonId(),
        executionGroupId: UUID? = RangeUUID.generate(),
        totvsGuiaId: UUID? = RangeUUID.generate(),
        lastProcessAt: LocalDateTime? = null,
    ) = FleuryProcess(
        claimId = claimId,
        personId = personId,
        status = status,
        executionGroupId = executionGroupId,
        totvsGuiaId = totvsGuiaId,
        lastProcessAt = lastProcessAt
    )

    fun buildFleuryResult(
        personId: PersonId = PersonId(),
        fileId: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) = FleuryTestResultFile(
        personId = personId,
        createdAt = createdAt,
        idFicha = "id-da-ficha",
        idItem = "id-item",
        siglaExame = "HM",
        fileId = fileId
    )

    fun buildFleuryTestResult(
        personId: PersonId = PersonId(),
        idFicha: String = "idFicha",
        idItem: String = "idItem",
        idProduto: String = "1012",
        idUnidade: String = "1310",
        claimId: String = "claimId",
        procedimento: String = "procedimento",
        laudos: List<FleuryLaudo> = emptyList(),
        laudoFormatado: String? = null,
        type: FleuryResultType = FleuryResultType.STRUCTURED
    ) = FleuryTestResult(
        personId = personId,
        idFicha = idFicha,
        idProduto = idProduto,
        idUnidade = idUnidade,
        idItem = idItem,
        claimId = claimId,
        procedimento = procedimento,
        laudos = laudos,
        laudoFormatado = laudoFormatado,
        type = type
    )

    fun buildFleuryLaudo(
        analitoId: String = "analitoId",
        nome: String = "nome",
        nomeReduzido: String = "nomeReduzido",
        unidadeMedida: String? = null,
        listaFaixaReferenciaCritica: List<FaixaReferenciaCritica>? = emptyList(),
        listaFaixaReferenciaNormal: List<FaixaReferenciaNormal>? = emptyList(),
        resultado: String = "resultado",
        indicativoVisualizacao: Boolean = false,
        codigoCondicaoResultado: String = "codigoCondicaoResultado",
        codigoTipoResultado: String = "codigoTipoResultadog",
    ) = FleuryLaudo(
        analitoId = analitoId,
        nome = nome,
        nomeReduzido = nomeReduzido,
        unidadeMedida = unidadeMedida,
        listaFaixaReferenciaCritica = listaFaixaReferenciaCritica,
        listaFaixaReferenciaNormal = listaFaixaReferenciaNormal,
        resultado = resultado,
        indicativoVisualizacao = indicativoVisualizacao,
        codigoCondicaoResultado = codigoCondicaoResultado,
        codigoTipoResultado = codigoTipoResultado,
    )

    fun buildPersonTask(
        personId: PersonId = PersonId(),
        title: String = "title",
        description: String? = null,
        type: TaskType = TaskType.TEST_REQUEST,
        status: TaskStatus = TaskStatus.WAITING_REVIEW,
        attachmentIds: List<UUID> = emptyList(),
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) = PersonTask(
        personId = personId,
        title = title,
        description = description,
        type = type,
        status = status,
        attachmentIds = attachmentIds,
        createdAt = createdAt
    )

    fun buildHealthGoal(
        name: String = "Quero cuidar de um problema de saúde",
        imageUrl: String = "https://alice-member-app-assets.s3.amazonaws.com/health_goals/health_issue.png",
    ) = HealthGoal(
        name = name,
        imageUrl = imageUrl,
        active = true
    )

    fun buildPersonHealthGoal(
        personId: PersonId = PersonId(),
        healthGoalIds: List<UUID> = listOf(RangeUUID.generate(), RangeUUID.generate()),
    ) = PersonHealthGoal(
        personId = personId,
        healthGoalIds = healthGoalIds
    )

    fun buildPrescriptionSentence(tokens: List<String> = listOf("Amoxilina 300 MG")) =
        PrescriptionSentence(
            tokens = tokens,
            dose = 1f,
            unit = CAPSULE,
            routeOfAdministration = RouteOfAdministration.ORAL,
            action = ActionType.TAKE,
            start = StartType.IMMEDIATE,
            frequency = Frequency(type = FrequencyType.QUANTITY_IN_PERIOD, unit = PeriodUnit.HOUR, quantity = 8),
            deadline = Deadline(unit = PeriodUnit.DAY, quantity = 7)
        )

    fun buildTrackingInfo(
        origin: TrackingOrigin = TrackingOrigin.SITE,
        utmSource: String? = "source",
        utmCampaign: String? = "campaign",
        utmCampaignName: String? = "campaign-name",
        utmCampaignId: String? = "campaign-id",
        utmAdSet: String? = "ad-set",
        utmAdSetId: String? = "cad-set-id",
        utmAdId: String? = "ad-id",
        utmContent: String? = "content",
        utmMedium: String? = "medium",
        utmTerm: String? = "term",
        steps: String? = "x-y-z",
        tests: List<LeadTest>? = null,
        testsMap: Map<LeadTest, Boolean>? = null
    ) = TrackingInfo(
        origin = origin,
        utmSource = utmSource,
        utmCampaign = utmCampaign,
        utmCampaignName = utmCampaignName,
        utmCampaignId = utmCampaignId,
        utmAdSet = utmAdSet,
        utmAdSetId = utmAdSetId,
        utmAdId = utmAdId,
        utmContent = utmContent,
        utmMedium = utmMedium,
        utmTerm = utmTerm,
        steps = steps,
        tests = tests,
        testsMap = testsMap,
    )

    fun buildExecIndicatorAuthorizer(
        id: UUID = RangeUUID.generate(),
        providerUnitId: UUID = RangeUUID.generate(),
        domain: String = "alice.com.br",
        mvProviderId: Int = Prestador.DB.code,
        mvCdLocalPrestador: Int = 0,
        mvCdLocal: Int = 0,
        passwordKey: String = "",
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) =
        ExecIndicatorAuthorizer(
            id = id,
            providerUnitId = providerUnitId,
            domain = domain,
            mvCdPrestador = mvProviderId,
            createdAt = createdAt,
            updatedAt = updatedAt,
            mvCdLocalPrestador = mvCdLocalPrestador,
            mvCdLocal = mvCdLocal,
            passwordKey = passwordKey
        )

    fun buildExecutionGroup(
        id: UUID = RangeUUID.generate(),
        providerUnitId: UUID = RangeUUID.generate(),
        code: String = ExecutionGroup.generateRandomCode(),
        userEmail: String = "<EMAIL>",
        tag: String? = "tag"
    ) = ExecutionGroup(
        id = id,
        code = code,
        userEmail = userEmail,
        tags = tag?.let { listOf(it) } ?: emptyList(),
        providerUnitId = providerUnitId
    )

    fun buildDiseaseClinicalBackground(
        id: UUID = RangeUUID.generate(),
        type: ClinicalBackgroundType = ClinicalBackgroundType.DISEASE,
        diseaseType: DiseaseBackground.Type = DiseaseBackground.Type.CID_10,
        value: String? = "CID123",
        description: String? = null,
        status: ClinicalBackgroundStatus = ClinicalBackgroundStatus.ACTIVE,
        denied: Boolean = false,
        addedByStaffId: UUID? = RangeUUID.generate(),
        addedAt: LocalDateTime = LocalDateTime.now(),
        appointmentId: UUID? = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        deletedByStaffId: UUID? = null,
        deletedAt: LocalDateTime? = null,
    ) = ClinicalBackground(
        id = id,
        personId = personId,
        type = type,
        content = mapOf("diseaseType" to diseaseType, "value" to value, "description" to description),
        status = status,
        denied = denied,
        addedByStaffId = addedByStaffId,
        addedAt = addedAt,
        deletedByStaffId = deletedByStaffId,
        deletedAt = deletedAt,
        appointmentId = appointmentId,
    )

    fun buildUnstructuredClinicalBackground(
        id: UUID = RangeUUID.generate(),
        type: ClinicalBackgroundType = ClinicalBackgroundType.DISEASE,
        content: Map<String, Any?> = emptyMap(),
        status: ClinicalBackgroundStatus = ClinicalBackgroundStatus.ACTIVE,
        denied: Boolean = false,
        addedByStaffId: UUID = RangeUUID.generate(),
        addedAt: LocalDateTime = LocalDateTime.now(),
        appointmentId: UUID? = null,
        personId: PersonId = PersonId(),
        deletedByStaffId: UUID? = null,
        deletedAt: LocalDateTime? = null,
    ) = ClinicalBackground(
        id = id,
        personId = personId,
        type = type,
        content = content,
        status = status,
        denied = denied,
        addedByStaffId = addedByStaffId,
        addedAt = addedAt,
        deletedByStaffId = deletedByStaffId,
        deletedAt = deletedAt,
        appointmentId = appointmentId,
    )

    fun buildDietClinicalBackground(
        id: UUID = RangeUUID.generate(),
        type: ClinicalBackgroundType = ClinicalBackgroundType.DIET,
        dietType: DietBackground.DietType = DietBackground.DietType.VEGAN,
        status: ClinicalBackgroundStatus = ClinicalBackgroundStatus.ACTIVE,
        denied: Boolean = false,
        addedByStaffId: UUID = RangeUUID.generate(),
        addedAt: LocalDateTime = LocalDateTime.now(),
        appointmentId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        deletedByStaffId: UUID? = null,
        deletedAt: LocalDateTime? = null,
    ) = ClinicalBackground(
        id = id,
        personId = personId,
        type = type,
        content = mapOf("value" to dietType),
        status = status,
        denied = denied,
        addedByStaffId = addedByStaffId,
        addedAt = addedAt,
        deletedByStaffId = deletedByStaffId,
        deletedAt = deletedAt,
        appointmentId = appointmentId,
    )

    fun buildHealthPlanTask(
        personId: PersonId = PersonId(),
        healthPlanId: UUID? = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        content: Map<String, Any>? = mapOf("professional" to ProfessionalType.HEALTH_CARE_TEAM_PHYSICIAN.name),
        date: LocalDateTime = LocalDateTime.now(),
        dueDate: LocalDate? = LocalDate.now(),
        type: HealthPlanTaskType = HealthPlanTaskType.REFERRAL,
        frequency: Frequency? = null,
        deadline: Deadline? = null,
        start: StartType? = null,
        groupId: UUID? = null,
        id: UUID = RangeUUID.generate(),
        status: HealthPlanTaskStatus = HealthPlanTaskStatus.DRAFT,
        title: String = "Ir ao geriatra",
        description: String = "Descrição",
        initiatedByMemberAt: LocalDateTime? = null,
        finishedBy: TaskUpdatedBy? = null,
        acknowledgedAt: LocalDateTime? = null,
        appointmentId: UUID? = null,
        caseRecordDetails: List<CaseRecordDetails> = emptyList(),
        caseId: UUID? = null,
        releasedAt: LocalDateTime? = null,
        createdBy: TaskUpdatedBy? = null
    ) =
        HealthPlanTask(
            id = id,
            personId = personId,
            healthPlanId = healthPlanId,
            title = title,
            description = description,
            content = content,
            dueDate = dueDate,
            status = status,
            lastRequesterStaffId = staffId,
            requestersStaffIds = setOf(staffId),
            type = type,
            createdAt = date,
            updatedAt = date,
            frequency = frequency,
            deadline = deadline,
            start = start?.let { Start(type = start, date = date) },
            groupId = groupId,
            releasedByStaffId = staffId,
            initiatedByMemberAt = initiatedByMemberAt,
            favorite = false,
            finishedBy = finishedBy,
            acknowledgedAt = acknowledgedAt,
            appointmentId = appointmentId,
            caseRecordDetails = caseRecordDetails,
            caseId = caseId,
            releasedAt = releasedAt,
            createdBy = createdBy
        )

    fun buildHealthPlanTaskPrescription(
        personId: PersonId = PersonId(),
        healthPlanId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        groupId: UUID? = null,
        withDigitalPrescription: Boolean = false,
        appointmentId: UUID? = null,
    ) = buildHealthPlanTask(
        personId = personId,
        staffId = staffId,
        healthPlanId = healthPlanId,
        content = buildPrescriptionContent(withDigitalPrescription),
        type = HealthPlanTaskType.PRESCRIPTION,
        frequency = Frequency(
            type = FrequencyType.QUANTITY_IN_PERIOD,
            unit = PeriodUnit.DAY,
            quantity = 4
        ),
        deadline = Deadline(unit = PeriodUnit.DAY, quantity = 15),
        start = StartType.IMMEDIATE,
        groupId = groupId,
        appointmentId = appointmentId
    )

    private fun buildPrescriptionContent(
        withDigitalPrescription: Boolean
    ) = if (withDigitalPrescription) {
        buildBasicPrescriptionContent()
            .plus(
                "digitalPrescription" to mapOf(
                    "link" to "https://awesomeprescriptions.com/123abc",
                    "digits" to "xyz999",
                    "documentLink" to ""
                )
            )
    } else {
        buildBasicPrescriptionContent()
    }

    private fun buildBasicPrescriptionContent() = mapOf(
        "dose" to mapOf(
            "unit" to CAPSULE.name,
            "quantity" to 1.0
        ),
        "action" to ActionType.TAKE.name,
        "routeOfAdministration" to RouteOfAdministration.ORAL.name,
        "medicine" to mapOf(
            "name" to "Amoxil",
            "unit" to CAPSULE.name,
            "quantity" to "15",
            "concentration" to "mg",
            "concentrationQuantity" to "500",
            "drug" to "Amoxilina",
            "type" to PrescriptionMedicineType.SIMPLE.name,
            "id" to RangeUUID.generate().toString(),
            "drugId" to Random.nextInt(1000, 10000)
        ),
        "packing" to 1
    )

    fun buildHealthcareResource(
        code: String = "123456",
        tussCode: String? = null,
        description: String = "description",
        searchTokens: String? = null,
        version: Int = 0,
        nrol: Boolean = false,
        dut: Boolean = false,
        pac: Boolean = false,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        id: UUID = RangeUUID.generate(),
        type: HealthcareResourceType = HealthcareResourceType.EXAM,
        category: HealthcareResourceCategory = HealthcareResourceCategory.HEALTH_INSTITUTION,
        tableType: String = "22",
        compositionHash: String? = "123",
        active: Boolean = true,
    ) = HealthcareResource(
        code = code,
        tussCode = tussCode,
        description = description,
        searchTokens = searchTokens,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        nrol = nrol,
        dut = dut,
        pac = pac,
        id = id,
        type = type,
        category = category,
        tableType = tableType,
        compositionHash = compositionHash,
        active = active,
    )

    fun buildMvAuthorizedProcedure(
        id: UUID = RangeUUID.generate(),
        executionGroupId: UUID? = null,
        personId: PersonId = PersonId(),
        procedureId: String? = "procedure-id",
        executedAt: LocalDateTime? = null,
        executionRequestedAt: LocalDateTime? = null,
        testRequestId: UUID? = null,
        dbFailureStatus: FailureReason? = null,
        dbFailureDescription: String? = null,
        authorizationRequestedAt: LocalDateTime? = null,
        authorizedAt: LocalDateTime? = null,
        status: MvAuthorizedProcedureStatus = MvAuthorizedProcedureStatus.ACTIVE,
        executorId: UUID? = null,
        executedByProviderUnitId: UUID? = null,
        executorCnpj: String? = null,
        extraGuiaInfo: ExtraGuiaInfo = ExtraGuiaInfo(),
        dataIntegration: List<DataIntegration> = emptyList(),
        authorizedByStaff: UUID? = null,
        healthEventId: UUID? = null,
        opmeId: String? = null,
        createdByAuthorizer: UUID? = null,
        requestedByProfessional: ProfessionalIdentification = ProfessionalIdentification.DEFAULT_PROFESSIONAL_IDENTIFICATION,
        origin: MvAuthorizedProcedureOrigin? = null,
        totvsGuiaId: UUID? = null,
        gloss: List<GlossAuthorizationInfoData>? = emptyList(),
        guiaExecutionCode: String? = null,
        quantity: Int = 1,
        type: MvUtil.TISS = MvUtil.TISS.EXAM,
    ) = MvAuthorizedProcedure(
        id = id,
        personId = personId,
        procedureId = procedureId,
        testRequestId = testRequestId,
        authorizationRequestedAt = authorizationRequestedAt,
        authorizedAt = authorizedAt,
        executionGroupId = executionGroupId,
        executedAt = executedAt,
        executionRequestedAt = executionRequestedAt,
        dbFailureStatus = dbFailureStatus,
        dbFailureDescription = dbFailureDescription,
        status = status,
        executorId = executorId,
        executedByProviderUnitId = executedByProviderUnitId,
        executorCnpj = executorCnpj,
        extraGuiaInfo = extraGuiaInfo,
        dataIntegration = dataIntegration,
        authorizedByStaff = authorizedByStaff,
        healthEventId = healthEventId,
        opmeId = opmeId,
        createdByAuthorizer = createdByAuthorizer,
        requestedByProfessional = requestedByProfessional,
        origin = origin,
        totvsGuiaId = totvsGuiaId,
        gloss = gloss,
        guiaExecutionCode = guiaExecutionCode,
        quantity = quantity,
        type = type
    )

    fun buildPromoCode(
        code: String = "FEL908",
        type: PromoCodeType = PromoCodeType.MEMBER_GET_MEMBER,
        personId: PersonId? = null,
        description: String? = null,
        id: UUID = RangeUUID.generate(),
    ) = PromoCode(
        id = id,
        code = code,
        ownerPersonId = personId,
        type = type,
        enabled = true,
        description = description
    )

    fun buildCboCode(
        code: String = "12345",
        description: String = "Médico Ortopedista",
    ) = CboCode(
        code = code,
        description = description,
    )

    fun buildMedicalSpecialty(
        name: String = "Ortopedia",
        type: MedicalSpecialtyType = MedicalSpecialtyType.SPECIALTY,
        parentSpecialtyId: UUID? = null,
        requireSpecialist: Boolean = false,
        generateGeneralistSubSpecialty: Boolean = false,
        internal: Boolean = false,
        id: UUID = RangeUUID.generate(),
        urlSlug: String = "url-slug",
        active: Boolean = true,
        attentionLevel: AttentionLevel = AttentionLevel.SECONDARY,
        isTherapy: Boolean = false,
        cboCode: MedicalSpecialty.CboCode? = MedicalSpecialty.CboCode(RangeUUID.generate(), "1122", "description"),
        isAdvancedAccess: Boolean = false,
    ) = MedicalSpecialty(
        id = id,
        name = name,
        type = type,
        parentSpecialtyId = parentSpecialtyId,
        requireSpecialist = requireSpecialist,
        generateGeneralistSubSpecialty = generateGeneralistSubSpecialty,
        internal = internal,
        urlSlug = urlSlug,
        active = active,
        attentionLevel = attentionLevel,
        isTherapy = isTherapy,
        cboCode = cboCode,
        isAdvancedAccess = isAdvancedAccess
    )

    fun buildInsurancePortabilityRequest(
        personId: PersonId = PersonId(),
        status: InsurancePortabilityRequestStatus = InsurancePortabilityRequestStatus.CREATED,
        answers: List<InsurancePortabilityRequestAnswer> = emptyList(),
        answersV2: List<InsurancePortabilityRequestAnswerV2> = emptyList(),
        missingDocuments: List<InsurancePortabilityMissingDocumentFile> = emptyList(),
        declineReason: InsurancePortabilityRequestDeclineReason? = null,
        notes: String? = null,
        productId: UUID? = null,
        type: InsurancePortabilityRequestType? = InsurancePortabilityRequestType.NORMAL,
        step: InsurancePortabilityRequestStep? = InsurancePortabilityRequestStep.HAS_HEALTH_INSURANCE,
        adhesionContract: Boolean = false,
        hasCpt: Boolean? = null,
        hasFulfilledCpt: Boolean? = null,
        paymentFulfillmentRequirement: Boolean? = null,
        minimumTermRequirement: Boolean? = null,
        priceCompatibilityRequirement: Boolean? = null,
        activePlanRequirement: Boolean? = null,
        declinedReasons: List<InsurancePortabilityRequestDeclineReason> = emptyList(),
        currentTermPeriod: InsurancePortabilityRequestCurrentTermPeriod? = null,
        pendingAt: LocalDateTime? = null,
        finishedAt: LocalDateTime? = null,
        archived: Boolean = false,
    ) = InsurancePortabilityRequest(
        personId = personId,
        productId = productId,
        status = status,
        answers = answers,
        answersV2 = answersV2,
        declineReason = declineReason,
        missingDocuments = missingDocuments,
        type = type,
        notes = notes,
        step = step,
        adhesionContract = adhesionContract,
        hasCpt = hasCpt,
        hasFulfilledCpt = hasFulfilledCpt,
        paymentFulfillmentRequirement = paymentFulfillmentRequirement,
        minimumTermRequirement = minimumTermRequirement,
        priceCompatibilityRequirement = priceCompatibilityRequirement,
        activePlanRequirement = activePlanRequirement,
        declinedReasons = declinedReasons,
        currentTermPeriod = currentTermPeriod,
        pendingAt = pendingAt,
        finishedAt = finishedAt,
        archived = archived,
    )

    fun buildInsurancePortabilityRequestAnswer(
        answer: String = "42",
        questionType: InsurancePortabilityRequestQuestionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD,
    ) = InsurancePortabilityRequestAnswer(
        answer = answer,
        questionType = questionType
    )

    fun buildInsurancePortabilityRequestAnswerV2(
        answer: String = "42",
        questionType: InsurancePortabilityRequestQuestionTypeV2 = InsurancePortabilityRequestQuestionTypeV2.MIN_GRACE_PERIOD,
    ) = InsurancePortabilityRequestAnswerV2(
        answer = answer,
        questionType = questionType
    )

    fun buildHaocDocument(
        personId: PersonId = PersonId(),
        content: String = "content",
        documentIdHash: String = "document-id-hash",
        documentType: HaocDocumentType = HaocDocumentType.SUMARIO_DE_ALTA,
        claimId: Int? = 123456,
    ) = HaocDocument(
        personId = personId,
        content = content,
        documentIdHash = documentIdHash,
        documentType = documentType,
        claimId = claimId,
    )

    fun buildTestCodePackage(
        name: String = "Exames para Diabetes",
        testCodeIds: Set<UUID> = setOf(RangeUUID.generate(), RangeUUID.generate()),
    ) =
        TestCodePackage(name = name, testCodeIds = testCodeIds)

    fun buildCounterReferral(
        personId: PersonId = PersonId(),
        referralId: UUID? = RangeUUID.generate(),
        healthCommunitySpecialistId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        typeOfService: CounterReferralTypeOfService = CounterReferralTypeOfService.OUTPATIENT_PROCEDURE,
        appointmentDate: LocalDate = LocalDate.now(),
        typeOfAppointment: CounterReferralTypeOfAppointment = CounterReferralTypeOfAppointment.REGULAR,
        presential: Boolean = false,
        isEligible: Boolean? = null,
        attendanceType: AttendanceType = AttendanceType.CLINIC,
        diagnosticHypothesis: String = "Diagnostic Hypothesis",
        clinicalEvaluation: String = "Clinical Evaluation",
        memberOrientation: String = "Member Orientation",
        healthcareTeamOrientation: String = "Healthcare Team Orientation",
        followUpDate: LocalDate? = null,
        testRequests: List<CounterReferralGenericTask> = emptyList(),
        referrals: List<CounterReferralGenericTask> = emptyList(),
        procedures: List<CounterReferralGenericTask> = emptyList(),
        medicines: List<CounterReferralGenericTask> = emptyList(),
        surgicalReferrals: List<CounterReferralGenericTask> = emptyList(),
        healthCommunityUnreferencedAccessId: UUID? = null,
        notOccurredReason: NotOccurredReason? = null,
        appointmentOccurred: Boolean? = null,
        followUpDays: Int? = null,
        appointmentId: UUID? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) =
        CounterReferral(
            personId = personId,
            referralId = referralId,
            healthCommunitySpecialistId = healthCommunitySpecialistId,
            staffId = staffId,
            typeOfService = typeOfService,
            appointmentDate = appointmentDate,
            typeOfAppointment = typeOfAppointment,
            presential = presential,
            attendanceType = attendanceType,
            diagnosticHypothesis = diagnosticHypothesis,
            clinicalEvaluation = clinicalEvaluation,
            memberOrientation = memberOrientation,
            healthcareTeamOrientation = healthcareTeamOrientation,
            followUpDate = followUpDate,
            testRequests = testRequests,
            referrals = referrals,
            procedures = procedures,
            medicines = medicines,
            surgicalReferrals = surgicalReferrals,
            healthCommunityUnreferencedAccessId = healthCommunityUnreferencedAccessId,
            notOccurredReason = notOccurredReason,
            appointmentOccurred = appointmentOccurred,
            followUpDays = followUpDays,
            isEligible = isEligible,
            appointmentId = appointmentId,
            createdAt = createdAt
        )

    fun buildHealthProductSimulationAnswer(
        answer: String = "42",
        questionType: HealthProductSimulationQuestionType = HealthProductSimulationQuestionType.NAME,
    ) = HealthProductSimulationAnswer(
        answer = answer,
        questionType = questionType,
    )

    fun buildHealthProductSimulation(
        answers: List<HealthProductSimulationAnswer> = emptyList(),
        expiresAt: LocalDateTime? = null,
        trackingInfo: TrackingInfo? = null,
        simulatorVersion: HealthProductSimulationSimulatorVersion? = null,
        id: UUID = RangeUUID.generate(),
        groupId: UUID? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        leadId: UUID? = null,
        type: HealthProductSimulationType? = null,
        productId: UUID? = null,
        prices: PriceListing? = null,
        version: Int = 1,
    ) = HealthProductSimulation(
        answers = answers,
        expiresAt = expiresAt,
        simulatorVersion = simulatorVersion,
        id = id,
        trackingInfo = trackingInfo,
        groupId = groupId,
        createdAt = createdAt,
        leadId = leadId,
        type = type,
        productId = productId,
        prices = prices,
        version = version
    )

    fun buildHealthProductSimulationAnswersV2(
        companyEmployees: String? = null,
        companyCity: String? = null,
        companyCnpj: String? = null,
        companyName: String? = null,
        responsibleName: String? = null,
        responsiblePhone: String? = null,
        responsibleEmail: String? = null,
        findOutAlice: String? = null,
    ) = HealthProductSimulationAnswersV2(
        companyEmployees = companyEmployees,
        companyCity = companyCity,
        companyCnpj = companyCnpj,
        companyName = companyName,
        responsibleName = responsibleName,
        responsiblePhone = responsiblePhone,
        responsibleEmail = responsibleEmail,
        findOutAlice = findOutAlice
    )

    fun buildShoppingCart(
        leadId: UUID = RangeUUID.generate(),
        promoCodeId: UUID? = RangeUUID.generate(),
        healthcareTeam: String = "Henry",
        providerIds: List<String> = listOf("HAOC Vergueiro"),
        productId: UUID = RangeUUID.generate(),
    ) = ShoppingCart(
        leadId = leadId,
        promoCodeId = promoCodeId,
        healthcareTeam = healthcareTeam,
        providerIds = providerIds,
        productId = productId
    )

    fun buildAllRequiredSimulationAnswers() = listOf(
        HealthProductSimulationAnswer("28", HealthProductSimulationQuestionType.AGES),
        HealthProductSimulationAnswer("01303001", HealthProductSimulationQuestionType.POSTAL_CODE),
        HealthProductSimulationAnswer("Alice", HealthProductSimulationQuestionType.NAME),
        HealthProductSimulationAnswer("**********", HealthProductSimulationQuestionType.PHONE_NUMBER),
        HealthProductSimulationAnswer("true", HealthProductSimulationQuestionType.CONTACT_OPTIN),
        HealthProductSimulationAnswer("<EMAIL>", HealthProductSimulationQuestionType.EMAIL),
    )

    fun buildChannelComment(
        id: UUID = RangeUUID.generate(),
        channelId: String = "channelId",
        comment: String = "comment 01",
        staffId: UUID? = RangeUUID.generate(),
        type: ChannelCommentType = ChannelCommentType.COMMENT,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) =
        ChannelComment(
            id = id,
            channelId = channelId,
            comment = comment,
            staffId = staffId,
            type = type,
            createdAt = createdAt,
            updatedAt = updatedAt
        )

    fun buildChannelTheme(
        id: UUID = RangeUUID.generate(),
        channelId: String = "channelId",
        setupId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        suggestedTheme: String? = "suggestedTheme",
        theme: String = "theme",
        reason: String = "reason",
        screenOpenedAt: LocalDateTime = LocalDateTime.now(),
        screenClosedAt: LocalDateTime = LocalDateTime.now(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) =
        ChannelTheme(
            id = id,
            channelId = channelId,
            setupId = setupId,
            staffId = staffId,
            suggestedTheme = suggestedTheme,
            theme = theme,
            reason = reason,
            screenOpenedAt = screenOpenedAt,
            screenClosedAt = screenClosedAt,
            createdAt = createdAt,
            updatedAt = updatedAt
        )

    fun buildProductBundle(
        name: String = "Pacote 01",
        type: ProductBundleType = ProductBundleType.HOSPITAL,
        imageUrl: String? = "image.png",
        priceScale: Int = 0,
        id: UUID = RangeUUID.generate(),
        providerIds: List<UUID> = emptyList(),
        specialistIds: List<UUID> = emptyList(),
        specialtyTiers: List<SpecialtyTiers> = emptyList(),
        externalSpecialists: List<UUID> = emptyList(),
    ) = ProductBundle(
        id = id,
        name = name,
        type = type,
        imageUrl = imageUrl,
        priceScale = priceScale,
        active = true,
        providerIds = providerIds,
        specialistIds = specialistIds,
        specialtyTiers = specialtyTiers,
        externalSpecialists = externalSpecialists,
    )

    fun buildChannelFup(
        id: UUID = RangeUUID.generate(),
        name: String = "channel_fup_name",
        question: String = "channel_fup_question",
        answers: List<ChannelFupAnswer> = emptyList(),
        status: Status = Status.ACTIVE,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        listed: Boolean = true,
    ) =
        ChannelFup(
            id = id,
            name = name,
            question = question,
            answers = answers,
            status = status,
            createdAt = createdAt,
            updatedAt = updatedAt,
            listed = listed
        )

    fun buildHealthForm(
        id: UUID = RangeUUID.generate(),
        name: String = "Pre Imersão",
        key: String = "PRE_IMMERSION",
        type: FormType = FormType.HEALTH,
    ) =
        HealthForm(
            id = id,
            name = name,
            key = key,
            type = type,
        )

    fun buildHealthFormSection(
        healthFormId: UUID = RangeUUID.generate(),
        title: String = "Section 123",
        estimatedTimeText: String? = null,
        id: UUID = RangeUUID.generate()
    ) =
        HealthFormSection(
            id = id,
            healthFormId = healthFormId,
            title = title,
            details = "Details 123",
            imageUrl = "s3/bucket/123.jpg",
            nextButtonText = "Cool Text",
            estimatedTimeText = estimatedTimeText,
        )

    fun buildHealthFormQuestion(
        id: UUID = RangeUUID.generate(),
        formId: UUID = RangeUUID.generate(),
        sectionId: UUID = RangeUUID.generate(),
        index: Int = 1,
        required: Boolean = true,
        type: HealthFormQuestionType = HealthFormQuestionType.MULTIPLE_OPTIONS
    ) =
        HealthFormQuestion(
            id = id,
            healthFormId = formId,
            healthFormSectionId = sectionId,
            summaryQuestion = "Nunc sed velit",
            question = "Nunc sed velit dignissim sodales ut eu sem integer vitae",
            type = type,
            index = index,
            defaultNext = 2,
            validations = mapOf(
                HealthFormQuestionValidationType.SEX to Sex.MALE.name
            ),
            options = listOf(
                HealthFormQuestionOption(
                    next = 2,
                    value = true,
                    label = "Sim"
                ),
                HealthFormQuestionOption(
                    next = 3,
                    value = false,
                    label = "Não"
                )
            ),
            required = required
        )

    fun buildStepperHealthFormQuestion(
        id: UUID = RangeUUID.generate(),
        formId: UUID = RangeUUID.generate(),
        sectionId: UUID = RangeUUID.generate(),
        index: Int = 1,
    ) =
        HealthFormQuestion(
            id = id,
            healthFormId = formId,
            healthFormSectionId = sectionId,
            summaryQuestion = "Nunc sed velit",
            question = "Nunc sed velit dignissim sodales ut eu sem integer vitae",
            type = HealthFormQuestionType.MULTIPLE_OPTIONS,
            displayAttributes = mapOf(
                HealthFormQuestionDisplayType.VIEW_TYPE to ViewType.STEPPER
            ),
            index = index,
            defaultNext = 2,
            validations = mapOf(
                HealthFormQuestionValidationType.SEX to Sex.MALE.name
            ),
            options = listOf(
                HealthFormQuestionOption(
                    next = 2,
                    value = "0",
                    label = "Sim"
                ),
                HealthFormQuestionOption(
                    next = 3,
                    value = "1",
                    label = "Não"
                )
            )
        )

    fun buildHealthFormQuestionAnswer(
        personId: PersonId = PersonId(),
        healthFormId: UUID = RangeUUID.generate(),
        healthFormQuestionId: UUID = RangeUUID.generate(),
        healthFormAnswerGroupId: UUID = RangeUUID.generate(),
        next: Int? = 2,
        answer: String = "xpto",
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) = HealthFormQuestionAnswer(
        personId = personId,
        healthFormId = healthFormId,
        healthFormQuestionId = healthFormQuestionId,
        next = next,
        answer = answer,
        healthFormAnswerGroupId = healthFormAnswerGroupId,
        createdAt = createdAt
    )

    fun buildHealthFormAnswerGroup(
        personId: PersonId = PersonId(),
        healthFormId: UUID = RangeUUID.generate(),
        source: HealthFormAnswerSource? = null,
        id: UUID = RangeUUID.generate(),
        finishedAt: LocalDateTime? = null
    ) = HealthFormAnswerGroup(
        id = id,
        personId = personId,
        healthFormId = healthFormId,
        source = source,
        finishedAt = finishedAt
    )

    fun buildInvoicePayment(
        id: UUID = RangeUUID.generate(),
        amount: BigDecimal = BigDecimal(699.00),
        approvedAt: LocalDateTime? = null,
        status: InvoicePaymentStatus = InvoicePaymentStatus.PENDING,
        method: PaymentMethod = PaymentMethod.BOLETO,
        canceledReason: CancellationReason? = null,
        externalId: String? = null,
        source: InvoicePaymentSource? = null,
        paymentDetail: PaymentDetail? = null,
        memberInvoiceIds: List<UUID> = listOf(RangeUUID.generate()),
        invoiceGroupId: UUID? = RangeUUID.generate(),
        billingAccountablePartyId: UUID? = RangeUUID.generate(),
        origin: InvoicePaymentOrigin? = InvoicePaymentOrigin.UNDEFINED,
        reason: PaymentReason? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        invoiceLiquidationId: UUID? = null,
        sendEmail: Boolean? = null,
        preActivationPaymentId: UUID? = null,
    ) = InvoicePayment(
        id = id,
        amount = amount,
        approvedAt = approvedAt,
        status = status,
        method = method,
        canceledReason = canceledReason,
        externalId = externalId,
        source = source,
        memberInvoiceIds = memberInvoiceIds,
        invoiceGroupId = invoiceGroupId,
        billingAccountablePartyId = billingAccountablePartyId,
        origin = origin,
        reason = reason,
        createdAt = createdAt,
        invoiceLiquidationId = invoiceLiquidationId,
        sendEmail = sendEmail,
        preActivationPaymentId = preActivationPaymentId,
    ).withPaymentDetail(paymentDetail)

    fun buildDasaDiagnosticReport(
        personId: PersonId = PersonId(),
        externalId: String = "externalId",
        serviceRequests: List<ResourceServiceRequest> = listOf(buildDasaServiceRequest()),
        observations: List<ResourceObservation> = listOf(buildDasaObservation()),
        encounters: List<ResourceDiagnosticEncounter> = listOf(buildEncounter()),
    ) = DasaDiagnosticReport(
        personId = personId,
        id = RangeUUID.generate(),
        externalId = externalId,
        resourceType = "DiagnosticReport",
        meta = ResourceMeta(
            lastUpdated = LocalDateTime.now().toString(),
            profile = emptyList()
        ),
        status = "final",
        category = emptyList(),
        code = ResourceCode(
            coding = listOf(
                ResourceCoding(
                    system = "system",
                    code = "code"
                )
            )
        ),
        subject = ResourceReference(
            reference = "reference",
            type = "type"
        ),
        encounter = ResourceReference(
            reference = "reference",
            type = "type"
        ),
        performer = emptyList(),
        resultsInterpreter = emptyList(),
        result = emptyList(),
        presentedForm = emptyList(),
        observations = observations,
        medias = emptyList(),
        encounters = encounters,
        coverages = emptyList(),
        serviceRequests = serviceRequests,
        practitioners = emptyList(),
    )

    private fun buildDasaObservation(
        testName: String = "Sorologia para COVID-19 (IgM/IgG) - CLIA",
        valueString: String = "Não Reagente",
        effectiveDateTime: String? = "2020-06-14T11:33:35.000Z",
        lastUpdated: String = "2021-05-05T18:00:00Z",
        performerName: String = "Dr. Chapeleiro Maluco",
    ) = ResourceObservation(
        id = "observation-id",
        resourceType = "Observation",
        meta = ResourceMeta(
            profile = listOf("https://interoperabilidade-hml.dasa.com.br/fhir/StructureDefinition/Observation"),
            lastUpdated = lastUpdated
        ),
        identifier = listOf(
            ResourceIdentifier(
                use = "usual",
                system = "https://interoperabilidade-hml.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicoes-codigoOrigem",
                value = "79854946-39943-39943",
            )
        ),
        status = "final",
        category = listOf(
            ResourceCode(
                coding = listOf(
                    ResourceCoding(
                        system = "http://terminology.hl7.org/CodeSystem/observation-category",
                        code = "laboratory",
                        display = "Laboratory",
                    )
                ),
                text = "Laboratory",
            )
        ),
        code = ResourceCode(
            coding = listOf(
                ResourceCoding(
                    code = "39943",
                    display = testName,
                )
            ),
            text = testName
        ),
        effectiveDateTime = effectiveDateTime,
        performer = listOf(
            ResourceReference(
                type = "Practitioner",
                reference = "Practitioner/608b04153fbff5f47f8c0cfe",
                display = performerName
            )
        ),
        valueQuantity = null,
        valueString = valueString,
        referenceRange = emptyList(),
        basedOn = listOf(
            ResourceReference(
                reference = "ServiceRequest/608c3e0e81d57bcd04eb1592",
                type = "ServiceRequest"
            )
        ),
        component = listOf(
            ResourceComponent(
                code = ResourceCode(
                    coding = listOf(
                        ResourceCoding(
                            code = "39943",
                            display = testName,
                        )
                    ),
                    text = testName
                ),
                valueQuantity = null,
                valueString = valueString,
                referenceRange = emptyList(),
            )
        ),
    )

    fun buildBoletoPaymentDetail(
        paymentId: UUID = RangeUUID.generate(),
        dueDate: LocalDateTime = LocalDateTime.now().plusDays(1),
        barcode: String? = "12312312312313",
        paymentUrl: String? = "https://url",
        id: UUID = RangeUUID.generate(),
    ) = BoletoPaymentDetail(
        paymentId = paymentId,
        dueDate = dueDate,
        barcode = barcode,
        paymentUrl = paymentUrl,
        id = id
    )

    fun buildPixPaymentDetail(
        paymentId: UUID = RangeUUID.generate(),
        paymentUrl: String = "https://url",
        id: UUID = RangeUUID.generate(),
        dueDate: LocalDateTime? = LocalDateTime.now(),
        paymentCode: String? = "paymentCodePix",
    ) = PixPaymentDetail(
        id = id,
        paymentId = paymentId,
        paymentUrl = paymentUrl,
        dueDate = dueDate,
        paymentCode = paymentCode,
    )

    fun buildBolepixPaymentDetail(
        paymentId: UUID = RangeUUID.generate(),
        dueDate: LocalDateTime = LocalDateTime.now().plusDays(1),
        barcodeBoleto: String? = "12312312312313",
        boletoPaymentUrl: String? = "barcodeImage",
        paymentCodePix: String? = "78784545pix787878",
        pixPaymentUrl: String? = "pixImageUrl",
        paymentUrl: String? = "https://url",
        id: UUID = RangeUUID.generate(),
    ) = BolepixPaymentDetail(
        paymentId = paymentId,
        dueDate = dueDate,
        barcodeBoleto = barcodeBoleto,
        boletoPaymentUrl = boletoPaymentUrl,
        paymentCodePix = paymentCodePix,
        pixPaymentUrl = pixPaymentUrl,
        paymentUrl = paymentUrl,
        id = id
    )

    fun buildSimpleCreditCardPaymentDetail(
        paymentId: UUID = RangeUUID.generate(),
        paymentUrl: String = "https://url",
        id: UUID = RangeUUID.generate(),
    ) = SimpleCreditCardPaymentDetail(
        paymentId = paymentId,
        paymentUrl = paymentUrl,
        id = id
    )

    fun buildMemberInvoiceWithPayments(
        memberId: UUID,
        paymentStatus: InvoicePaymentStatus = InvoicePaymentStatus.PENDING,
        invoicePayments: List<InvoicePayment>? = null,
        invoiceItems: List<InvoiceItem>? = null,
        invoiceGroupId: UUID? = RangeUUID.generate(),
        preActivationPaymentId: UUID? = null,
        canceledReason: CancellationReason? = null,
    ): MemberInvoice {

        val enrichedInvoicePayments = invoicePayments
            ?: listOf(
                buildInvoicePayment(
                    invoiceGroupId = invoiceGroupId,
                    status = paymentStatus,
                    canceledReason = canceledReason,
                    preActivationPaymentId = preActivationPaymentId
                )
            )
        val enrichedInvoiceItems = invoiceItems ?: listOf(buildInvoiceItem())
        val member = buildMember(id = memberId)
        val memberInvoice = buildMemberInvoice(
            member = member,
            invoiceItems = enrichedInvoiceItems,
            preActivationPaymentId = preActivationPaymentId
        )

        return memberInvoice.withInvoicePayments(enrichedInvoicePayments)
    }

    fun buildHealthCommunityUnreferencedAccess(
        personId: PersonId,
        staffId: UUID? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) =
        HealthCommunityUnreferencedAccess(
            personId = personId,
            staffId = staffId,
            createdAt = createdAt
        )

    fun buildInvoiceLiquidation(
        id: UUID = RangeUUID.generate(),
        externalId: String = "DEFAULT_EXTERNAL_ID",
        amount: BigDecimal = BigDecimal("0.00"),
        addition: BigDecimal = BigDecimal.ZERO,
        discount: BigDecimal = BigDecimal.ZERO,
        dueDate: LocalDate = LocalDate.now().plusDays(30),
        memberInvoiceGroupIds: List<UUID> = listOf(RangeUUID.generate()),
        status: InvoiceLiquidationStatus = InvoiceLiquidationStatus.PROCESSED,
        billingAccountablePartyId: UUID = RangeUUID.generate(),
        companyIds: List<UUID> = emptyList(),
        subcontractIds: List<UUID> = emptyList(),
        companyId: UUID? = null,
        subcontractId: UUID? = null,
        installment: Int = 1,
        totalInstallments: Int = 1,
        businessType: BusinessType = BusinessType.B2B
    ) = InvoiceLiquidation(
        id = id,
        externalId = externalId,
        amount = amount,
        addition = addition,
        discount = discount,
        dueDate = dueDate,
        memberInvoiceGroupIds = memberInvoiceGroupIds,
        status = status,
        billingAccountablePartyId = billingAccountablePartyId,
        companyIds = companyIds,
        subcontractIds = subcontractIds,
        companyId = companyId,
        subcontractId = subcontractId,
        installment = installment,
        totalInstallments = totalInstallments,
        businessType = businessType
    )

    fun buildInvoiceItem(
        id: UUID = RangeUUID.generate(),
        referenceDate: LocalDate = LocalDate.now(),
        operation: InvoiceItemOperation = InvoiceItemOperation.CHARGE,
        type: InvoiceItemType = InvoiceItemType.COPAY,
        notes: String? = null,
        status: InvoiceItemStatus = InvoiceItemStatus.ACTIVE,
        personId: PersonId? = PersonId(),
        absoluteValue: BigDecimal? = BigDecimal("120.00"),
        percentageValue: BigDecimal? = null,
        resolvedValue: BigDecimal? = null,
        companyId: UUID? = null,
        subcontractId: UUID? = null,
        externalId: String? = null,
        referenceValue: BigDecimal? = null,
        validityPeriodInDays: Int? = null,
    ) = InvoiceItem(
        id = id,
        referenceDate = referenceDate,
        operation = operation,
        type = type,
        notes = notes,
        status = status,
        personId = personId,
        companyId = companyId,
        companySubcontractId = subcontractId,
        absoluteValue = absoluteValue,
        percentageValue = percentageValue,
        resolvedValue = resolvedValue,
        externalId = externalId,
        validityPeriodInDays = validityPeriodInDays,
        referenceValue = referenceValue,
    )

    fun buildHaocFhirProcess(
        personId: PersonId = PersonId(),
        processedAt: LocalDateTime = LocalDateTime.now(),
    ) = HaocFhirProcess(
        personId = personId,
        processedAt = processedAt,
    )

    fun buildHaocProntoAtendimentoResult(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(RangeUUID.generate(PERSON_ID_RANGE)),
        claimId: Int? = null,
        caracterizacaoAtendimento: CaracterizacaoAtendimento = buildCaracterizacaoAtendimento(),
        motivoAtendimento: String = "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        atendimentos: List<HaocAtendimento> = emptyList(),
        desfecho: HaocDesfecho = buildHaocDesfecho(),
        haocDocumentId: UUID = RangeUUID.generate()
    ) = HaocProntoAtendimentoResult(
        id = id,
        personId = personId,
        claimId = claimId,
        caracterizacaoAtendimento = caracterizacaoAtendimento,
        motivoAtendimento = motivoAtendimento,
        atendimentos = atendimentos,
        desfecho = desfecho,
        haocDocumentId = haocDocumentId
    )

    fun buildHaocSumarioDeAltaResult(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        claimId: Int? = null,
        haocDocumentId: UUID = RangeUUID.generate(),
        dataEntrada: LocalDateTime = LocalDateTime.now(),
        dataSaida: LocalDateTime = LocalDateTime.now(),
    ) = HaocSumarioDeAltaResult(
        id = id,
        personId = personId,
        claimId = claimId,
        motivoAdmissao = HaocMotivoAdmissao(
            diagnostico = emptyList(),
        ),
        caracterizacaoAtendimento = HaocCaracterizacaoChegada(
            procedencia = "procedencia",
            local = "local",
            caraterDaInternacao = "caraterDaInternacao",
            dataInternacao = dataEntrada
        ),
        evolucaoClinica = "evolucaoClinica",
        desfecho = HaocDesfechoInternacao(
            motivo = "motivo",
            dataSaida = dataSaida,
            diasUTI = 0,
            profissionalDeAlta = HaocProfissional(
                nome = "profissional",
                ocupacao = "ocupacao",
                uf = "uf",
                conselho = "conselho",
                numeroRegistro = "numeroRegistro"
            )
        ),
        haocDocumentId = haocDocumentId
    )

    fun buildEinsteinAtendimento(
        personId: PersonId = PersonId(),
        passagem: String = "ABC1234",
        identificacao: String = "identificacao",
        local: String = "local",
        estabelecimento: String = "estabelecimento",
        procedencia: String? = "procedencia",
        dataChegada: String = "2021-06-30",
        dataAtendimento: String = "2021-06-30",
        modalidade: String = "modalidade",
        profissionais: List<EinsteinProfissional> = emptyList(),
        motivos: List<Motivo> = emptyList(),
    ) = EinsteinAtendimento(
        personId = personId,
        passagem = passagem,
        identificacao = identificacao,
        local = local,
        estabelecimento = estabelecimento,
        procedencia = procedencia,
        dataChegada = dataChegada,
        dataAtendimento = dataAtendimento,
        modalidade = modalidade,
        profissionais = profissionais,
        motivos = motivos,
    )

    fun buildEinsteinDiagnostico(
        personId: PersonId = PersonId(),
        passagem: String = "ABC1234",
        diagnosticos: List<Diagnostico> = emptyList(),
    ) = EinsteinDiagnostico(
        personId = personId,
        passagem = passagem,
        diagnosticos = diagnosticos
    )

    fun buildEinsteinAvaliacaoInicial(
        personId: PersonId = PersonId(),
        passagem: String = "ABC1234",
        escalaDor: String = "escalaDor",
        escalaGlasgow: String = "escalaGlasgow",
        exameFisico: ExameFisico = ExameFisico("ExameGeral"),
        frequenciaCardiaca: String = "75",
        frequenciaRespiratoria: String = "18",
        glicemiaCapilar: String = "60",
        medicoes: Medicoes = Medicoes("70", "1,70", "20,8"),
        pressao: Pressao = Pressao("100", "70"),
        saturacao: String = "98",
        temperatura: String = "36,6",
    ) = EinsteinAvaliacaoInicial(
        personId = personId,
        passagem = passagem,
        escalaDor = escalaDor,
        escalaGlasgow = escalaGlasgow,
        exameFisico = exameFisico,
        frequenciaCardiaca = frequenciaCardiaca,
        frequenciaRespiratoria = frequenciaRespiratoria,
        glicemiaCapilar = glicemiaCapilar,
        medicoes = medicoes,
        pressao = pressao,
        saturacao = saturacao,
        temperatura = temperatura
    )

    fun buildEinsteinProcedimento(
        personId: PersonId = PersonId(),
        passagem: String = "ABC1234",
        procedimentos: List<Procedimento> = emptyList(),
    ) = EinsteinProcedimento(
        personId = personId,
        passagem = passagem,
        procedimentos = procedimentos
    )

    fun buildEinsteinMedicamento(
        personId: PersonId = PersonId(),
        passagem: String = "ABC1234",
        medicamentos: List<Medicamento> = emptyList(),
    ) = EinsteinMedicamento(
        personId = personId,
        passagem = passagem,
        medicamentos = medicamentos
    )

    fun buildEinsteinEncaminhamento(
        personId: PersonId = PersonId(),
        passagem: String = "ABC1234",
        desfechoPa: String = "desfechoPa",
        encaminhamentos: List<Encaminhamento> = emptyList(),
    ) = EinsteinEncaminhamento(
        personId = personId,
        passagem = passagem,
        desfechoPa = desfechoPa,
        encaminhamentos = encaminhamentos
    )

    fun buildEinsteinDadosDeAlta(
        personId: PersonId = PersonId(),
        passagem: String = "ABC1234",
        dataAltaPa: String = "2021-06-30",
        profissionais: List<EinsteinProfissional> = emptyList(),
    ) = EinsteinDadosDeAlta(
        personId = personId,
        passagem = passagem,
        dataAltaPa = dataAltaPa,
        profissionais = profissionais
    )

    fun buildEinsteinResultadoExame(
        personId: PersonId = PersonId(RangeUUID.generate(PERSON_ID_RANGE)),
        passagem: String = "ABC1234",
        status: String = "status",
        dataPassagem: String = "01/02/2021",
        tipoPassagem: String = "tipoPassagem",
        localPassagem: String = "localPassagem",
        ocorrencias: String = "1",
        exames: List<Exame> = emptyList(),
    ) = EinsteinResultadoExame(
        personId = personId,
        passagem = passagem,
        status = status,
        dataPassagem = dataPassagem,
        tipoPassagem = tipoPassagem,
        localPassagem = localPassagem,
        ocorrencias = ocorrencias,
        exames = exames
    )

    fun buildBook(
        name: String = "I'm Thinking of Ending Things",
        author: String = "Iain Reid",
        isbn: String = "1501126946",
        age: Int? = 12,
        available: Boolean = true,
        genres: List<String> = listOf("Thriller", "Horror fiction", "Psychological thriller", "Psychological Fiction"),
        nested: Nested? = null,
        launchDate: LocalDate = LocalDate.of(2016, 6, 14),
        personId: PersonId = PersonId(),
        searchTokens: String = name.unaccent(),
        listOfMap: List<Map<String, Any?>> = emptyList(),
        someUuid: UUID = RangeUUID.generate(),
        latitude: String? = null,
        longitude: String? = null,
    ) = Book(
        name = name,
        author = author,
        isbn = isbn,
        age = age,
        available = available,
        genres = genres,
        nested = nested,
        launchDate = launchDate,
        personId = personId,
        searchTokens = searchTokens,
        listOfMap = listOfMap,
        someUuid = someUuid,
        latitude = latitude,
        longitude = longitude
    )

    fun buildHealthCondition(
        id: UUID = RangeUUID.generate(),
        name: String = "Glaucoma secundário a traumatismo ocular",
        displayName: String = "Glaucoma",
        code: String = "H403",
        codeType: HealthConditionCodeType = HealthConditionCodeType.CID_10,
        specialities: List<String>? = listOf("Oftalmologia"),
        cptApplicationRule: CptApplicationRule = CptApplicationRule.WITHOUT_SURGERY_ONLY,
        surgeryNames: List<String>? = listOf("Trabeculectomia"),
        riskRating: Int = 0,
        isChronic: Boolean = false,
        memberFriendlyName: String? = null,
        conditionType: HealthConditionType? = null,
        healthConditionAxisId: UUID? = null,
    ) = HealthCondition(
        id = id,
        name = name,
        displayName = displayName,
        code = code,
        codeType = codeType,
        specialities = specialities,
        cptApplicationRule = cptApplicationRule,
        surgeryNames = surgeryNames,
        riskRating = riskRating,
        isChronic = isChronic,
        memberFriendlyName = memberFriendlyName,
        conditionType = conditionType,
        healthConditionAxisId = healthConditionAxisId
    )

    fun buildHealthConditionTemplate(
        id: UUID = RangeUUID.generate(),
        healthConditionId: UUID = RangeUUID.generate(),
        template: String = "template",
    ) = HealthConditionTemplate(
        id = id,
        healthConditionId = healthConditionId,
        template = template
    )


    fun buildHealthConditionAxis(
        id: UUID = RangeUUID.generate(),
        name: String = "Axis",
        type: HealthConditionCodeType = HealthConditionCodeType.CIPE,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = HealthConditionAxis(
        id = id,
        name = name,
        type = type,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildHealthConditionRelated(
        id: UUID = RangeUUID.generate(),
        healthConditionId: UUID = RangeUUID.generate(),
        healthConditionIds: List<UUID> = listOf(RangeUUID.generate()),
        type: HealthConditionCodeType = HealthConditionCodeType.CIPE,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = HealthConditionRelated(
        id = id,
        healthConditionId = healthConditionId,
        healthConditionIds = healthConditionIds,
        type = type,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildHealthConditionGroup(
        name: String = "Outras Demandas",
        healthConditionIds: List<UUID>,
    ) = HealthConditionGroup(
        name = name,
        healthConditionIds = healthConditionIds
    )

    fun buildPersonPII() = PersonPII(
        firstName = "José",
        lastName = "da Silva",
        nickName = "Zé",
        socialName = "Josi",
        nationalId = "**********0",
        formattedNationalId = "012.345.678-90"
    )

    fun buildPersonPreferences(
        firstPaymentMethod: PaymentMethod = PaymentMethod.BOLETO,
        personId: PersonId = PersonId(),
    ) = PersonPreferences(
        personId = personId,
        firstPaymentMethod = firstPaymentMethod
    )

    fun buildHealthDeclarationAnswer(
        question: String = "Você tem ou já teve algum problema no %%nariz, ouvido ou de visão%%?",
        answer: String = listOf("H521", "J342").toString(),
        questionType: HealthDeclarationQuestionType? = HealthDeclarationQuestionType.NOSE_EYE_AND_EARS,
        answeredAt: String? = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME),
        healthConditions: List<HealthCondition>? = listOf(
            buildHealthCondition(
                name = "Miopia degenerativa",
                displayName = "Miopia",
                code = "H521",
                cptApplicationRule = CptApplicationRule.WITHOUT_SURGERY_ONLY,
                surgeryNames = listOf("Refrativa"),
            ),
            buildHealthCondition(
                name = "Desvio do septo nasal",
                displayName = "Desvio do septo",
                code = "J342",
                cptApplicationRule = CptApplicationRule.ALWAYS,
                surgeryNames = listOf("Septoplastia"),
            )
        ),
    ) = HealthDeclarationAnswer(
        question = question,
        answer = answer,
        questionType = questionType,
        answeredAt = answeredAt,
        healthConditions = healthConditions,
    )

    fun buildHealthLogic(
        personId: PersonId,
        status: HealthLogicStatus = HealthLogicStatus.ACTIVE,
        comment: String = "comment 1",
        addedByStaffId: UUID = RangeUUID.generate(),
        healthLogicNodeId: UUID = RangeUUID.generate(),
        currentNodeId: UUID = RangeUUID.generate(),
        startedAt: LocalDateTime = LocalDateTime.now(),
        addedAt: LocalDateTime = LocalDateTime.now(),
    ) = HealthLogicRecord(
        personId = personId,
        status = status,
        comment = comment,
        addedByStaffId = addedByStaffId,
        healthLogicNodeId = healthLogicNodeId,
        currentNodeId = currentNodeId,
        addedAt = addedAt,
        startedAt = startedAt,
    )

    fun buildPersonHealthLogic(
        personId: PersonId,
        status: HealthLogicStatus = HealthLogicStatus.ACTIVE,
        comment: String = "comment 1",
        addedByStaffId: UUID = RangeUUID.generate(),
        healthLogicNodeId: UUID = RangeUUID.generate(),
        currentNodeId: UUID = RangeUUID.generate(),
        startedAt: LocalDateTime = LocalDateTime.now(),
        addedAt: LocalDateTime = LocalDateTime.now(),
        referencedLink: HealthLogicRecord.ReferencedLink? = null,
        id: UUID = RangeUUID.generate()
    ) = PersonHealthLogic(
        id = id,
        personId = personId,
        status = status,
        comment = comment,
        addedByStaffId = addedByStaffId,
        healthLogicNodeId = healthLogicNodeId,
        currentNodeId = currentNodeId,
        addedAt = addedAt,
        startedAt = startedAt,
        referencedLink = referencedLink,
    )

    fun buildOpportunity(
        id: UUID = RangeUUID.generate(),
        productId: UUID = RangeUUID.generate(),
        simulationId: UUID? = RangeUUID.generate(),
        prices: List<ProductPrice> = listOf(buildProductPrice()),
        expiresAt: LocalDateTime = LocalDate.now().atEndOfTheDay().plusDays(30),
        productPriceListingId: UUID? = null,
        priceListing: PriceListing? = null,
    ) = Opportunity(
        id = id,
        productId = productId,
        simulationId = simulationId,
        prices = prices,
        expiresAt = expiresAt,
        productPriceListingId = productPriceListingId,
        priceListing = priceListing,
    )

    fun buildAliceFile(
        id: UUID = RangeUUID.generate(),
        fileName: String = "sample.pdf",
        url: String = "https://s3.alice.com.br/sample.pdf",
        type: String = "pdf",
        fileSize: Long? = null,
    ) = AliceFile(
        id = id,
        fileName = fileName,
        url = url,
        type = type,
        fileSize = fileSize,
    )

    fun buildFileVault(
        personId: PersonId = PersonId(),
        id: UUID = RangeUUID.generate(),
        domain: String = "member",
        namespace: String? = null,
        fileType: String = "pdf",
        originalFileName: String = "sample.pdf",
        url: String = "https://s3.alice.com.br/sample.pdf",
        fileSize: Long? = null,
        referencedLinks: List<FileVault.ReferencedLink> = emptyList()
    ) = FileVault(
        personId = personId,
        id = id,
        domain = domain,
        namespace = namespace,
        fileType = fileType,
        url = url,
        originalFileName = originalFileName,
        fileSize = fileSize,
        referencedLinks = referencedLinks
    )

    fun buildGenericFileVault(
        id: UUID = RangeUUID.generate(),
        domain: String = "member",
        namespace: String? = null,
        fileType: String = "pdf",
        originalFileName: String = "sample.pdf",
        url: String = "https://s3.alice.com.br/sample.pdf",
        fileSize: Long? = null,
        entityType: EntityType = EntityType.STAFF,
        entityId: UUID? = RangeUUID.generate(),
    ) = GenericFileVault(
        id = id,
        domain = domain,
        namespace = namespace,
        fileType = fileType,
        url = url,
        originalFileName = originalFileName,
        fileSize = fileSize,
        entityType = entityType,
        entityId = entityId
    )

    fun buildPriceListing(
        id: UUID = RangeUUID.generate(),
        title: String = "Plano 10",
        ranges: List<PriceListingItem> = listOf(buildPriceListingItem()),
    ) = PriceListing(
        title = title,
        items = ranges,
        id = id,
    )

    fun buildPriceListingItem(
        minAge: Int = 1,
        maxAge: Int = 18,
        amount: BigDecimal = 100.money,
        priceAdjustment: BigDecimal = BigDecimal("20.00"),
    ) = PriceListingItem(
        minAge = minAge,
        maxAge = maxAge,
        amount = amount,
        priceAdjustment = priceAdjustment
    )

    fun buildProductPriceListing(
        id: UUID = RangeUUID.generate(),
        priceListingId: UUID = RangeUUID.generate(),
        productId: UUID = RangeUUID.generate(),
        startDate: LocalDateTime = LocalDateTime.now(),
        endDate: LocalDateTime? = null,
    ) = ProductPriceListing(
        id = id,
        priceListingId = priceListingId,
        productId = productId,
        startDate = startDate,
        endDate = endDate,
    )

    fun buildMemberProductPrice(
        id: UUID = RangeUUID.generate(),
        productPriceListingId: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        productPriceAdjustmentId: UUID? = null,
        startDate: LocalDateTime = LocalDateTime.now(),
        endDate: LocalDateTime? = null,
        sequence: Int = 0,
        items: List<PriceListingItem> = listOf(
            PriceListingItem(
                minAge = 1,
                maxAge = 18,
                amount = 100.money,
                priceAdjustment = BigDecimal("20.00")
            )
        ),
    ) = MemberProductPrice(
        id = id,
        productPriceListingId = productPriceListingId,
        productPriceAdjustmentId = productPriceAdjustmentId,
        memberId = memberId,
        startDate = startDate,
        endDate = endDate,
        sequence = sequence,
        items = items,
    )

    fun buildCaseRecord(
        personId: PersonId = PersonId(),
        caseId: UUID = RangeUUID.generate(),
        addedByStaffId: UUID = RangeUUID.generate(),
        responsibleStaffId: UUID = RangeUUID.generate(),
        description: Disease = Disease(CID_10, "A99", id = RangeUUID.generate()),
        observation: String? = null,
        severity: CaseSeverity = CaseSeverity.COMPENSATED,
        status: CaseStatus = CaseStatus.ACTIVE,
        addedAt: LocalDateTime = LocalDateTime.now(),
        startedAt: LocalDateTime = LocalDateTime.now(),
        healthConditionId: UUID? = null,
        follow: Follow? = null,
        referencedLinks: List<CaseRecordReference> = emptyList(),
        seriousness: CaseSeriousness? = null,
        cipes: List<Disease>? = emptyList(),
    ) = CaseRecord(
        personId = personId,
        caseId = caseId,
        addedByStaffId = addedByStaffId,
        responsibleStaffId = responsibleStaffId,
        description = description,
        observation = observation,
        severity = severity,
        status = status,
        addedAt = addedAt,
        startedAt = startedAt,
        healthConditionId = healthConditionId,
        follow = follow,
        referencedLinks = referencedLinks,
        seriousness = seriousness,
        cipes = cipes
    )

    fun buildExternalCalendarEvent(
        id: UUID = RangeUUID.generate(),
        externalEventId: String = "event_id",
        staffId: UUID = RangeUUID.generate(),
        status: ExternalEventStatus = ExternalEventStatus.CONFIRMED,
        startTime: LocalDateTime = LocalDateTime.now(),
        endTime: LocalDateTime = LocalDateTime.now(),
        provider: ExternalCalendarProvider = ExternalCalendarProvider.GOOGLE,
        externalUpdatedAt: LocalDateTime = LocalDateTime.now(),
        externalCalendarRecurrentEventId: UUID? = null,
        transparency: ExternalEventTransparency = ExternalEventTransparency.BUSY,
        appointmentScheduleId: UUID? = null
    ) = ExternalCalendarEvent(
        id = id,
        externalEventId = externalEventId,
        staffId = staffId,
        status = status,
        startTime = startTime,
        endTime = endTime,
        provider = provider,
        externalUpdatedAt = externalUpdatedAt,
        externalCalendarRecurrentEventId = externalCalendarRecurrentEventId,
        transparency = transparency,
        appointmentScheduleId = appointmentScheduleId
    )

    fun buildExternalCalendarRecurrentEvent(
        id: UUID = RangeUUID.generate(),
        externalEventId: String = "event_id",
        staffId: UUID = RangeUUID.generate(),
        status: ExternalEventStatus = ExternalEventStatus.CONFIRMED,
        provider: ExternalCalendarProvider = ExternalCalendarProvider.GOOGLE,
        recurrence: List<String> = emptyList(),
        byDay: List<Weekday> = emptyList(),
        untilDateTime: LocalDateTime? = null,
        transparency: ExternalEventTransparency = ExternalEventTransparency.BUSY,
        staffScheduleId: UUID? = null,
    ) = ExternalCalendarRecurrentEvent(
        id = id,
        externalEventId = externalEventId,
        staffId = staffId,
        status = status,
        provider = provider,
        recurrence = recurrence,
        byDay = byDay,
        untilDateTime = untilDateTime,
        transparency = transparency,
        staffScheduleId = staffScheduleId,
    )

    fun buildBillingAccountableParty(
        firstName: String = "Duck",
        lastName: String = "Philips",
        type: BillingAccountablePartyType = BillingAccountablePartyType.NATURAL_PERSON,
        nationalId: String = "*********",
        email: String = "<EMAIL>",
        address: Address = buildAddress(),
    ) = BillingAccountableParty(
        firstName = firstName,
        lastName = lastName,
        type = type,
        nationalId = nationalId,
        email = email,
        address = address,
        updatedBy = UpdatedBy(userType = "Unauthenticated", userId = "undefined", environmentName = "undefined")
    )

    fun buildPersonBillingAccountableParty(
        id: UUID = RangeUUID.generate(),
        billingAccountablePartyId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        startDate: LocalDateTime = LocalDateTime.now(),
        endDate: LocalDateTime? = null,
        sequence: Int = 0,
    ) = PersonBillingAccountableParty(
        id = id,
        personId = personId,
        billingAccountablePartyId = billingAccountablePartyId,
        startDate = startDate,
        endDate = endDate,
        sequence = sequence,
    )

    fun buildEligibilityCheck(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        checkedByEmail: String = "<EMAIL>",
        checkedAt: LocalDateTime = LocalDateTime.now(),
        providerUnitId: UUID = RangeUUID.generate(),
        result: EligibilityResult = EligibilityResult.ELIGIBLE,
    ) = EligibilityCheck(
        id = id,
        personId = personId,
        checkedByEmail = checkedByEmail,
        checkedAt = checkedAt,
        providerUnitId = providerUnitId,
        result = result,
    )

    fun buildEmailCommunication(
        id: UUID = RangeUUID.generate(),
        sender: String = "<EMAIL>",
        recipient: String = "<EMAIL>",
        template: String = "template",
        idempotencyKey: String = RangeUUID.generate().toString(),
    ) = EmailCommunication(
        id = id,
        sender = sender,
        recipient = recipient,
        template = template,
        idempotencyKey = idempotencyKey,
    )

    fun buildPersonDocumentsUpload(
        personId: PersonId,
        description: String = "Description",
        category: PersonDocumentsUploadCategory = PersonDocumentsUploadCategory.TEST_RESULT,
        eventDate: LocalDateTime = LocalDateTime.now(),
        attachments: List<AliceFileAttachment> = emptyList(),
    ) = PersonDocumentsUpload(
        personId = personId,
        category = category,
        eventDate = eventDate,
        attachments = attachments,
        description = description,
    )

    fun buildPersonBenefit(
        personId: PersonId,
        campaignId: String = "campaignId",
        partner: CampaignPartners = CampaignPartners.MEDIPRECO,
        optedInAt: LocalDateTime = LocalDateTime.now(),
        optInStatus: PersonBenefitOptInStatus = PersonBenefitOptInStatus.ACCEPTED,
    ) = PersonBenefit(
        personId = personId,
        campaignId = campaignId,
        partner = partner,
        optedInAt = optedInAt,
        optInStatus = optInStatus,
    )

    fun buildClinicalOutcomeRecord(
        personId: PersonId = PersonId(),
        addedAt: LocalDateTime = LocalDateTime.now(),
        addedBy: ClinicalOutcomeRecord.AddedBy = ClinicalOutcomeRecord.AddedBy(
            id = null, type = ClinicalOutcomeRecord.AddedByType.MEMBER
        ),
        outcome: BigDecimal = BigDecimal.ONE,
        referenceRange: String? = "LIGHT",
        referencedLinks: List<ClinicalOutcomeRecord.ReferencedLink> = listOf(
            ClinicalOutcomeRecord.ReferencedLink(
                id = RangeUUID.generate(),
                model = ClinicalOutcomeRecord.ReferenceLinkModel.HEALTH_FORM_ANSWER_GROUP
            )
        ),
        calculatorConfUsed: ClinicalOutcomeRecord.ReferencedLink? = null,
        outcomeConfId: UUID = RangeUUID.generate(),
        caseRecordId: UUID? = null
    ) = ClinicalOutcomeRecord(
        personId = personId,
        addedAt = addedAt,
        addedBy = addedBy,
        outcome = outcome,
        referenceRange = referenceRange,
        calculatorConfUsed = calculatorConfUsed,
        referencedLinks = referencedLinks,
        outcomeConfId = outcomeConfId,
        caseRecordId = caseRecordId
    )

    fun buildPersonCalendly(
        personId: PersonId,
        id: UUID = RangeUUID.generate(),
    ) = PersonCalendly(
        personId = personId,
        id = id,
    )

    fun <T : Any> buildRandom(clazz: KClass<T>): T {
        val constructor = clazz.constructors.first()
        val parameters = try {
            processParameters(constructor.parameters)
        } catch (e: Exception) {
            logger.error("Error on random object parameter processing", e)
            throw e
        }
        return try {
            constructor.callBy(parameters)
        } catch (e: Exception) {
            logger.error("Error on random object generation", e)
            throw e
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun processParameters(parameters: List<KParameter>): Map<KParameter, Any?> =
        parameters.associateWith { param ->
            val value = when {
                param.type.jvmErasure.isData -> buildRandom(param.type.classifier!! as KClass<Any>)
                param.type.classifier == List::class -> (1..Random.nextInt(5)).map {
                    val itemClass = param.type.arguments[0].type?.classifier as KClass<*>
                    if (itemClass == UUID::class) //buildRandom() doesn't work for List<UUID>
                        RangeUUID.generate()
                    else
                        buildRandom(itemClass)
                }

                isEnum(param) -> (param.type.classifier!! as KClass<Enum<*>>).java.enumConstants.random()
                param.type.classifier == PersonId::class -> PersonId()
                param.type.classifier == Int::class -> Random.nextInt()
                param.type.classifier == Float::class -> Random.nextFloat()
                param.type.classifier == UUID::class -> RangeUUID.generate()
                param.type.classifier == Boolean::class -> Random.nextBoolean()
                param.type.classifier == LocalDateTime::class ->
                    MIN_DATE_TIME.plusSeconds(
                        Random.nextLong(
                            ChronoUnit.SECONDS.between(
                                MIN_DATE_TIME,
                                LocalDateTime.now()
                            )
                        )
                    )

                param.type.classifier == LocalDate::class ->
                    MIN_DATE.plusDays(Random.nextLong(ChronoUnit.DAYS.between(MIN_DATE, LocalDate.now())))

                param.type.classifier == String::class -> (1..Random.nextInt(100)).map {
                    Random.nextInt(
                        0,
                        CHAR_POOL.size
                    )
                }.map(CHAR_POOL::get).joinToString("")

                else -> null
            }
            value
        }

    private fun isEnum(parameter: KParameter): Boolean =
        parameter.type.classifier!!.createType().jvmErasure.isSubclassOf(Enum::class)

    fun buildHealthPlanTaskTemplate(
        id: UUID = RangeUUID.generate(),
        content: Map<String, Any>? = emptyMap(),
        type: HealthPlanTaskType = HealthPlanTaskType.EATING,
        title: String = "Comer cenoura",
        description: String = "Coma cenoure cozida no vapor ou assada, nunca crua."
    ) =
        HealthPlanTaskTemplate(
            id = id,
            type = type,
            title = title,
            description = description,
            content = content,
            frequency = Frequency(FrequencyType.TIMES, PeriodUnit.DAY, 2),
            deadline = Deadline(PeriodUnit.MONTH, 3),
            start = Start(StartType.IMMEDIATE),
            attachments = emptyList(),
            active = true
        )

    fun buildHealthMeasurementCategory(
        key: String = "ANTHROPOMETRIC ${RangeUUID.generate()}",
        name: String = "Antropométrico",
        active: Boolean = true,
    ) = HealthMeasurementCategory(
        key = key,
        name = name,
        active = active,
    )

    fun buildHealthMeasurementType(
        key: String = "HEIGHT ${RangeUUID.generate()}",
        name: String = "Altura",
        categoryId: UUID = RangeUUID.generate(),
        format: HealthMeasurementTypeFormat = HealthMeasurementTypeFormat.INTEGER,
        unit: String = "cm",
        readOnly: Boolean = false,
        active: Boolean = true,
    ) = HealthMeasurementType(
        key = key,
        name = name,
        healthMeasurementCategoryId = categoryId,
        format = format,
        unit = unit,
        readOnly = readOnly,
        active = active,
    )

    fun buildPregnancy(
        personId: PersonId = PersonId(),
        lastPeriodDate: LocalDate? = LocalDate.now(),
        firstUsgDate: LocalDate? = LocalDate.now(),
        usgEstimatedGestationalAgeInDays: Int? = 10,
        pregnancyWalletLink: String? = "https://wallet.net/${RangeUUID.generate()}",
        outcomeType: PregnancyOutcomeType? = null,
        outcomeDate: LocalDate? = null,
        babies: List<PregnancyBaby> = emptyList(),
        active: Boolean = true,
    ) = Pregnancy(
        personId = personId,
        lastPeriodDate = lastPeriodDate,
        firstUsgDate = firstUsgDate,
        usgEstimatedGestationalAgeInDays = usgEstimatedGestationalAgeInDays,
        pregnancyWalletLink = pregnancyWalletLink,
        outcomeType = outcomeType,
        outcomeDate = outcomeDate,
        babies = babies,
        active = active,
    )

    fun buildHealthFormOutcomeCalculatorConf(
        strategy: CalculatorConf.Strategy = CalculatorConf.Strategy.SUM,
        healthFormId: UUID = RangeUUID.generate(),
        questionIds: List<UUID> = listOf(RangeUUID.generate()),
        outcomeConfId: UUID = RangeUUID.generate(),
        status: CalculatorConfStatus = CalculatorConfStatus.ACTIVE,
        parameters: Map<String, Any> = emptyMap(),
    ) = HealthFormOutcomeCalculatorConf(
        strategy = strategy,
        healthFormId = healthFormId,
        questionIds = questionIds,
        outcomeConfId = outcomeConfId,
        status = status,
        parameters = parameters
    )

    fun buildClinicalOutcomesConsolidatedCalculatorConf(
        outcomeConfIds: List<UUID> = emptyList(),
        outcomeConfId: UUID = RangeUUID.generate(),
        status: CalculatorConfStatus = CalculatorConfStatus.ACTIVE,
        strategy: CalculatorConf.Strategy = CalculatorConf.Strategy.LINEAR_EQUATION,
        parameters: Map<String, Any> = emptyMap(),
    ) = ClinicalOutcomesConsolidatedCalculatorConf(
        outcomeConfIds = outcomeConfIds,
        outcomeConfId = outcomeConfId,
        status = status,
        strategy = strategy,
        parameters = parameters,
    )

    fun buildAppointmentEvolution(
        appointmentId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        description: String = "It's gonna be ok",
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) = AppointmentEvolution(
        appointmentId = appointmentId,
        staffId = staffId,
        description = description,
        createdAt = createdAt
    )

    fun buildFhirBundle(
        personId: PersonId = PersonId(),
        externalId: String = RangeUUID.generate().toString(),
        encounter: FhirEncounter? = null,
        appointments: List<FhirAppointment> = emptyList(),
        practitioners: List<FhirPractitioner> = emptyList(),
        observations: List<FhirObservation> = emptyList(),
        diagnosticReports: List<FhirSimpleDiagnosticReport> = emptyList(),
        procedures: List<FhirProcedure> = emptyList(),
        conditions: List<FhirCondition> = emptyList(),
        allergyIntolerances: List<FhirAllergyIntolerance> = emptyList(),
        medicationStatements: List<FhirMedicationStatement> = emptyList(),
        riskAssessments: List<FhirRiskAssessment> = emptyList(),
        clinicalImpressions: List<FhirClinicalImpression> = emptyList(),
        provider: ProviderIntegration = ProviderIntegration.BP,
    ) = FhirBundle(
        personId = personId,
        externalId = externalId,
        bundleType = "DISCHARGE_SUMMARY",
        encounter = encounter,
        appointments = appointments,
        practitioners = practitioners,
        observations = observations,
        diagnosticReports = diagnosticReports,
        procedures = procedures,
        conditions = conditions,
        allergyIntolerances = allergyIntolerances,
        medicationStatements = medicationStatements,
        riskAssessments = riskAssessments,
        clinicalImpressions = clinicalImpressions,
        provider = provider,
    )

    fun buildProductPriceAdjustment(
        id: UUID = RangeUUID.generate(),
        startDate: LocalDate = LocalDate.now(),
        endDate: LocalDate = LocalDate.now().plusYears(1),
        percentage: BigDecimal = 10.0.toBigDecimal(),
        regulatoryCode: String = "xyz",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = ProductPriceAdjustment(
        id = id,
        startDate = startDate,
        endDate = endDate,
        percentage = percentage,
        regulatoryCode = regulatoryCode,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildMemberProductPriceAdjustment(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        productPriceAdjustmentId: UUID = RangeUUID.generate(),
        adjustedMemberProductPriceId: UUID = RangeUUID.generate(),
        baseMemberProductPriceId: UUID = RangeUUID.generate(),
        referenceDate: LocalDate = LocalDate.now(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = MemberProductPriceAdjustment(
        id = id,
        memberId = memberId,
        productPriceAdjustmentId = productPriceAdjustmentId,
        adjustedMemberProductPriceId = adjustedMemberProductPriceId,
        baseMemberProductPriceId = baseMemberProductPriceId,
        referenceDate = referenceDate,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildB2bBatchInvoiceReport(
        id: UUID = RangeUUID.generate(),
        billingAccountablePartyId: UUID,
        memberId: UUID,
        totalAmount: BigDecimal,
        productPrice: BigDecimal? = null,
        proRation: BigDecimal? = null,
        copay: BigDecimal? = null,
        productChange: BigDecimal? = null,
        promoCode: BigDecimal? = null,
        promoCodeResult: BigDecimal? = null,
        sales: BigDecimal? = null,
        salesResult: BigDecimal? = null,
        generatedAt: LocalDateTime = LocalDateTime.now(),
    ) = B2bBatchInvoiceReport(
        id = id,
        billingAccountablePartyId = billingAccountablePartyId,
        memberId = memberId,
        totalAmount = totalAmount,
        productPrice = productPrice,
        proRation = proRation,
        copay = copay,
        productChange = productChange,
        promoCode = promoCode,
        promoCodeResult = promoCodeResult,
        sales = sales,
        salesResult = salesResult,
        generatedAt = generatedAt,
    )

    fun buildCompany(
        id: UUID = RangeUUID.generate(),
        parentId: UUID? = null,
        externalCode: String? = null,
        name: String = "Acme",
        legalName: String = "Acme LTDA.",
        cnpj: String = "00.000.000/0001-00",
        email: String = "<EMAIL>",
        phoneNumber: String = "+*************",
        address: CompanyAddress = CompanyAddress(
            postalCode = "12345-123",
            street = "Av. Paulista",
            number = 1000,
            city = "São Paulo",
            State = "SP",
            neighborhood = "Pinheiros"
        ),
        bankingInfo: CompanyBankingInfo = CompanyBankingInfo(
            bankCode = 0,
            agencyNumber = "0000",
            accountNumber = "00000-0"
        ),
        billingAccountablePartyId: UUID? = RangeUUID.generate(),
        availableProducts: List<UUID>? = listOf(RangeUUID.generate()),
        defaultProductId: UUID = RangeUUID.generate(),
        flexBenefit: Boolean? = null,
        hasEmployeesAbroad: Boolean = false,
        contractUrls: List<String> = listOf("url-1", "url-2"),
        priceAdjustmentType: PriceAdjustmentType = PriceAdjustmentType.POOL,
        contractStartedAt: LocalDateTime = LocalDateTime.now(),
        beneficiariesCountAtDayZero: Int = 40,
        defaultFlowType: BeneficiaryOnboardingFlowType? = null,
        version: Int = 0,
        brand: Brand? = Brand.ALICE,
        totvsContract: String? = null,
        totvsSubcontract: String? = null,
        externalBrandId: String? = null,
        contractIds: List<UUID> = emptyList(),
        status: CompanyStatus? = null,
        companySize: CompanySize? = null,
        companyBusinessUnit: CompanyBusinessUnit? = null,
        updatedBy: UpdatedBy? = null
    ) = Company(
        id = id,
        parentId = parentId,
        externalCode = externalCode,
        name = name,
        legalName = legalName,
        cnpj = cnpj,
        email = email,
        phoneNumber = phoneNumber,
        address = address,
        bankingInfo = bankingInfo,
        billingAccountablePartyId = billingAccountablePartyId,
        availableProducts = availableProducts,
        defaultProductId = defaultProductId,
        flexBenefit = flexBenefit,
        hasEmployeesAbroad = hasEmployeesAbroad,
        contractsUrls = contractUrls,
        priceAdjustmentType = priceAdjustmentType,
        contractStartedAt = contractStartedAt,
        beneficiariesCountAtDayZero = beneficiariesCountAtDayZero,
        defaultFlowType = defaultFlowType,
        version = version,
        brand = brand,
        totvsContract = totvsContract,
        totvsSubContract = totvsSubcontract,
        externalBrandId = externalBrandId,
        status = status,
        contractIds = contractIds,
        companySize = companySize,
        companyBusinessUnit = companyBusinessUnit,
        updatedBy = updatedBy
    )

    fun buildCompanyContract(
        id: UUID = RangeUUID.generate(),
        externalId: String? = null,
        title: String = "contrato abc",
        billingAccountablePartyId: UUID? = null,
        startedAt: LocalDate? = LocalDate.now(),
        accountableEmail: String = "<EMAIL>",
        contractFileIds: List<ContractFile> = emptyList(),
        isProRata: Boolean? = null,
        defaultProductId: UUID? = null,
        availableProducts: List<UUID>? = null,
        dueDate: Int = 10,
        isBillingLevel: Boolean = false,
        groupCompany: String? = null,
        paymentType: PaymentModel? = null,
        termStartType: TermStartType? = null,
        nature: String? = "20009",
        status: CompanyContractStatus? = null,
        beneficiaryCountAtDayZero: Int? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
    ) = CompanyContract(
        id = id,
        externalId = externalId,
        title = title,
        billingAccountablePartyId = billingAccountablePartyId,
        startedAt = startedAt,
        accountableEmail = accountableEmail,
        contractFileIds = contractFileIds,
        isProRata = isProRata,
        defaultProductId = defaultProductId,
        availableProducts = availableProducts,
        dueDate = dueDate,
        isBillingLevel = isBillingLevel,
        groupCompany = groupCompany,
        paymentType = paymentType,
        termStartType = termStartType,
        nature = nature,
        status = status,
        beneficiaryCountAtDayZero = beneficiaryCountAtDayZero,
        createdAt = createdAt
    )

    fun buildCompanySubContract(
        id: UUID = RangeUUID.generate(),
        externalId: String? = "1234567",
        title: String = "plano abc",
        companyId: UUID = RangeUUID.generate(),
        contractId: UUID = RangeUUID.generate(),
        hasEmployeesAbroad: Boolean = true,
        billingAccountablePartyId: UUID? = null,
        isProRata: Boolean? = null,
        hasRetroactiveCharge: Boolean? = null,
        defaultProductId: UUID? = null,
        availableProducts: List<UUID>? = null,
        dueDate: Int = 10,
        isBillingLevel: Boolean = false,
        paymentType: PaymentModel? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        nature: String? = "20009",
        billingGroup: String? = "0004",
        availableCompanyProductPriceListing: List<UUID>? = null,
        flexBenefit: Boolean? = null,
    ) = CompanySubContract(
        id = id,
        externalId = externalId,
        title = title,
        billingAccountablePartyId = billingAccountablePartyId,
        isProRata = isProRata,
        hasRetroactiveCharge = hasRetroactiveCharge,
        defaultProductId = defaultProductId,
        availableProducts = availableProducts,
        dueDate = dueDate,
        isBillingLevel = isBillingLevel,
        paymentType = paymentType,
        companyId = companyId,
        contractId = contractId,
        hasEmployeesAbroad = hasEmployeesAbroad,
        createdAt = createdAt,
        billingGroup = billingGroup,
        nature = nature,
        avaliableCompanyProductPriceListing = availableCompanyProductPriceListing,
        flexBenefit = flexBenefit
    )

    fun buildCompanyProductConfiguration(
        id: UUID = RangeUUID.generate(),
        companyProductType: CompanyProductType = CompanyProductType.MICRO_2_5_OPTIONAL,
        availableProductIds: List<UUID> = listOf(RangeUUID.generate()),
        productPriceListIds: List<ProductPriceListingConfiguration> = emptyList(),
        configurationVersion: ConfigurationVersion = ConfigurationVersion.STABLE
    ) = CompanyProductConfiguration(
        id = id,
        companyProductType = companyProductType,
        availableProductIds = availableProductIds,
        productPriceListIds = productPriceListIds,
        configurationVersion = configurationVersion
    )

    fun buildBeneficiary(
        id: UUID = RangeUUID.generate(),
        parentBeneficiary: UUID? = null,
        personId: PersonId = PersonId(),
        memberId: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        companySubContractId: UUID? = RangeUUID.generate(),
        type: BeneficiaryType = BeneficiaryType.EMPLOYEE,
        contractType: BeneficiaryContractType? = null,
        parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
        activatedAt: LocalDateTime = LocalDateTime.now(),
        canceledAt: LocalDateTime? = null,
        hiredAt: LocalDateTime? = LocalDateTime.now(),
        parentBeneficiaryRelatedAt: LocalDateTime? = null,
        cnpj: String? = null,
        hasContributed: Boolean? = null,
        dependents: List<Beneficiary>? = null,
        version: Int = 0,
        onboarding: BeneficiaryOnboarding? = null,
        canceledReason: BeneficiaryCancelationReason? = null,
        canceledDescription: String? = null,
        archived: Boolean = false,
        brand: Brand? = Brand.ALICE,
        memberStatus: MemberStatus? = MemberStatus.PENDING,
        parentPerson: PersonId? = null,
        gracePeriodType: GracePeriodType? = null,
        gracePeriodTypeReason: GracePeriodTypeReason? = null,
        gracePeriodBaseDate: LocalDate? = null,
        updatedBy: UpdatedBy? = null
    ) = Beneficiary(
        id = id,
        parentBeneficiary = parentBeneficiary,
        personId = personId,
        memberId = memberId,
        companyId = companyId,
        companySubContractId = companySubContractId,
        type = type,
        contractType = contractType,
        parentBeneficiaryRelationType = parentBeneficiaryRelationType,
        activatedAt = activatedAt,
        canceledAt = canceledAt,
        hiredAt = hiredAt,
        parentBeneficiaryRelatedAt = parentBeneficiaryRelatedAt,
        dependents = dependents,
        version = version,
        onboarding = onboarding,
        canceledReason = canceledReason,
        canceledDescription = canceledDescription,
        archived = archived,
        cnpj = cnpj,
        hasContributed = hasContributed,
        brand = brand,
        memberStatus = memberStatus,
        parentPerson = parentPerson,
        gracePeriodType = gracePeriodType,
        gracePeriodTypeReason = gracePeriodTypeReason,
        gracePeriodBaseDate = gracePeriodBaseDate,
        updatedBy = updatedBy
    )

    fun buildBeneficiaryOnboarding(
        id: UUID = RangeUUID.generate(),
        beneficiaryId: UUID = RangeUUID.generate(),
        flowType: BeneficiaryOnboardingFlowType = BeneficiaryOnboardingFlowType.FULL_RISK_FLOW,
        initialProductId: UUID = RangeUUID.generate(),
        phases: List<BeneficiaryOnboardingPhase> = emptyList(),
        version: Int = 0,
    ) = BeneficiaryOnboarding(
        id = id,
        beneficiaryId = beneficiaryId,
        flowType = flowType,
        initialProductId = initialProductId,
        version = version,
        phases = phases,
    )

    fun buildBeneficiaryOnboardingPhase(
        id: UUID = RangeUUID.generate(),
        beneficiaryOnboardingId: UUID = RangeUUID.generate(),
        phase: BeneficiaryOnboardingPhaseType = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
        transactedAt: LocalDateTime = LocalDateTime.now(),
        finishedAt: LocalDateTime? = null,
        version: Int = 0,
    ) = BeneficiaryOnboardingPhase(
        id = id,
        beneficiaryOnboardingId = beneficiaryOnboardingId,
        phase = phase,
        transactedAt = transactedAt,
        finishedAt = finishedAt,
        version = version,
    )

    fun buildBeneficiaryCompiledView(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        companySubContractId: UUID? = null,
        personId: PersonId = PersonId(),
        personNationalId: String = "*********12",
        personEmail: String = "<EMAIL>",
        personFullSocialName: String = "Beneficiário de Teste",
        beneficiaryId: UUID = RangeUUID.generate(),
        beneficiaryType: BeneficiaryType = BeneficiaryType.EMPLOYEE,
        beneficiaryContractType: BeneficiaryContractType = BeneficiaryContractType.CLT,
        productId: UUID = RangeUUID.generate(),
        productTitle: String = "Título do Produto",
        memberId: UUID = RangeUUID.generate(),
        memberStatus: MemberStatus = MemberStatus.ACTIVE,
        immersionStatus: BeneficiaryViewImmersionStatus = BeneficiaryViewImmersionStatus.SCHEDULED,
        insuranceStatus: BeneficiaryViewInsuranceStatus = BeneficiaryViewInsuranceStatus.ACTIVE,
        lastOnboardingPhase: BeneficiaryOnboardingPhaseType = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD,
        flowType: BeneficiaryOnboardingFlowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
        viewUpdatedAt: LocalDateTime = LocalDateTime.now(),
        version: Int = 0,
        memberProductChangeSchedule: MemberProductChangeSchedule? = null,
        newProductRequest: Product? = null,
        parentPerson: Person? = null,
        personBirthDate: LocalDate? = null,
    ) = BeneficiaryCompiledView(
        id = id,
        companyId = companyId,
        companySubContractId = companySubContractId,
        personId = personId,
        personNationalId = personNationalId,
        personFullSocialName = personFullSocialName,
        personEmail = personEmail,
        parentPersonId = parentPerson?.id,
        parentPersonFullName = parentPerson?.fullSocialName,
        beneficiaryId = beneficiaryId,
        beneficiaryType = beneficiaryType,
        beneficiaryContractType = beneficiaryContractType,
        productId = productId,
        productTitle = productTitle,
        memberId = memberId,
        memberStatus = memberStatus,
        immersionStatus = immersionStatus,
        insuranceStatus = insuranceStatus,
        lastOnboardingPhase = lastOnboardingPhase,
        flowType = flowType,
        viewUpdatedAt = viewUpdatedAt,
        version = version,
        requestProductId = newProductRequest?.id,
        requestProductDisplayName = newProductRequest?.displayName,
        requestProductApplyAt = memberProductChangeSchedule?.applyAt,
        personBirthDate = personBirthDate
    )

    fun buildCassiMember(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        accountNumber: String? = "**********",
        startDate: LocalDateTime? = LocalDateTime.now(),
        expirationDate: LocalDateTime? = LocalDateTime.now().plusYears(1),
        version: Int = 0,
    ) = CassiMember(
        id = id,
        memberId = memberId,
        accountNumber = accountNumber,
        startDate = startDate,
        expirationDate = expirationDate,
        version = version,
    )

    fun buildMemberContract(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        terms: List<MemberContractTerm>? = null,
        signature: UserSignature? = null,
        version: Int = 0,
    ) = MemberContract(
        id = id,
        memberId = memberId,
        terms = terms,
        signature = signature,
        version = version
    )

    fun buildBeneficiaryHubspot(
        id: UUID = RangeUUID.generate(),
        beneficiaryId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        companyId: UUID = RangeUUID.generate(),
        externalContactId: String = "4353413",
        externalDealId: String = "*********",
        version: Int = 0,
    ) = BeneficiaryHubspot(
        id = id,
        beneficiaryId = beneficiaryId,
        personId = personId,
        companyId = companyId,
        externalContactId = externalContactId,
        externalDealId = externalDealId,
        version = version,
    )

    fun buildCompanyStaff(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        firstName: String = "Usuário",
        lastName: String = "Plataforma RH",
        email: String = "<EMAIL>",
        role: CompanyStaffRole = CompanyStaffRole.MAIN_COMPANY_STAFF,
        archivedAt: LocalDateTime? = null,
        transactedAt: LocalDateTime = LocalDateTime.now(),
        version: Int = 0,
    ) = CompanyStaff(
        id = id,
        companyId = companyId,
        transactedAt = transactedAt,
        version = version,
        firstName = firstName,
        lastName = lastName,
        email = email,
        role = role,
        archivedAt = archivedAt
    )

    fun buildPersonContractualRisk(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        riskWeight: BigDecimal = BigDecimal(10.0),
        riskLevel: PersonContractualRiskLevel = PersonContractualRiskLevel.LOW_RISK,
        version: Int = 0,
        calculationDetails: CalculationDetails? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        totalMonthlyCost: BigDecimal? = null,
    ) = PersonContractualRisk(
        id = id,
        personId = personId,
        riskWeight = riskWeight,
        riskLevel = riskLevel,
        version = version,
        calculationDetails = calculationDetails,
        totalMonthlyCost = totalMonthlyCost,
        createdAt = createdAt
    )

    fun buildCompanyContractMaco(
        id: UUID = RangeUUID.generate(),
        contractId: UUID = RangeUUID.generate(),
        beneficiaryCount: Int = 10,
        totalGrossRevenue: BigDecimal = BigDecimal(100.0),
        totalStandardCost: BigDecimal = BigDecimal(50.0),
        totalCostRisk: BigDecimal = BigDecimal(10.0),
        totalCostWeight: BigDecimal = BigDecimal(5.0),
        totalWeightedIncome: BigDecimal = BigDecimal(100.0),
        totalStandardIncome: BigDecimal = BigDecimal(50.0),
        totalRiskIncome: BigDecimal = BigDecimal(10.0),
        beneficiaryMacoIds: List<UUID> = emptyList(),
        targetMacoLimit: BigDecimal = BigDecimal(100.0),
        createdAt: LocalDateTime = LocalDateTime.now(),
        riskAssessmentStatus: RiskAssessmentStatus? = null,
        riskAssessmentReasonType: RiskAssessmentReasonType? = null,
        riskAssessmentReason: String? = null,
        version: Int = 0,
        updatedBy: UpdatedBy? = null,
    ) = CompanyContractMaco(
        id = id,
        contractId = contractId,
        beneficiaryCount = beneficiaryCount,
        totalGrossRevenue = totalGrossRevenue,
        totalStandardCost = totalStandardCost,
        totalCostRisk = totalCostRisk,
        totalCostWeight = totalCostWeight,
        totalWeightedIncome = totalWeightedIncome,
        totalStandardIncome = totalStandardIncome,
        totalRiskIncome = totalRiskIncome,
        beneficiaryMacoIds = beneficiaryMacoIds,
        targetMacoLimit = targetMacoLimit,
        riskAssessmentStatus = riskAssessmentStatus,
        riskAssessmentReasonType = riskAssessmentReasonType,
        riskAssessmentReason = riskAssessmentReason,
        createdAt = createdAt,
        version = version,
        updatedBy = updatedBy
    )

    fun buildBeneficiaryMaco(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        beneficiaryId: UUID = RangeUUID.generate(),
        standardCostDetails: StandardCostDetails = StandardCostDetails(
            standardCost = BigDecimal(10.0),
            age = 10,
            adhesion = "adhesion",
            productAnsNumber = "productAnsNumber",
            companySize = "companySize",
            companyBusinessUnit = "companyBusinessUnit",
            standardCostId = RangeUUID.generate(),
            sex = Sex.FEMALE
        ),
        contractId: UUID = RangeUUID.generate(),
        productId: UUID = RangeUUID.generate(),
        riskLevel: PersonContractualRiskLevel = PersonContractualRiskLevel.LOW_RISK,
        riskWeight: BigDecimal = BigDecimal(10.0),
        grossRevenue: BigDecimal = BigDecimal(10.0),
        standardCost: BigDecimal = BigDecimal(10.0),
        personalizedCost: BigDecimal = BigDecimal(10.0),
        createdAt: LocalDateTime = LocalDateTime.now(),
        version: Int = 0,
    ) = BeneficiaryMaco(
        id = id,
        personId = personId,
        beneficiaryId = beneficiaryId,
        standardCostDetails = standardCostDetails,
        contractId = contractId,
        productId = productId,
        riskLevel = riskLevel,
        riskWeight = riskWeight,
        grossRevenue = grossRevenue,
        standardCost = standardCost,
        personalizedCost = personalizedCost,
        createdAt = createdAt,
        version = version
    )

    fun buildPersonHealthConditionContractualRisk(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        healthConditionId: UUID = RangeUUID.generate(),
        healthConditionDescription: String = "description",
        staffId: UUID = RangeUUID.generate(),
        factor: Int = 10,
        baseRiskRating: Int = 5,
        finalRiskRating: Int = 10,
        reason: String = "reason",
        code: String = "code",
        codeType: Disease.Type = CID_10,
        version: Int = 0,
        monthlyCost: BigDecimal? = null,
        suggestedMonthlyCost: BigDecimal? = null,
    ) = PersonHealthConditionContractualRisk(
        id = id,
        personId = personId,
        healthConditionId = healthConditionId,
        healthConditionDescription = healthConditionDescription,
        staffId = staffId,
        factor = factor,
        baseRiskRating = baseRiskRating,
        finalRiskRating = finalRiskRating,
        reason = reason,
        code = code,
        codeType = codeType,
        monthlyCost = monthlyCost,
        suggestedMonthlyCost = suggestedMonthlyCost,
        version = version,
    )

    fun buildFhirObservation(
        name: String = "COVID",
        valueString: String = "Não Detectado",
        referenceRange: List<FhirReferenceRange> = listOf(FhirReferenceRange(text = "Não Detectado")),
    ) = FhirObservation(
        id = "observation-01",
        code = FhirCodeableConcept(
            coding = listOf(
                FhirCoding(
                    code = "39951",
                    system = "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem",
                    display = name
                ),
                FhirCoding(
                    code = "40302512",
                    system = "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo",
                    display = name,
                )
            )
        ),
        status = "final",
        basedOn = listOf(FhirReference(type = "ServiceRequest")),
        performer = listOf(
            FhirReference(
                type = "Practitioner",
                display = "Nome do medico"
            )
        ),
        identifier = listOf(
            FhirIdentifier(
                use = "usual",
                value = "916100034874-39951-39951",
                system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicoes-codigoOrigem"
            )
        ),
        valueString = valueString,
        resourceType = "Observation",
        referenceRange = referenceRange,
        effectiveDateTime = "2021-02-10T20:09:38-03:00",
    )

    fun buildFhirObservationWithComponent() = FhirObservation(
        id = "observation-01",
        status = "final",
        basedOn = listOf(FhirReference(type = "ServiceRequest")),
        code = FhirCodeableConcept(
            coding = listOf(
                FhirCoding(
                    code = "39951",
                    system = "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem",
                    display = "COVID"
                )
            )
        ),
        performer = listOf(
            FhirReference(
                type = "Practitioner",
                display = "Nome do medico"
            )
        ),
        identifier = listOf(
            FhirIdentifier(
                use = "usual",
                value = "916100034874-39951-39951",
                system = "https://interoperabilidade.dasa.com.br/fhir/NamingSystem/dasa-motion-requisicoes-codigoOrigem"
            )
        ),
        resourceType = "Observation",
        effectiveDateTime = "2021-02-10T20:09:38-03:00",
        component = listOf(
            FhirComponent(
                valueString = "Não Detectado",
                referenceRange = listOf(
                    FhirReferenceRange(
                        text = "Não Detectado"
                    )
                ),
                code = FhirCodeableConcept(
                    coding = listOf(
                        FhirCoding(
                            code = "39951",
                            system = "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/dasa-motion-exames-codigoOrigem",
                            display = "COVID"
                        ),
                        FhirCoding(
                            code = "40302512",
                            system = "https://interoperabilidade.dasa.com.br/fhir/CodeSystem/ans-tuss-procedimento-codigo",
                            display = "COVID",
                        )
                    )
                )
            )
        )
    )

    fun buildFhirMedicationStatements() = FhirMedicationStatement(
        id = RangeUUID.generate().toString(),
        resourceType = "MedicationStatement",
        meta = FhirMeta(
            source = "Provider Source",
            lastUpdate = null,
        ),
        note = listOf(
            FhirAnnotation(
                text = "SEKI"
            ),
            FhirAnnotation(
                text = "Tomar 5 mililitros (ml) 3 vezes por dia"
            )
        ),
        context = FhirReference(type = "Encounter"),
        subject = FhirReference(type = "Patient"),
        informationSource = FhirReference(type = "Practitioner"),
        effectiveDateTime = "2021-12-14T21:19:59.349"
    )

    fun buildFhirRiskAssessment() = FhirRiskAssessment(
        id = RangeUUID.generate().toString(),
        resourceType = "RiskAssessment",
        meta = FhirMeta(
            source = "Provider Source",
            lastUpdate = null,
        ),
        subject = FhirReference(),
        performer = FhirReference(),
        occurrencePeriod = FhirPeriod(
            start = "2022-01-26T14:39:34Z",
            end = "2022-01-26T14:38:50Z"
        ),
        reasonCodeableConcept = FhirCodeableConcept(
            text = "Urgente",
            coding = listOf(
                FhirCoding(
                    code = "Manchester",
                    display = "Urgente"
                )
            )
        ),
        status = "final",
    )

    fun buildClinicalImpressions() = FhirClinicalImpression(
        id = RangeUUID.generate().toString(),
        resourceType = "ClinicalImpression",
        meta = FhirMeta(
            source = "Provider Source",
            lastUpdate = null,
        ),
        subject = FhirReference(),
        summary = "Anamnese Medica PS BP - Queixa Principal",
        assessor = FhirReference(),
        description = "1. Queixa e Duração:\nvitima de queda de bicicleta hoje com queixa de dor no cotovelo esquerdo. trazida pela mãe\n\nOBS: Estou tentando internar a criança desde as 13:00 horas",
        status = "final",
    )

    fun buildFhirCondition(notes: List<FhirAnnotation> = listOf(FhirAnnotation("nega cafe"))) = FhirCondition(
        id = RangeUUID.generate().toString(),
        resourceType = "Condition",
        meta = FhirMeta(
            source = "Provider Source",
            lastUpdate = null,
        ),
        subject = FhirReference(type = "Patient"),
        asserter = FhirReference(type = "Practitioner"),
        encounter = FhirReference(type = "Encounter"),
        note = notes
    )

    fun buildFhirDiagnosticReport(
        observations: List<FhirObservation> = emptyList(),
        personId: PersonId = PersonId(),
        servicesRequest: List<FhirServiceRequest> = emptyList(),
        encounters: List<FhirEncounter> = emptyList(),
    ): FhirDiagnosticReport {
        return FhirDiagnosticReport(
            personId = personId,
            externalId = RangeUUID.generate().toString(),
            provider = "DASA",
            patients = emptyList(),
            observations = observations,
            servicesRequest = servicesRequest,
            coverages = emptyList(),
            practitioners = emptyList(),
            medias = emptyList(),
            imagingStudies = emptyList(),
            conclusionCodes = emptyList(),
            code = FhirCodeableConcept(),
            conclusion = null,
            implicitRules = null,
            issued = null,
            language = null,
            meta = null,
            status = "",
            subject = FhirReference(),
            categories = emptyList(),
            performers = emptyList(),
            results = emptyList(),
            presentedForms = emptyList(),
            encounters = encounters
        )
    }

    fun buildFhirEncounter(): FhirEncounter {
        return FhirEncounter(
            id = RangeUUID.generate().toString(),
            meta = FhirMeta(
                source = "Provider Source",
                lastUpdate = null,
            ),
            resourceType = "Encounter",
            period = FhirPeriod(
                end = "2021-11-04T17:02:48Z",
                start = "2021-10-25T17:44:46Z"
            ),
            identifier = listOf(
                FhirIdentifier(
                    value = RangeUUID.generate().toString()
                )
            ),
            `class` = FhirCoding(
                code = "emergency"
            ),
            participant = listOf(
                FhirParticipant(
                    individual = FhirReference(
                        display = "Practitioner Name",
                    ),
                    type = listOf(
                        FhirCodeableConcept(
                            coding = listOf(
                                FhirCoding(
                                    display = "Clinica Médica"
                                )
                            ),
                            text = "Clinica Médica"
                        )
                    ),
                    period = null,
                )
            ),
            status = "finish",
            implicitRules = null,
            language = null,
            priority = null,
            serviceProvider = null,
            serviceType = null,
            location = listOf(
                FhirLocation(
                    FhirReference(
                        type = "Location",
                        identifier = FhirIdentifier(value = "46"),
                        display = "Provider Unit"
                    )
                )
            )
        )
    }

    fun buildInsurancePortabilityHealthInsurance(
        name: String = "Bradesco",
        portabilityLetterGuide: List<InsurancePortabilityLetterGuideStep> = listOf(
            InsurancePortabilityLetterGuideStep(order = 1, description = "Tenha em mão os documentos de identificação"),
            InsurancePortabilityLetterGuideStep(order = 2, description = "Ligue para (00)XXXX-XXXX")
        ),
    ) = InsurancePortabilityHealthInsurance(
        name = name,
        portabilityLetterGuide = portabilityLetterGuide,
        color = "#cc092f",
    )

    fun buildVideoCall(
        externalId: UUID = RangeUUID.generate(),
        channelId: String = "xpto",
        personId: PersonId = PersonId(),
        type: VideoCallType = VideoCallType.ALICE_AGORA,
        status: VideoCallStatus = VideoCallStatus.IN_PROGRESS,
        staffId: UUID = RangeUUID.generate(),
        memberJoinedAt: LocalDateTime = LocalDateTime.now(),
        mediaCaptureId: UUID = RangeUUID.generate()
    ) = VideoCall(
        externalId = externalId,
        channelId = channelId,
        personId = personId,
        type = type,
        status = status,
        startedByStaffId = staffId,
        memberJoinedAt = memberJoinedAt,
        mediaCaptureId = mediaCaptureId
    )

    fun buildProviderReadTracking(
        provider: ProviderIntegration = ProviderIntegration.BP,
        personId: PersonId = PersonId(),
        requestedData: RequestedData = RequestedData(LocalDateTime.MIN, LocalDateTime.now())
    ) = ProviderReadTracking(
        provider = provider,
        personId = personId,
        requestedData = requestedData
    )

    fun buildHealthcareMap(
        personId: PersonId = PersonId(),
        healthcareTeamId: UUID = RangeUUID.generate(),
        riskValue: Int? = 1,
        riskDescription: RiskDescription? = RiskDescription.NO_RISK,
        cidGroups: List<UUID> = emptyList(),
        cases: List<HealthcareMap.HealthcareMapCase> = emptyList(),
        riskAddedAt: LocalDateTime? = LocalDateTime.now(),
        caseRecordAddedAt: LocalDateTime = LocalDateTime.now(),
        totalCompensated: Int = 0,
        totalDecompensated: Int = 0,
        healthcareTeamAddedAt: LocalDateTime = LocalDateTime.now(),
        healthEvents: HealthcareMap.HealthEvents = HealthcareMap.HealthEvents(
            nextHealthEvent = HealthcareMap.HealthEvent(
                id = RangeUUID.generate(),
                category = PersonHealthEventCategory.APPOINTMENT_FOLLOW_UP,
                title = "Retorno médico",
                dueDate = LocalDateTime.now()
            ),
            lastHealthEvent = HealthcareMap.HealthEvent(
                id = RangeUUID.generate(),
                category = PersonHealthEventCategory.APPOINTMENT_FOLLOW_UP,
                title = "Retorno médico",
                dueDate = LocalDateTime.now()
            )
        ),
        referenceNurseGroupId: UUID? = null,
        multiStaffIds: List<UUID> = emptyList(),
        public: HealthcareMap.Public = HealthcareMap.Public.DEFAULT
    ) = HealthcareMap(
        personId = personId,
        healthcareTeamId = healthcareTeamId,
        riskValue = riskValue,
        riskDescription = riskDescription,
        healthEvents = healthEvents,
        cidGroups = cidGroups,
        cases = cases,
        riskAddedAt = riskAddedAt,
        caseRecordAddedAt = caseRecordAddedAt,
        totalCompensated = totalCompensated,
        totalDecompensated = totalDecompensated,
        healthcareTeamAddedAt = healthcareTeamAddedAt,
        referenceNurseGroupId = referenceNurseGroupId,
        multiStaffIds = multiStaffIds,
        public = public
    )

    fun buildChannel(
        id: UUID = RangeUUID.generate(),
        channelId: String = RangeUUID.generate().toString().replace("-", ""),
        personId: PersonId = PersonId(),
        staffId: UUID = RangeUUID.generate(),
        name: String? = "Dor no mindinho",
        type: ChannelType = ChannelType.ASSISTANCE_CARE,
        status: ChannelStatus = ChannelStatus.ACTIVE,
        ownerStaffId: UUID? = staffId,
        staff: Map<UUID, ChannelStaff> = mapOf(staffId to ChannelStaff(staffId, false)),
        staffHistory: Map<UUID, ChannelStaff> = emptyMap(),
        staffIds: List<UUID> = staff.keys.toList(),
        tags: List<String> = emptyList(),
        timeLastMessage: LocalDateTime? = LocalDateTime.now(),
        isArchived: Boolean = false,
        archivedAt: LocalDateTime? = LocalDateTime.now(),
        lastSync: LocalDateTime? = LocalDateTime.now(),
        origin: String? = null,
        mergedWith: String? = RangeUUID.generate().toString().replace("-", ""),
        appVersion: String? = "2.16.1 (12348) android",
        becameAsyncAt: LocalDateTime? = LocalDateTime.now(),
        channelPersonType: UserType? = UserType.MEMBER,
        inProgress: Boolean? = false,
        waitingSince: LocalDateTime? = LocalDateTime.now(),
        followUp: Map<String, Any>? = null,
        starred: StarredAction = FOLLOW_UP,
        assessment: ChannelAssessment? = ChannelAssessment.EMERGENCY,
        hasAppointment: Boolean = false,
        kind: ChannelKind? = ChannelKind.CHAT,
        category: ChannelCategory? = ChannelCategory.ASSISTANCE,
        subCategory: ChannelSubCategory? = ChannelSubCategory.LONGITUDINAL,
        subCategoryClassifier: ChannelSubCategoryClassifier? = ChannelSubCategoryClassifier.HEALTH_TEAM,
        demands: List<Demand> = listOf(
            Demand(
                caseId = RangeUUID.generate(),
                startedAt = LocalDateTime.now(),
                description = Disease(
                    type = CID_10,
                    value = "A10",
                    description = "CID A10"
                ),
                severity = CaseSeverity.DECOMPENSATED,
            )
        ),
        appointmentIds: List<UUID> = listOf(RangeUUID.generate()),
        screeningStatus: ScreeningStatus? = null,
        screeningFinishedAt: LocalDateTime? = null,
        screeningNavigationId: UUID? = null
    ) =
        Channel(
            id = id,
            channelId = channelId,
            personId = personId,
            name = name,
            type = type,
            status = status,
            ownerStaffId = ownerStaffId,
            staff = staff,
            staffHistory = staffHistory,
            staffIds = staffIds,
            tags = tags,
            timeLastMessage = timeLastMessage,
            isArchived = isArchived,
            archivedAt = archivedAt,
            lastSync = lastSync,
            origin = origin,
            mergedWith = mergedWith,
            appVersion = appVersion,
            becameAsyncAt = becameAsyncAt,
            channelPersonType = channelPersonType,
            inProgress = inProgress,
            waitingSince = waitingSince,
            followUp = followUp,
            starred = starred,
            screening = assessment?.let { Screening(it) },
            hasAppointment = hasAppointment,
            kind = kind,
            category = category,
            subCategory = subCategory,
            subCategoryClassifier = subCategoryClassifier,
            demands = demands,
            appointmentIds = appointmentIds,
            screeningStatus = screeningStatus,
            screeningFinishedAt = screeningFinishedAt,
            screeningNavigationId = screeningNavigationId
        )

    fun buildInsurancePortabilityRequestFile(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        insurancePortabilityRequestId: UUID = RangeUUID.generate(),
        file: AliceFile = buildAliceFile(),
        type: InsurancePortabilityRequestFileType = InsurancePortabilityRequestFileType.LETTER,
    ) = InsurancePortabilityRequestFile(
        id = id,
        insurancePortabilityRequestId = insurancePortabilityRequestId,
        file = file,
        type = type,
        personId = personId,
    )

    fun buildRiskGroup(
        id: UUID = RangeUUID.generate(),
        name: String = "Diabetes",
        key: String = "DIABETES",
        status: Status = Status.ACTIVE,
        type: RiskGroup.Type = RiskGroup.Type.DEFAULT
    ) = RiskGroup(
        id = id,
        name = name,
        key = key,
        status = status,
        type = type
    )

    fun buildRiskCalculation(
        id: UUID = RangeUUID.generate(),
        healthConditionId: UUID = RangeUUID.generate(),
        riskGroupId: UUID? = null,
        chronicScore: Int = 0,
        severeScore: Int = 0,
        decompensatedScore: Int = 0,
        childChronicScore: Int = 0,
        childSevereScore: Int = 0,
        childDecompensatedScore: Int = 0
    ) = RiskCalculationConf(
        id = id,
        healthConditionId = healthConditionId,
        riskGroupId = riskGroupId,
        chronicScore = chronicScore,
        severeScore = severeScore,
        decompensatedScore = decompensatedScore,
        childChronicScore = childChronicScore,
        childSevereScore = childSevereScore,
        childDecompensatedScore = childDecompensatedScore
    )

    fun buildRisk(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        calculatedValue: Int? = null,
        finalValue: Int = 0,
        riskDescription: RiskDescription = RiskDescription.NO_RISK,
        addedBy: Risk.AddedBy = Risk.AddedBy(type = Risk.AddedByType.SYSTEM),
        addedAt: LocalDateTime = LocalDateTime.now(),
        referencedModels: List<Risk.ReferencedModel> = emptyList(),
    ) = Risk(
        id = id,
        personId = personId,
        calculatedValue = calculatedValue,
        finalValue = finalValue,
        riskDescription = riskDescription,
        addedBy = addedBy,
        addedAt = addedAt,
        referencedModels = referencedModels,
    )

    fun buildLegalGuardianAssociation(
        personId: PersonId,
        status: LegalGuardianAssociationStatus,
        guardianId: PersonId = buildPerson().id,
        isSigned: Boolean = false
    ) = LegalGuardianAssociation(
        id = RangeUUID.generate(),
        personId = personId,
        guardianId = guardianId,
        degreeOfKinship = DegreeOfKinship.GUARDIAN,
        status = status.legalGuardianAssociationStatusType,
        statusHistory = listOf(status),
        isSigned = isSigned,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        version = 0,
    )

    fun buildFollowUpHistory(
        personId: PersonId,
        channelId: String,
        wandaTaskId: UUID,
        staffId: UUID,
        followUpId: UUID? = null,
        question: String,
        options: List<ChannelFollowUpOptions> = emptyList(),
        sentAt: LocalDateTime = LocalDateTime.now(),
        viewedAt: LocalDateTime? = null,
        skipAt: LocalDateTime? = null,
        answerAt: LocalDateTime? = null,
        answer: String? = null
    ) =
        FollowUpHistory(
            personId = personId,
            channelId = channelId,
            wandaTaskId = wandaTaskId,
            staffId = staffId,
            followUpId = followUpId,
            question = question,
            options = options,
            sentAt = sentAt,
            viewedAt = viewedAt,
            skipAt = skipAt,
            answerAt = answerAt,
            answer = answer
        )

    fun buildDraftCommand(
        appointmentId: UUID = RangeUUID.generate(),
        action: DraftCommandAction = DraftCommandAction.ADD,
        serializedModel: String = "",
        referencedModel: DraftCommandReferencedModel = DraftCommandReferencedModel.PREGNANCY,
        status: DraftCommandStatus = DraftCommandStatus.PENDING,
    ) = DraftCommand(
        appointmentId = appointmentId,
        action = action,
        serializedModel = serializedModel,
        referencedModel = referencedModel,
        status = status,
    )

    fun buildAliceTestResultBundle(
        id: UUID = RangeUUID.generate(),
        personId: PersonId,
        results: List<AliceTestResult> = listOf(buildAliceTestResult()),
        collectedAt: LocalDateTime = LocalDateTime.now()
    ) = AliceTestResultBundle(
        id = id,
        personId = personId,
        externalId = "40028922",
        integrationSource = ProviderIntegration.DB,
        realizedAtProvider = ProviderName.CASA_ALICE,
        realizedAtUnit = "Casa Alice - Moema",
        results = results,
        collectedAt = collectedAt
    )

    fun buildAliceTestResult(
        id: UUID = RangeUUID.generate(),
        name: String = "covid",
        items: List<AliceResultItem> = listOf(buildAliceItem()),
        collectedAt: LocalDateTime = LocalDateTime.now(),
        releasedAt: LocalDateTime = LocalDateTime.now(),
        showResult: Boolean = true,
        procedureId: String? = null
    ) = AliceTestResult(
        id = id,
        name = name,
        items = items,
        collectedAt = collectedAt,
        releasedAt = releasedAt,
        showResult = showResult,
        procedureId = procedureId
    )

    fun buildAliceItem(
        name: String = "Covid pcr",
        result: String = "positivo",
        reference: ReferenceRange = ReferenceRange.WITHIN_NORMAL_LIMIT
    ) = AliceResultItem(
        name = name,
        result = result,
        referenceRange = reference
    )

    fun buildFhirDocument(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        provider: ProviderIntegration = ProviderIntegration.DASA,
        externalId: String = RangeUUID.generate().toString(),
        fhirResource: String = "DiagnosticReport",
        fhirVersion: FhirVersion = FhirVersion.R4,
        rawData: JsonRaw = JsonRaw("{}")
    ) = FhirDocument(
        id = id,
        personId = personId,
        provider = provider,
        externalId = externalId,
        fhirResource = fhirResource,
        fhirVersion = fhirVersion,
        rawData = rawData
    )

    fun buildTestResultFeedback(
        personId: PersonId = PersonId(),
        content: String = "default feedback",
        addedAt: LocalDateTime = LocalDateTime.now(),
        aliceTestResultBundleIds: List<UUID> = emptyList(),
        healthPlanTaskIds: List<UUID> = emptyList(),
        staffId: UUID = RangeUUID.generate(),
    ) = TestResultFeedback(
        content = content,
        addedAt = addedAt,
        aliceTestResultBundleIds = aliceTestResultBundleIds,
        healthPlanTaskIds = healthPlanTaskIds,
        personId = personId,
        staffId = staffId
    )

    fun buildLegalGuardianInfoTemp(
        personId: PersonId,
        id: UUID = RangeUUID.generate(),
        firstName: String = "Amy",
        lastName: String = "Fowler",
        socialFirstName: String = "Amy",
        socialLastName: String = "Fowler",
        nationalId: String = "438221321",
        degreeOfKinship: DegreeOfKinship = DegreeOfKinship.GUARDIAN,
        email: String = "<EMAIL>",
        address: Address? = null,
        identityDocumentIssuingBody: String = "SSP/SP",
        identityDocument: String = "803795660",
    ) = LegalGuardianInfoTemp(
        id = id,
        personId = personId,
        firstName = firstName,
        lastName = lastName,
        socialFirstName = socialFirstName,
        socialLastName = socialLastName,
        nationalId = nationalId,
        email = email,
        degreeOfKinship = degreeOfKinship,
        identityDocumentIssuingBody = identityDocumentIssuingBody,
        identityDocument = identityDocument,
        address = address ?: buildAddress(),
    )

    fun buildTissBatch(
        id: UUID = RangeUUID.generate(),
        invoiceId: UUID = RangeUUID.generate(),
        status: TissBatchStatus = TissBatchStatus.UPLOADING,
        fileId: UUID? = RangeUUID.generate(),
        fileName: String? = "filename",
        guiaQuantity: Int = 0,
        totalValue: BigDecimal = BigDecimal.ZERO
    ) = TissBatch(
        id = id,
        invoiceId = invoiceId,
        status = status,
        fileId = fileId,
        fileName = fileName,
        guiaQuantity = guiaQuantity,
        totalValue = totalValue
    )

    fun buildTissBatchError(
        tissBatchId: UUID = RangeUUID.generate(),
        lineNumber: Int? = null,
        description: String = "Default description"
    ) = TissBatchError(
        tissBatchId = tissBatchId,
        lineNumber = lineNumber,
        description = description
    )

    fun buildTissBatchHistoric(
        tissBatchId: UUID = RangeUUID.generate(),
        providerId: UUID = RangeUUID.generate(),
        itemCode: String = "123",
        itemName: String = "ABC",
        valueInformed: BigDecimal = BigDecimal(10),
        valueExpected: BigDecimal = BigDecimal(5)
    ) = TissBatchHistoric(
        tissBatchId = tissBatchId,
        providerId = providerId,
        itemCode = itemCode,
        itemName = itemName,
        valueInformed = valueInformed,
        valueExpected = valueExpected
    )

    fun buildOutcomeConf(
        id: UUID = RangeUUID.generate(),
        type: OutcomeConf.OutcomeType = OutcomeConf.OutcomeType.CLINICAL,
        key: String = "MSQ",
        description: String = "mini sleep questionnaire = MSQ",
        status: OutcomeConf.OutcomeStatus = OutcomeConf.OutcomeStatus.ACTIVE,
        referenceRange: List<OutcomeConf.ReferenceRange> = listOf(
            OutcomeConf.ReferenceRange(
                lowerLimit = BigDecimal(10),
                upperLimit = BigDecimal(25),
                description = "LIGHT"
            )
        ),
        healthMeasurementTypeId: UUID? = RangeUUID.generate(),
        action: OutcomeConf.OutcomeAction? = OutcomeConf.OutcomeAction(
            type = OutcomeConf.ActionType.QUESTIONNAIRE,
            additionalInfoId = RangeUUID.generate()
        )
    ) = OutcomeConf(
        id = id,
        type = type,
        key = key,
        description = description,
        status = status,
        referenceRange = referenceRange,
        healthMeasurementTypeId = healthMeasurementTypeId,
        action = action
    )

    fun buildHealthDemandMonitoring(
        healthConditionId: UUID = RangeUUID.generate(),
        outcomeId: UUID = RangeUUID.generate(),
        schedulingConfig: List<HealthDemandMonitoring.SchedulingConfig> = listOf(
            HealthDemandMonitoring.SchedulingConfig(
                severity = CaseSeverity.COMPENSATED,
                daysToFirstSchedule = 10,
                scheduleInLoop = true,
                schedulingInterval = 30
            )
        ),
        outcomeEvolutionRanges: List<HealthDemandMonitoring.OutcomeEvolutionRange> = emptyList(),
        id: UUID = RangeUUID.generate()
    ) = HealthDemandMonitoring(
        id = id,
        healthConditionId = healthConditionId,
        outcomeId = outcomeId,
        schedulingConfig = schedulingConfig,
        outcomeEvolutionRanges = outcomeEvolutionRanges
    )

    fun buildTissInvoice(
        id: UUID = RangeUUID.generate(),
        status: TissInvoiceStatus = TissInvoiceStatus.DRAFT,
        execIndicatorAuthorizerId: UUID? = RangeUUID.generate(),
        providerUnitId: UUID? = RangeUUID.generate(),
        type: InvoiceType? = InvoiceType.HEALTH_INSTITUTION,
        staffId: UUID? = RangeUUID.generate(),
        referenceDate: LocalDate? = null,
        expenseType: InvoiceExpenseType = InvoiceExpenseType.UNDEFINED,
        bonusPercent: Int? = null,
    ): Invoice {
        return Invoice(
            id = id,
            status = status,
            userEmail = "<EMAIL>",
            execIndicatorAuthorizerId = execIndicatorAuthorizerId,
            providerUnitId = providerUnitId,
            type = type,
            code = "XXXX-XXXX",
            staffId = staffId,
            referenceDate = referenceDate,
            expenseType = expenseType,
            bonusPercent = bonusPercent
        )
    }

    fun buildInvoiceCritique(
        id: UUID = RangeUUID.generate(),
        invoiceId: UUID = RangeUUID.generate(),
        description: String = "description",
        status: InvoiceCritiqueStatus = InvoiceCritiqueStatus.PENDING,
        guiaId: UUID = RangeUUID.generate(),
    ): InvoiceCritique {
        return InvoiceCritique(
            id = id,
            invoiceId = invoiceId,
            description = description,
            status = status,
            guiaId = guiaId,
            procedures = emptyList(),
            reason = InvoiceCritiqueReason.INCORRECT_CODE
        )
    }

    fun buildTissGuia(
        id: UUID = RangeUUID.generate(),
        tissBatchId: UUID = RangeUUID.generate(),
        person: PersonId? = null,
        number: String = "**********",
        valueTotal: BigDecimal = BigDecimal(4002),
        memberName: String = "Nome do membro",
        executionEnvironment: HealthSpecialistProcedureExecutionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL
    ): Guia {
        return Guia(
            id = id,
            personId = person,
            memberName = memberName,
            number = number,
            memberNewBorn = "N",
            memberCode = "*********00",
            professionalRequesterName = "Nome do profissional solicitante",
            professionalRequesterCouncilNumber = "5000",
            professionalRequesterCouncil = "CRM",
            professionalRequesterCbos = "1000",
            professionalRequesterCouncilState = "SP",
            professionalExecutorName = "Nome do profissional executor",
            professionalExecutorCouncil = "CRM",
            professionalExecutorCouncilNumber = "2000",
            professionalExecutorCbos = "1000",
            professionalExecutorCouncilState = "SP",
            providerRequesterCode = "**********",
            providerRequesterName = "Nome do provider requisidor",
            requestedAt = LocalDate.now(),
            attendanceType = "04",
            providerExecutorCode = "*********",
            providerExecutorName = "Nome do provider executor",
            providerExecutorCnes = "456789",
            attendanceCharacter = "2",
            accidentIndication = "Não acidente",
            tissBatchNumber = "165407",
            valueProcedures = BigDecimal(1000.50),
            valueTaxRents = BigDecimal(1000.50),
            valueMaterials = BigDecimal(1000.50),
            valueMedicines = BigDecimal(1000.50),
            valueOpme = BigDecimal.ZERO,
            valueDaily = BigDecimal.ZERO,
            valueMedicinalGases = BigDecimal.ZERO,
            valueTotal = valueTotal,
            clinicIndication = "Cerume Impactado",
            appointmentType = "1",
            providerRequesterCnpj = "60726502000126",
            providerExecutorCnpj = "60726502000126",
            tissBatchId = tissBatchId,
            executionEnvironment = executionEnvironment
        )
    }

    fun buildTissGuiaProcedure(
        id: UUID = RangeUUID.generate(),
        guiaId: UUID = RangeUUID.generate(),
        critique: String? = null
    ): GuiaProcedure {
        return GuiaProcedure(
            id = id,
            executedAt = LocalDate.now(),
            procedureCode = "98000",
            procedureDescription = "Nome do procedimento",
            procedureTable = "98",
            quantity = 1,
            reduceValue = BigDecimal(1000.50),
            unityValue = BigDecimal(1000.50),
            totalValue = BigDecimal(1000.50),
            guiaId = guiaId,
            critique = critique
        )
    }

    fun buildTissGuiaExpense(
        id: UUID = RangeUUID.generate(),
        guiaId: UUID = RangeUUID.generate(),
        critique: String? = null,
        reviewerId: String? = null,
        totalValue: BigDecimal = BigDecimal(1000.50),
        executedAt: LocalDate = LocalDate.now()
    ): TissGuiaExpense {
        return TissGuiaExpense(
            id = id,
            executedAt = executedAt,
            procedureCode = "98000",
            procedureDescription = "Nome do procedimento",
            procedureTable = "98",
            quantity = 1,
            reduceValue = BigDecimal(1000.50),
            unityValue = BigDecimal(1000.50),
            totalValue = totalValue,
            guiaId = guiaId,
            critique = critique,
            reviewerId = reviewerId,
            expenseCode = "01"
        )
    }

    fun buildAppointmentReminder(
        appointmentScheduleId: UUID = RangeUUID.generate(),
        notificationTime: AppointmentReminderNotificationTime = AppointmentReminderNotificationTime.ONE_DAY_BEFORE,
        provider: AppointmentReminderProvider = AppointmentReminderProvider.TREBLE,
    ): AppointmentReminder {
        return AppointmentReminder(
            appointmentScheduleId = appointmentScheduleId,
            provider = provider,
            notificationTime = notificationTime,
        )
    }

    fun buildMemberContractTerm(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        signature: UserSignature? = buildUserSignature(),
        memberContractId: UUID? = null,
        documentFileId: UUID? = RangeUUID.generate(),
        signedDocumentFileId: UUID? = RangeUUID.generate(),
        termType: TermType = TermType.DATA_PROCESSING_TERMS,
    ): MemberContractTerm {
        return MemberContractTerm(
            id = id,
            memberId = memberId,
            memberContractId = memberContractId,
            signature = signature,
            termType = termType,
            signedDocumentFileId = signedDocumentFileId,
            documentFileId = documentFileId
        )
    }

    fun buildUserSignature(id: UUID = RangeUUID.generate(), signedAt: LocalDateTime = LocalDateTime.now()) =
        UserSignature(
            id = id,
            userAgent = "userAgent",
            ipAddress = "***********",
            signedAt = signedAt,
            idToken = "idToken"
        )

    fun buildGuiaProcedure(
        id: UUID = RangeUUID.generate(),
        executedAt: LocalDate = LocalDate.now(),
        procedureCode: String = "procedureCode",
        procedureDescription: String = "procedureDescription",
        procedureTable: String = "procedureTable",
        quantity: Int = 0,
        guiaId: UUID = RangeUUID.generate(),
        totalValue: BigDecimal = 100.0.toBigDecimal(),
        hash: String? = null,
        unityValue: BigDecimal = 100.0.toBigDecimal()
    ): GuiaProcedure {
        return GuiaProcedure(
            id = id,
            executedAt = executedAt,
            procedureCode = procedureCode,
            procedureDescription = procedureDescription,
            procedureTable = procedureTable,
            quantity = quantity,
            guiaId = guiaId,
            totalValue = totalValue,
            hash = hash,
            unityValue = unityValue
        )
    }

    fun buildAppointmentTemplate(
        title: String = RangeUUID.generate().toString(),
        description: String = RangeUUID.generate().toString(),
        type: AppointmentType = DEFAULT
    ) = AppointmentTemplate(
        id = RangeUUID.generate(),
        title = title,
        description = description,
        type = type,
        content = emptyMap(),
        attachments = emptyList(),
        active = true,
        updatedBy = null,
        version = 0,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )


    fun buildOutcomeRequestScheduling(
        personId: PersonId = PersonId(),
        id: UUID = RangeUUID.generate(),
        status: SchedulingStatus = SchedulingStatus.PENDING,
        scheduledFor: LocalDateTime = LocalDateTime.now().plusDays(1),
        processedAt: LocalDateTime? = null,
        referencedLink: OutcomeRequestScheduling.ReferencedLink? = null,
        caseRecordIds: Set<UUID> = setOf(),
        healthDemandMonitoringId: UUID = RangeUUID.generate(),
    ) = OutcomeRequestScheduling(
        personId = personId,
        id = id,
        status = status,
        scheduledFor = scheduledFor,
        processedAt = processedAt,
        referencedLink = referencedLink,
        caseRecordIds = caseRecordIds,
        healthDemandMonitoringId = healthDemandMonitoringId
    )

    fun buildHDataOverview(
        personId: PersonId = PersonId(),
        healthDemands: Map<UUID, HDataOverview.HDemand> = emptyMap(),
        outcomeRecords: Map<UUID, HDataOverview.ClinicalOutcome> = emptyMap(),
        monthYearBirth: LocalDate? = null,
        sex: Sex? = null,
        risk: RiskDescription? = null
    ) = HDataOverview(
        personId = personId,
        healthDemands = healthDemands,
        outcomeRecords = outcomeRecords,
        monthYearBirth = monthYearBirth,
        sex = sex,
        risk = risk,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    fun buildHealthEvents(
        procedureIds: List<String>? = listOf("123"),
        originReferences: List<EventReference>? = listOf(
            EventReference(
                RangeUUID.generate().toString(), HealthEventLocationEnum.EXECUTION_GROUP
            )
        ),
        personId: PersonId = PersonId(),
        eventType: HealthEventTypeEnum = HealthEventTypeEnum.EXAM,
        caseIds: List<String>? = emptyList(),
        caseRecordIds: List<String>? = emptyList(),
        requestedAt: LocalDateTime = LocalDateTime.now(),
        executedAt: LocalDateTime? = null,
        origin: HealthEventOriginEnum = HealthEventOriginEnum.EITA,
        healthProfessionalId: UUID = RangeUUID.generate(),
        id: UUID = RangeUUID.generate()
    ) = HealthEvents(
        personId = personId,
        eventType = eventType,
        caseIds = caseIds,
        caseRecordIds = caseRecordIds,
        requestedAt = requestedAt,
        executedAt = executedAt,
        originReferences = originReferences,
        origin = origin,
        healthProfessionalId = healthProfessionalId,
        procedureIds = procedureIds,
        id = id
    )

    fun buildPersonTeamAssociation(
        personId: PersonId = PersonId(),
        healthcareTeamId: UUID = RangeUUID.generate(),
        healthcareAdditionalTeamId: UUID = RangeUUID.generate(),
        risk: RiskDescription = RiskDescription.LOW_RISK,
        status: PersonTeamAssociationStatus = PersonTeamAssociationStatus.ACTIVE,
    ) = PersonTeamAssociation(
        personId = personId,
        healthcareTeamId = healthcareTeamId,
        healthcareAdditionalTeamId = healthcareAdditionalTeamId,
        risk = risk,
        status = status
    )

    fun buildPersonCase(
        id: UUID = RangeUUID.generate(),
        recordId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        follow: Follow? = null,
        responsibleStaffId: UUID? = null,
        addedByStaffId: UUID? = null,
        codeType: Disease.Type = GOAL,
        codeValue: String = "CORRER",
        codeDescription: String? = null,
        observation: String? = null,
        severity: CaseSeverity = CaseSeverity.ONGOING,
        status: CaseStatus = CaseStatus.ACTIVE,
        addedAt: LocalDateTime = LocalDateTime.now(),
        startedAt: LocalDateTime = LocalDateTime.now(),
        healthConditionId: UUID = RangeUUID.generate(),
        channelId: String? = null,
        seriousness: CaseSeriousness? = null
    ) = PersonCase(
        id = id,
        recordId = recordId,
        personId = personId,
        follow = follow,
        responsibleStaffId = responsibleStaffId,
        addedByStaffId = addedByStaffId,
        codeType = codeType,
        codeValue = codeValue,
        codeDescription = codeDescription,
        observation = observation,
        severity = severity,
        status = status,
        addedAt = addedAt,
        startedAt = startedAt,
        healthConditionId = healthConditionId,
        channelId = channelId,
        seriousness = seriousness
    )

    fun buildProviderHealthDocument(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        providerUnitId: UUID = RangeUUID.generate(),
        documentType: ProviderHealthDocumentType = ProviderHealthDocumentType.DISCHARGE_SUMMARY_EMERGENCY,
        actionStatus: ProviderHealthDocumentStatus = ProviderHealthDocumentStatus.RELEASED
    ) = ProviderHealthDocument(
        id = id,
        personId = personId,
        providerUnitId = providerUnitId,
        fileVaultId = RangeUUID.generate(),
        documentType = documentType,
        actionStatus = actionStatus
    )

    fun buildDevice(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        deviceId: String,
        appVersion: String? = null,
        voipToken: String? = null
    ) = Device(
        id = id,
        personId = personId,
        deviceId = deviceId,
        appVersion = appVersion,
        voipToken = voipToken
    )

    fun buildProfessionalTierProcedureValue(
        id: UUID = RangeUUID.generate(),
        tussCode: String = "123456",
        tier: SpecialistTier = SpecialistTier.TALENTED,
        speciality: String = "ABC",
        value: BigDecimal = BigDecimal(160.00),
        staffId: UUID = RangeUUID.generate(),
        medicalSpecialtyId: UUID = RangeUUID.generate(),
        beginAt: LocalDate = LocalDate.now()
    ) = ProfessionalTierProcedureValue(
        id = id,
        tussCode = tussCode,
        tier = tier,
        speciality = speciality,
        value = value,
        staffId = staffId,
        medicalSpecialtyId = medicalSpecialtyId,
        beginAt = beginAt
    )

    fun buildEventTypeProviderUnit(
        id: UUID = RangeUUID.generate(),
        providerUnitId: UUID,
        appointmentScheduleEventTypeId: UUID,
        duration: Int = 30,
        minimumTimeToScheduleBeforeAppointmentTime: Int = 1,
        numberOfDaysFromNowToAllowScheduling: Int = 90,
        availableWeekDays: List<Weekday> = Weekday.values().toList(),
        status: Status = Status.ACTIVE,
        availabilityStartTime: LocalTime? = LocalTime.parse("08:00"),
        availabilityEndTime: LocalTime? = LocalTime.parse("18:00"),
    ) = EventTypeProviderUnit(
        id = id,
        providerUnitId = providerUnitId,
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
        duration = duration,
        minimumTimeToScheduleBeforeAppointmentTime = minimumTimeToScheduleBeforeAppointmentTime,
        numberOfDaysFromNowToAllowScheduling = numberOfDaysFromNowToAllowScheduling,
        availableWeekDays = availableWeekDays,
        status = status,
        availabilityStartTime = availabilityStartTime,
        availabilityEndTime = availabilityEndTime
    )

    fun buildDischargeSummary(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        externalId: String = "40028922",
        provider: ProviderIntegration = ProviderIntegration.BP,
        dischargeItem: DischargeSummaryItem = buildDischargeItem()
    ) = DischargeSummary(
        id = id,
        personId = personId,
        externalId = externalId,
        provider = provider,
        dischargeItem = dischargeItem
    )

    fun buildActionPlanTask(
        personId: PersonId = PersonId(),
        healthPlanId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        content: Map<String, Any> = mapOf("professional" to ProfessionalType.HEALTH_CARE_TEAM_PHYSICIAN),
        date: LocalDateTime = LocalDateTime.now(),
        dueDate: LocalDate? = LocalDate.now(),
        type: ActionPlanTaskType = ActionPlanTaskType.REFERRAL,
        frequency: Frequency? = null,
        deadline: Deadline? = null,
        start: StartType? = null,
        groupId: UUID? = null,
        id: UUID = RangeUUID.generate(),
        status: ActionPlanTaskStatus = ActionPlanTaskStatus.ACTIVE,
        title: String = "Ir ao geriatra",
        description: String = "Descrição",
        initiatedByMemberAt: LocalDateTime? = null,
        scheduledAt: LocalDateTime? = null,
        finishedBy: ActionPlanTaskUpdatedBy? = null,
        caseRecordDetails: List<CaseRecordDetails> = emptyList(),
        favorite: Boolean = false,
        acknowledgedAt: LocalDateTime? = null,
        releasedAt: LocalDateTime? = null,
        attachments: List<Attachment> = emptyList(),
        appointmentId: UUID? = null,
        appointmentScheduleId: UUID? = null,
    ) =
        ActionPlanTask(
            id = id,
            personId = personId,
            healthPlanId = healthPlanId,
            title = title,
            description = description,
            content = content,
            dueDate = dueDate,
            status = status,
            lastRequesterStaffId = staffId,
            requestersStaffIds = setOf(staffId),
            type = type,
            createdAt = date,
            updatedAt = date,
            frequency = frequency,
            deadline = deadline,
            start = start?.let { Start(type = start, date = date) },
            groupId = groupId,
            releasedByStaffId = staffId,
            initiatedByMemberAt = initiatedByMemberAt,
            favorite = favorite,
            scheduledAt = scheduledAt,
            finishedBy = finishedBy,
            originalUpdatedAt = date,
            caseRecordDetails = caseRecordDetails,
            acknowledgedAt = acknowledgedAt,
            releasedAt = releasedAt,
            attachments = attachments,
            appointmentId = appointmentId,
            appointmentScheduleId = appointmentScheduleId,
        )

    fun buildActionPlanTaskReferral(
        personId: PersonId = PersonId(),
        healthPlanId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        dueDate: LocalDate? = LocalDate.now(),
        frequency: Frequency? = null,
        deadline: Deadline? = null,
        start: Start? = null,
        groupId: UUID? = null,
        id: UUID = RangeUUID.generate(),
        status: ActionPlanTaskStatus = ActionPlanTaskStatus.ACTIVE,
        title: String = "Ir ao geriatra",
        description: String = "Descrição",
        initiatedByMemberAt: LocalDateTime? = null,
        scheduledAt: LocalDateTime? = null,
        finishedBy: ActionPlanTaskUpdatedBy? = null,
        caseRecordDetails: List<CaseRecordDetails> = emptyList(),
        favorite: Boolean = false,
        acknowledgedAt: LocalDateTime? = null,
        diagnosticHypothesis: String = "está com problema endócrino",
        specialty: ReferralSpecialty? = null,
        subSpecialty: ReferralSpecialty? = null,
        suggestedSpecialist: SuggestedSpecialist? = null,
        sessionsQuantity: Int? = null,
        followUpMaxQuantity: Int? = null,
        date: LocalDateTime = LocalDateTime.now(),
    ): ActionPlanTask {
        val content = mutableMapOf<String, Any>(
            "diagnosticHypothesis" to diagnosticHypothesis
        )

        specialty?.let { content["specialty"] = it.asMap() }
        subSpecialty?.let { content["subSpecialty"] = it.asMap() }
        sessionsQuantity?.let { content["sessionsQuantity"] = it }
        followUpMaxQuantity?.let { content["followUpMaxQuantity"] = it }
        suggestedSpecialist?.let { content["suggestedSpecialist"] = it }

        return ActionPlanTask(
            id = id,
            personId = personId,
            healthPlanId = healthPlanId,
            title = title,
            description = description,
            content = content,
            dueDate = dueDate,
            status = status,
            lastRequesterStaffId = staffId,
            requestersStaffIds = setOf(staffId),
            type = ActionPlanTaskType.REFERRAL,
            createdAt = date,
            updatedAt = date,
            frequency = frequency,
            deadline = deadline,
            start = start,
            groupId = groupId,
            releasedByStaffId = staffId,
            initiatedByMemberAt = initiatedByMemberAt,
            favorite = favorite,
            scheduledAt = scheduledAt,
            finishedBy = finishedBy,
            originalUpdatedAt = date,
            caseRecordDetails = caseRecordDetails,
            acknowledgedAt = acknowledgedAt
        )
    }

    fun buildActionPlanTaskEmergency(
        personId: PersonId = PersonId(),
        healthPlanId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        dueDate: LocalDate? = LocalDate.now(),
        frequency: Frequency? = null,
        deadline: Deadline? = null,
        start: Start? = null,
        groupId: UUID? = null,
        id: UUID = RangeUUID.generate(),
        status: ActionPlanTaskStatus = ActionPlanTaskStatus.ACTIVE,
        title: String = "Ir ao geriatra",
        description: String = "Descrição",
        initiatedByMemberAt: LocalDateTime? = null,
        scheduledAt: LocalDateTime? = null,
        finishedBy: ActionPlanTaskUpdatedBy? = null,
        caseRecordDetails: List<CaseRecordDetails> = emptyList(),
        favorite: Boolean = false,
        acknowledgedAt: LocalDateTime? = null,
        diagnosticHypothesis: String = "está com problema endócrino",
        referenceLetterSentDate: LocalDateTime? = LocalDateTime.now(),
        cityId: String? = "112233",
        specialty: ReferralSpecialty? = null,
        date: LocalDateTime = LocalDateTime.now(),
    ): ActionPlanTask {

        val content: MutableMap<String, Any> = mutableMapOf(
            "diagnosticHypothesis" to diagnosticHypothesis,
            "referenceLetterSentDate" to referenceLetterSentDate.toString(),
            "cityId" to (cityId ?: "112233"),
        )

        specialty?.let { content["specialty"] = it.asMap() }

        return ActionPlanTask(
            id = id,
            personId = personId,
            healthPlanId = healthPlanId,
            title = title,
            description = description,
            content = content,
            dueDate = dueDate,
            status = status,
            lastRequesterStaffId = staffId,
            requestersStaffIds = setOf(staffId),
            type = ActionPlanTaskType.EMERGENCY,
            createdAt = date,
            updatedAt = date,
            frequency = frequency,
            deadline = deadline,
            start = start,
            groupId = groupId,
            releasedByStaffId = staffId,
            initiatedByMemberAt = initiatedByMemberAt,
            favorite = favorite,
            scheduledAt = scheduledAt,
            finishedBy = finishedBy,
            originalUpdatedAt = date,
            caseRecordDetails = caseRecordDetails,
            acknowledgedAt = acknowledgedAt
        )
    }

    fun buildDemandActionPlan(
        id: UUID = RangeUUID.generate(),
        personId: PersonId,
        description: String,
        healthConditionId: UUID? = RangeUUID.generate(),
        referencedModelId: UUID? = null,
        referencedModelClass: DemandActionPlanReferenceModel? = DemandActionPlanReferenceModel.HEALTH_DEMAND,
        relatedTaskIds: List<UUID>,
        lastTaskCreatedAt: LocalDateTime = LocalDateTime.now(),
        nextAppointmentScheduledAt: LocalDateTime? = null,
        status: DemandActionPlanStatus = DemandActionPlanStatus.ACTIVE,
        hasFavoriteTasks: Boolean = false,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = DemandActionPlan(
        id = id,
        personId = personId,
        description = description,
        healthConditionId = healthConditionId,
        referencedModelId = referencedModelId,
        referencedModelClass = referencedModelClass,
        relatedTaskIds = relatedTaskIds,
        lastTaskCreatedAt = lastTaskCreatedAt,
        nextAppointmentScheduledAt = nextAppointmentScheduledAt,
        status = status,
        hasFavoriteTasks = hasFavoriteTasks,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildDischargeItem(
        admittedAt: LocalDateTime = LocalDateTime.now(),
        dischargedAt: LocalDateTime = LocalDateTime.now(),
    ) = DischargeSummaryItem(
        kind = DischargeSummaryItemKind.HOSPITALIZATION,
        location = "Hospital",
        admittedAt = admittedAt,
        dischargedAt = dischargedAt,
        practitionerSpecialty = listOf(
            DischargeSummaryPractitioner(
                name = "doctor name", councilType = "CRM", councilValue = "000000-00", qualification = "Clinical"
            )
        ),
        diagnostic = listOf("doente", "melhorando", "vai ficar bem", "indo para casa"),
        risk = listOf(
            DischargeSummaryRisk(
                codeType = "DMA", codeValue = "Diabete"
            )
        ),
        clinicalImpression = listOf(
            DischargeSummaryImpression(
                description = "vai ficar bem tomando dipirona", summary = "alta"
            )
        ),
        carePlan = listOf(
            DischargeSummaryImpression(
                description = "tomar muita agua", summary = "ficar de repouso"
            )
        ),
        vitalSigns = listOf(
            DischargeSummaryVitalSigns(
                code = "1", display = "pressao", value = "100"
            )
        ),
        condition = listOf("alergia a leite"),
        medicines = listOf("dipirona", "medicamento 2")
    )

    fun buildNationalReceipt(
        id: UUID = RangeUUID.generate(),
        clientCnpj: String = "22929008000170",
        issuedAt: LocalDateTime = LocalDateTime.now(),
        issuerCnpj: String = "63208091000175",
        linkDownloadPdf: String? = "https://api2.enotasgw.com.br/v3/files/pdf/72F1DD38CA6062C0CA1A1526B8A18FB6/40.pdf?l=1",
        number: String = "123",
        status: NationalReceiptStatus = NationalReceiptStatus.FINALIZED,
        totalValue: BigDecimal = 4.2.toBigDecimal(),
        invoiceId: UUID? = null,
        paymentDate: LocalDate = LocalDate.now().plusDays(30),
        paymentAllowedAt: LocalDate? = null,
        externalId: String = "123-63208091000175",
        publicDocumentFileVaultId: UUID? = null,
        expenseType: ExpenseTypeEnum = ExpenseTypeEnum.MEDICAL_EVENTS
    ) = NationalReceipt(
        id = id,
        clientCnpj = clientCnpj,
        issuedAt = issuedAt,
        issuerCnpj = issuerCnpj,
        linkDownloadPdf = linkDownloadPdf,
        number = number,
        status = status,
        totalValue = totalValue,
        invoiceId = invoiceId,
        paymentDate = paymentDate,
        paymentAllowedAt = paymentAllowedAt,
        externalId = externalId,
        publicDocumentFileVaultId = publicDocumentFileVaultId,
        expenseType = expenseType
    )

    fun buildAppointmentScheduleEventTypeDateException(
        id: UUID = RangeUUID.generate(),
        exceptionDate: LocalDate = LocalDate.now(),
        appointmentScheduleEventTypeId: UUID,
    ) = AppointmentScheduleEventTypeDateException(
        id = id,
        exceptionDate = exceptionDate,
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
    )

    fun buildProtocol(
        healthConditionIds: List<UUID> = emptyList(),
        rootNodeId: UUID = RangeUUID.generate(),
        title: String = "Linha de cuidado - Diabetes",
        metadata: Protocol.ProtocolMetadata = Protocol.ProtocolMetadata(
            listOf("OBJETIVO_SONO_003"),
            listOf("Sono - Acordar mais disposto")
        ),
        attributes: Protocol.ProtocolAttributes = Protocol.ProtocolAttributes(
            healthConditions = listOf(
                Protocol.HealthConditionLight(
                    code = "OBJETIVO_SONO_003",
                    name = "Sono - Acordar mais disposto",
                    codeType = HealthConditionCodeType.CID_10
                )
            )
        ),
        status: Protocol.ProtocolStatus = Protocol.ProtocolStatus.ACTIVE,
        relatedTerms: List<String> = emptyList(),
        typeOfService: List<Protocol.ProtocolService> = emptyList(),
        category: Protocol.ProtocolCategory = Protocol.ProtocolCategory.ADMINISTRATIVE,
        attentionLevels: List<Protocol.ProtocolAttentionLevel> = listOf(Protocol.ProtocolAttentionLevel.PRIMARY),
        targets: List<Protocol.ProtocolTargets> = listOf(Protocol.ProtocolTargets.ADULT),
        typeOfMaterial: Protocol.ProtocolTypeOfMaterial = Protocol.ProtocolTypeOfMaterial.PROTOCOL,
        staffRoles: List<Role> = healthProfessionalStaffs(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = Protocol(
        healthConditionIds = healthConditionIds,
        rootNodeId = rootNodeId,
        title = title,
        metadata = metadata,
        attributes = attributes,
        status = status,
        relatedTerms = relatedTerms,
        typeOfService = typeOfService,
        category = category,
        attentionLevels = attentionLevels,
        targets = targets,
        typeOfMaterial = typeOfMaterial,
        staffRoles = staffRoles,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildProtocolTracking(
        type: HealthLogicTracking.HealthLogicTrackingModel =
            HealthLogicTracking.HealthLogicTrackingModel.HEALTH_PLAN_TASK,
        serviceScriptActionId: UUID = RangeUUID.generate(),
        serviceScriptNavigationId: UUID = RangeUUID.generate(),
        externalId: UUID = RangeUUID.generate(),
    ) = ProtocolTracking(
        type = type,
        externalId = externalId,
        serviceScriptActionId = serviceScriptActionId,
        serviceScriptNavigationId = serviceScriptNavigationId
    )

    fun buildUpdatedPersonContactInfoTemp(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        type: UpdatedPersonContactInfoTemp.Type = UpdatedPersonContactInfoTemp.Type.PHONE,
        newValue: String = "newValue",
        used: Boolean = false,
        token: String = "token",
        validUntil: LocalDateTime = LocalDateTime.now(),
    ) = UpdatedPersonContactInfoTemp(
        id = id,
        personId = personId,
        type = type,
        newValue = newValue,
        used = used,
        token = token,
        validUntil = validUntil,

        )

    fun buildAppointmentCoordination(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        appointmentId: UUID = RangeUUID.generate(),
        appointmentType: AppointmentCoordination.Type = AppointmentCoordination.Type.APPOINTMENT,
        healthPlanTaskId: UUID = RangeUUID.generate(),
        appointmentScheduleId: UUID = RangeUUID.generate(),
        coordinated: AppointmentCoordination.CoordinatedType = AppointmentCoordination.CoordinatedType.COORDINATED
    ) = AppointmentCoordination(
        id = id,
        personId = personId,
        appointmentId = appointmentId,
        appointmentType = appointmentType,
        healthPlanTaskId = healthPlanTaskId,
        appointmentScheduleId = appointmentScheduleId,
        coordinated = coordinated,
    )

    fun buildIntentionCoordination(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        eventExternalId: UUID = RangeUUID.generate(),
        eventType: IntentionCoordinationType = IntentionCoordinationType.ELIGIBILITY_CHECK,
        coordinated: AppointmentCoordination.CoordinatedType = AppointmentCoordination.CoordinatedType.COORDINATED,
        specialtyIds: List<UUID>? = null,
    ) = IntentionCoordination(
        id = id,
        personId = personId,
        eventExternalId = eventExternalId,
        eventType = eventType,
        coordinated = coordinated,
        specialtyIds = specialtyIds
    )

    fun buildHospitalSummaryHistory(
        personId: PersonId = PersonId(),
        providerIntegration: ProviderIntegration = ProviderIntegration.BP,
        unit: String? = "BP - Paulista",
        eventType: SummaryHistoryEventType = SummaryHistoryEventType.SAVE_FHIR_DOCUMENT,
        eventValue: String? = "test value",
        externalId: String? = "40028922",
        eventId: UUID = RangeUUID.generate()
    ) = HospitalSummaryHistory(
        personId = personId,
        providerIntegration = providerIntegration,
        unit = unit,
        eventType = eventType,
        eventValue = eventValue,
        externalId = externalId,
        eventId = eventId
    )

    fun buildWandaComment(
        addedAt: LocalDateTime = LocalDateTime.now(),
        staffId: UUID = RangeUUID.generate(),
        personHealthEventId: UUID = RangeUUID.generate(),
        comment: String = "Discuss",
        personId: PersonId = PersonId()
    ) =
        WandaComment(
            addedAt = addedAt,
            staffId = staffId,
            personHealthEventId = personHealthEventId,
            comment = comment,
            personId = personId
        )

    fun buildRoutingHistory(
        rule: String = "RuleTest",
        ruleVersion: Int = 0,
        input: RoutingHistoryInput = RoutingHistoryInput("channel_id", RoutingHistoryModel.CHANNEL),
        output: List<Map<String, Any?>> = listOf(mapOf("code" to "testCode"))
    ) = RoutingHistory(
        rule = rule,
        ruleVersion = ruleVersion,
        input = input,
        output = output
    )

    fun buildRoutingRule(
        id: UUID = RangeUUID.generate(),
        name: String = "rule_name",
        type: RoutingHistoryModel = RoutingHistoryModel.HEALTHCARE_TEAM,
        content: RoutingRuleContent = RoutingRuleContent(
            result = listOf(RangeUUID.generate().toString(), RangeUUID.generate().toString()),
            priority = 0,
            validations = listOf(
                RoutingRuleValidation(
                    type = RoutingRuleValidationType.AGE,
                    value = 12,
                    operator = RoutingRuleValidationOperator.GREATER_EQUALS
                ),
                RoutingRuleValidation(
                    type = RoutingRuleValidationType.CITY,
                    value = "São Paulo",
                    operator = RoutingRuleValidationOperator.EQUALS
                )
            )
        )
    ) = RoutingRule(
        id = id,
        name = name,
        type = type,
        content = content
    )

    fun buildPersonSalesInfo(
        leadId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        hubspotContactId: String? = null,
    ) = PersonSalesInfo(
        leadId = leadId,
        personId = personId,
        hubspotContactId = hubspotContactId,
    )

    fun buildDealSalesInfo(
        simulationId: UUID = RangeUUID.generate(),
        lastOpportunityId: UUID = RangeUUID.generate(),
        hubspotDealId: String? = null,
        personId: PersonId = PersonId()
    ) = DealSalesInfo(
        hubspotDealId = hubspotDealId,
        simulationId = simulationId,
        lastOpportunityId = lastOpportunityId,
        personId = personId,
    )

    fun buildTertiaryIntentionHospitalization(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        staffId: UUID = RangeUUID.generate(),
        providerUnitId: UUID = RangeUUID.generate(),
        objectiveCodes: List<Disease> = listOf(Disease(CID_10, "A99")),
        startedAt: LocalDateTime? = LocalDateTime.now(),
        endedAt: LocalDateTime? = LocalDateTime.now().plusDays(1),
        hospitalizationOrigin: TertiaryIntentionHospitalizationOrigin = TertiaryIntentionHospitalizationOrigin.URGENCY,
        hospitalizationResponsible: TertiaryIntentionHospitalizationResponsible = TertiaryIntentionHospitalizationResponsible.ALICE_REAR,
        internalPhysician: String = "Joao",
        hospitalizationSpecialty: String = "Cardiologia",
        objectiveCodesReason: String = "Membro teve mal estar",
        procedureDescription: String = "Descricao",
        hospitalizationPostProgramming: String = "Repouso absoluto por 4 dias",
        hospitalizationProcedures: List<TertiaryIntentionHospitalizationProcedure> = listOf(
            TertiaryIntentionHospitalizationProcedure(
                procedureDate = LocalDate.now(),
                procedureCode = "123"
            )
        ),
        evolutions: List<TertiaryIntentionEvolution> = listOf(
            TertiaryIntentionEvolution(
                createdAt = LocalDateTime.now(),
                staffId = RangeUUID.generate(),
                description = "Membro foi para UTI"
            ),
            TertiaryIntentionEvolution(
                createdAt = LocalDateTime.now().plusDays(1),
                staffId = RangeUUID.generate(),
                description = "Membro foi para o quarto"
            ),
        ),
        intensiveCare: List<TertiaryIntentionIntensiveCare> = emptyList(),
        dischargeForecastInDays: Int = 15,
        coordinated: TertiaryIntentionCoordinated = TertiaryIntentionCoordinated.TIR_COORDINATE,
        notifications: List<TertiaryIntentionNotification> = emptyList(),
        newBornWeight: BigDecimal? = null
    ) = TertiaryIntentionTouchPoint(
        id = id,
        type = TertiaryIntentionType.TIT_HOSPITALIZATION,
        personId = personId,
        staffId = staffId,
        providerUnitId = providerUnitId,
        objectiveCodes = objectiveCodes,
        startedAt = startedAt,
        endedAt = endedAt,
        hospitalizationOrigin = hospitalizationOrigin,
        hospitalizationResponsible = hospitalizationResponsible,
        internalPhysician = internalPhysician,
        hospitalizationSpecialty = hospitalizationSpecialty,
        objectiveCodesReason = objectiveCodesReason,
        procedureDescription = procedureDescription,
        hospitalizationPostProgramming = hospitalizationPostProgramming,
        hospitalizationProcedures = hospitalizationProcedures,
        evolutions = evolutions,
        intensiveCare = intensiveCare,
        dischargeForecastInDays = dischargeForecastInDays,
        coordinated = coordinated,
        notifications = notifications,
        newBornWeight = newBornWeight
    )

    fun buildTertiaryIntentionSurgery(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        staffId: UUID = RangeUUID.generate(),
        providerUnitId: UUID = RangeUUID.generate(),
        objectiveCodes: List<Disease> = listOf(Disease(CID_10, "A99")),
        startedAt: LocalDateTime? = LocalDateTime.now(),
        endedAt: LocalDateTime? = LocalDateTime.now().plusDays(1),
        hospitalizationPostProgramming: String = "Repouso absoluto por 4 dias",
        surgeryStatus: TertiaryIntentionSurgeryStatus = TertiaryIntentionSurgeryStatus.PERFORMED,
        surgeryIndicationDate: LocalDateTime = LocalDateTime.now().plusDays(1),
        surgeryAdmissionDate: LocalDateTime = LocalDateTime.now().plusDays(1),
        surgeonSpecialistId: UUID = RangeUUID.generate(),
        hospitalizationProcedures: List<TertiaryIntentionHospitalizationProcedure> = listOf(
            TertiaryIntentionHospitalizationProcedure(
                procedureDate = LocalDate.now(),
                procedureCode = "123"
            )
        ),
        evolutions: List<TertiaryIntentionEvolution> = listOf(
            TertiaryIntentionEvolution(
                createdAt = LocalDateTime.now(),
                staffId = staffId,
                description = "Membro foi para UTI"
            ),
            TertiaryIntentionEvolution(
                createdAt = LocalDateTime.now().plusDays(1),
                staffId = staffId,
                description = "Membro foi para o quarto"
            ),
        ),
        intensiveCare: List<TertiaryIntentionIntensiveCare> = emptyList(),
        dischargeForecastInDays: Int = 15
    ) = TertiaryIntentionTouchPoint(
        id = id,
        type = TertiaryIntentionType.TIT_SURGERY,
        personId = personId,
        staffId = staffId,
        providerUnitId = providerUnitId,
        objectiveCodes = objectiveCodes,
        startedAt = startedAt,
        endedAt = endedAt,
        surgeryStatus = surgeryStatus,
        surgeryIndicationDate = surgeryIndicationDate,
        surgeryAdmissionDate = surgeryAdmissionDate,
        surgeonSpecialistId = surgeonSpecialistId,
        hospitalizationProcedures = hospitalizationProcedures,
        hospitalizationPostProgramming = hospitalizationPostProgramming,
        intensiveCare = intensiveCare,
        evolutions = evolutions,
        dischargeForecastInDays = dischargeForecastInDays
    )

    fun buildTertiaryIntentionEmergency(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        staffId: UUID = RangeUUID.generate(),
        providerUnitId: UUID = RangeUUID.generate(),
        objectiveCodes: List<Disease> = listOf(Disease(CID_10, "A99")),
        startedAt: LocalDateTime? = LocalDateTime.now(),
        endedAt: LocalDateTime? = LocalDateTime.now().plusDays(1),
        surgeryStatus: TertiaryIntentionSurgeryStatus = TertiaryIntentionSurgeryStatus.PERFORMED,
        surgeryIndicationDate: LocalDateTime = LocalDateTime.now().plusDays(1),
        surgeryAdmissionDate: LocalDateTime = LocalDateTime.now().plusDays(1),
        surgeonSpecialistId: UUID = RangeUUID.generate(),
        dischargeForecastInDays: Int = 15,
        coordinated: TertiaryIntentionCoordinated = TertiaryIntentionCoordinated.TIR_COORDINATE
    ) = TertiaryIntentionTouchPoint(
        id = id,
        type = TertiaryIntentionType.TIT_EMERGENCY,
        coordinated = coordinated,
        personId = personId,
        staffId = staffId,
        providerUnitId = providerUnitId,
        objectiveCodes = objectiveCodes,
        startedAt = startedAt,
        endedAt = endedAt,
        surgeryStatus = surgeryStatus,
        surgeryIndicationDate = surgeryIndicationDate,
        surgeryAdmissionDate = surgeryAdmissionDate,
        surgeonSpecialistId = surgeonSpecialistId,
        dischargeForecastInDays = dischargeForecastInDays
    )

    fun buildAppContentScreenDetail(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        screenType: ScreenDetailType = ScreenDetailType.HOME,
        sectionType: ScreenDetailSectionType = ScreenDetailSectionType.QUESTIONNAIRE_SECTION,
        sectionContext: ScreenDetailSectionContext = ScreenDetailSectionContext.QUESTIONNAIRE,
        sectionContent: String = "home",
        startDate: LocalDateTime = LocalDateTime.now(),
        endDate: LocalDateTime = LocalDateTime.now(),
        status: ScreenDetailStatus = ScreenDetailStatus.ACTIVE,
        position: Int = 0,
        healthFormSource: HealthFormAnswerSource? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        healthDemandName: String? = null,
    ) = AppContentScreenDetail(
        id = id,
        personId = personId,
        screenType = screenType,
        sectionType = sectionType,
        sectionContext = sectionContext,
        sectionContent = sectionContent,
        startDate = startDate,
        endDate = endDate,
        status = status,
        position = position,
        healthFormSource = healthFormSource,
        createdAt = createdAt,
        updatedAt = updatedAt,
        healthDemandName = healthDemandName,
    )

    fun buildTimeline(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        staffId: UUID? = RangeUUID.generate(),
        specialtyId: UUID? = null,
        channelIds: List<String> = emptyList(),
        providerUnitId: UUID? = RangeUUID.generate(),
        description: String = "Appointment test",
        title: String = "title",
        evolutions: List<TimelineEvolution> = emptyList(),
        draftGroup: List<UUID> = emptyList(),
        type: TimelineType = TimelineType.THIRD_PARTY_APPOINTMENT_EMERGENCY,
        status: TimelineStatus? = null,
        referencedLinks: List<Timeline.ReferencedLink> = emptyList(),
        referencedModelId: UUID = RangeUUID.generate(),
        referencedModelClass: TimelineReferenceModel = TimelineReferenceModel.HAOC_PRONTO_ATENDIMENTO_RESULT,
        referencedModelDate: LocalDateTime = LocalDateTime.now(),
        appendages: List<TimelineAppendage> = emptyList(),
        aiSummary: String? = null,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = Timeline(
        id = id,
        personId = personId,
        staffId = staffId,
        specialtyId = specialtyId,
        channelIds = channelIds,
        providerUnitId = providerUnitId,
        description = description,
        evolutions = evolutions,
        draftGroup = draftGroup,
        title = title,
        type = type,
        status = status,
        referencedLinks = referencedLinks,
        referencedModelId = referencedModelId,
        referencedModelClass = referencedModelClass,
        referencedModelDate = referencedModelDate,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        appendages = appendages,
        aiSummary = aiSummary,
    )

    fun buildTimelineAiSummaryReview(
        id: UUID = RangeUUID.generate(),
        timelineId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        score: TimelineAiSummaryReviewScore = TimelineAiSummaryReviewScore.NOT_REVIEWED,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = TimelineAiSummaryReview(
        id = id,
        timelineId = timelineId,
        staffId = staffId,
        score = score,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildProductRecommendationByAge(
        id: UUID = RangeUUID.generate(),
        active: Boolean = true,
        recommendedByAge: List<ProductsRecommendedByAge> = listOf(
            ProductsRecommendedByAge(
                1,
                18,
                ProductAnchor.ALICE_FULL
            )
        )
    ) = ProductRecommendation(
        id = id,
        active = active,
        recommendedByAge = recommendedByAge
    )

    fun buildHLActionRecommendations(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        caseId: UUID = RangeUUID.generate(),
        action: HLActionRecommendationAction = HLActionRecommendationAction.COMPENSATE,
        status: HLActionRecommendationStatus = HLActionRecommendationStatus.ACTIVE,
        type: HLActionRecommendationType = HLActionRecommendationType.HEALTH_LOGIC,
        origin: HLActionRecommendationOrigin = HLActionRecommendationOrigin(
            id = RangeUUID.generate().toString(),
            model = HLActionRecommendationModel.CLINICAL_OUTCOME_RECORD
        )
    ) = HLActionRecommendation(
        id = id,
        personId = personId,
        caseId = caseId,
        action = action,
        status = status,
        type = type,
        origin = origin
    )

    fun buildTussProcedureSpecialty(
        aliceCode: String = "aliceCode",
        tussCode: String = "tussCode",
        medicalSpecialtyId: UUID? = RangeUUID.generate(),
        healthSpecialistResourceBundleId: UUID? = RangeUUID.generate(),
        tier: SpecialistTier = SpecialistTier.TALENTED,
        healthSpecialistScore: HealthSpecialistScoreEnum? = HealthSpecialistScoreEnum.DOMINATING,
        beginAt: LocalDate = LocalDate.now(),
        brand: Brand = Brand.ALICE_DUQUESA,
        productTier: TierType = TierType.TIER_1,
        price: BigDecimal = BigDecimal(102.23),
    ) = TussProcedureSpecialty(
        tier = tier,
        price = price,
        updatedStaffId = RangeUUID.generate(),
        beginAt = beginAt,
        tussCode = tussCode,
        aliceCode = aliceCode,
        medicalSpecialtyId = medicalSpecialtyId,
        healthSpecialistScore = healthSpecialistScore,
        brand = brand,
        productTier = productTier,
        healthSpecialistResourceBundleId = healthSpecialistResourceBundleId
    )

    fun buildCounterReferralRelevance(
        id: UUID = RangeUUID.generate(),
        counterReferralId: UUID = RangeUUID.generate(),
        isNecessary: Boolean = true,
        comments: String? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = CounterReferralRelevance(
        id = id,
        counterReferralId = counterReferralId,
        isNecessary = isNecessary,
        comments = comments,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildTestCodeAnalyte(
        analyteId: String = "98045678892",
        analyteName: String = "HDL - Colesterol",
        codeAlice: String = "9804567",
        codeItem: String = "hdl - colesterol",
        provider: ProviderTestCode.Brand = ProviderTestCode.Brand.DASA,
        id: UUID = RangeUUID.generate()
    ) = TestCodeAnalyte(
        analyteId = analyteId,
        analyteName = analyteName,
        codeAlice = codeAlice,
        codeItem = codeItem,
        provider = provider,
        id = id
    )

    fun buildMagicNumbers(
        code: String = "123456",
        status: MagicNumbersStatus = MagicNumbersStatus.NEW,
        link: String = "https://www.google.com",
        expirationAt: LocalDateTime = LocalDateTime.now().plusMinutes(10),
        accessedByUser: Boolean = false,
        generateTo: String = "<EMAIL>"
    ) = MagicNumbers(
        code = code,
        status = status,
        link = link,
        expirationAt = expirationAt,
        accessedByUser = accessedByUser,
        generateTo = generateTo
    )

    fun buildAnalyteOutcomeMapping(
        id: UUID = RangeUUID.generate(),
        outcomeId: UUID = RangeUUID.generate(),
        analyteId: String = "00011122"
    ) = AnalyteOutcomeMapping(
        id = id,
        outcomeId = outcomeId,
        analyteId = analyteId
    )

    fun buildNullvsIntegrationRecord(
        id: UUID = RangeUUID.generate(),
        internalId: UUID = RangeUUID.generate(),
        internalModelName: InternalModelType = InternalModelType.MEMBER,
        externalId: String = "00010003000001010",
        externalModelName: ExternalModelType = ExternalModelType.BENEFICIARY,
        integratedAt: LocalDateTime = LocalDateTime.now(),
        canceledAt: LocalDateTime? = null,
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = NullvsIntegrationRecord(
        id = id,
        internalId = internalId,
        internalModelName = internalModelName,
        externalId = externalId,
        externalModelName = externalModelName,
        integratedAt = integratedAt,
        canceledAt = canceledAt,
        updatedAt = updatedAt
    )

    fun buildItauPayment(
        id: UUID = RangeUUID.generate(),
        invoicePaymentId: UUID = RangeUUID.generate(),
        pixId: String? = null,
        boletoId: String? = null,
        ourNumber: Int? = 0,
        paidResponse: String? = null,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = ItauPayment(
        id = id,
        invoicePaymentId = invoicePaymentId,
        pixId = pixId,
        boletoId = boletoId,
        ourNumber = ourNumber,
        paidResponse = paidResponse,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildEitaNullvsIntegrationRecord(
        id: UUID = RangeUUID.generate(),
        internalId: UUID = RangeUUID.generate(),
        internalModelName: EitaNullvsInternalModelType = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalId: String = "00010003000001010",
        externalModelName: EitaNullvsExternalModelType = EitaNullvsExternalModelType.BUNDLE,
        integratedAt: LocalDateTime = LocalDateTime.now(),
        canceledAt: LocalDateTime? = null,
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = EitaNullvsIntegrationRecord(
        id = id,
        internalId = internalId,
        internalModelName = internalModelName,
        externalId = externalId,
        externalModelName = externalModelName,
        integratedAt = integratedAt,
        canceledAt = canceledAt,
        updatedAt = updatedAt
    )

    fun buildNullvsIntegrationLog(
        id: UUID = RangeUUID.generate(),
        eventId: UUID = RangeUUID.generate(),
        eventName: String = "MEMBER_CREATED_EVENT",
        internalId: UUID = RangeUUID.generate(),
        internalModelName: InternalModelType = InternalModelType.MEMBER,
        externalModelName: ExternalModelType = ExternalModelType.BENEFICIARY,
        integrationEventName: String = "beneficiario_create_request",
        batchId: String = "0000000000001",
        idSoc: String = RangeUUID.generate().toString(),
        payloadSequenceId: Int = 1,
        batchType: BatchType = BatchType.CREATE,
        description: String? = null,
        status: LogStatus = LogStatus.PENDING,
        dependsOnId: UUID? = null,
        dependsOnModel: InternalModelType? = null,
    ) = NullvsIntegrationLog(
        id = id,
        eventId = eventId,
        eventName = eventName,
        internalId = internalId,
        internalModelName = internalModelName,
        externalModelName = externalModelName,
        integrationEventName = integrationEventName,
        batchId = batchId,
        idSoc = idSoc,
        payloadSequenceId = payloadSequenceId,
        batchType = batchType,
        description = description,
        status = status,
        dependsOnId = dependsOnId,
        dependsOnModel = dependsOnModel,
    )

    fun buildEitaNullvsIntegrationLog(
        id: UUID = RangeUUID.generate(),
        eventId: UUID = RangeUUID.generate(),
        eventName: String = "HEALTHCARE_BUNDLE_CREATED_EVENT",
        internalId: UUID = RangeUUID.generate(),
        internalModelName: EitaNullvsInternalModelType = EitaNullvsInternalModelType.HEALTHCARE_BUNDLE,
        externalModelName: EitaNullvsExternalModelType = EitaNullvsExternalModelType.BUNDLE,
        integrationEventName: String = "bundle_create_request",
        batchId: String = "0000000000001",
        idSoc: String = RangeUUID.generate().toString(),
        payloadSequenceId: Int = 1,
        batchType: BatchType = BatchType.CREATE,
        description: String? = null,
        status: LogStatus = LogStatus.PENDING,
    ) = EitaNullvsIntegrationLog(
        id = id,
        eventId = eventId,
        eventName = eventName,
        internalId = internalId,
        internalModelName = internalModelName,
        externalModelName = externalModelName,
        integrationEventName = integrationEventName,
        batchId = batchId,
        idSoc = idSoc,
        payloadSequenceId = payloadSequenceId,
        batchType = batchType,
        description = description,
        status = status,
    )

    fun buildSpecialistOpinion(
        id: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        appointmentId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        status: SpecialistOpinionStatus = SpecialistOpinionStatus.REQUESTED,
        medicalSpecialtyId: UUID = RangeUUID.generate(),
        assignedStaffId: UUID? = null,
        assignedAt: LocalDateTime? = null,
        caseSummary: String = "case",
        question: String = "question",
        demand: Disease? = null,
        files: List<UUID> = emptyList(),
        personId: PersonId = PersonId(),
        emailId: String? = null,
    ) =
        SpecialistOpinion(
            id = id,
            version = version,
            createdAt = createdAt,
            updatedAt = updatedAt,
            appointmentId = appointmentId,
            staffId = staffId,
            status = status,
            medicalSpecialtyId = medicalSpecialtyId,
            assignedStaffId = assignedStaffId,
            assignedAt = assignedAt,
            caseSummary = caseSummary,
            question = question,
            demand = demand,
            files = files,
            personId = personId,
            additionalParameters = AdditionalParameters(
                emailId = emailId
            )
        )

    fun buildSpecialistOpinionMessage(
        id: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        specialistOpinionId: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        message: String = "message"
    ) =
        SpecialistOpinionMessage(
            id = id,
            version = version,
            createdAt = createdAt,
            updatedAt = updatedAt,
            specialistOpinionId = specialistOpinionId,
            staffId = staffId,
            message = message
        )

    fun buildGenerateExternalAttendancePa(
        externalId: String = "12345",
        origin: ExternalPaOrigin = ExternalPaOrigin.CIA,
        link: String = "teste.com.br",
        personId: PersonId = PersonId(),
        id: UUID = RangeUUID.generate()
    ) = GenerateExternalAttendancePa(
        link = link,
        origin = origin,
        externalId = externalId,
        personId = personId,
        id = id
    )

    fun buildPersonEligibilityDuquesa(
        personId: PersonId = PersonId(),
        externalPersonId: String = "12345",
        brand: PersonEligibilityBrand = PersonEligibilityBrand.CIA,
        startDate: LocalDateTime = LocalDateTime.now(),
        dueDate: LocalDateTime = LocalDateTime.now().plusYears(1),
    ) = PersonEligibilityDuquesa(
        personId = personId,
        externalPersonId = externalPersonId,
        brand = brand,
        startDate = startDate,
        dueDate = dueDate,
    )

    fun buildHealthPlanTaskGroupTemplate(
        name: String = "Atividade fisica",
        taskTemplateIds: List<UUID> = emptyList(),
        id: UUID = RangeUUID.generate()
    ) = HealthPlanTaskGroupTemplate(
        id = id,
        name = name,
        taskTemplateIds = taskTemplateIds
    )

    fun buildHLAdherence(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        taskId: UUID = RangeUUID.generate(),
        validationHistory: List<AdherenceValidation> = emptyList(),
        acceptedRecommendation: HLAdherence.AcceptedRecommendation = HLAdherence.AcceptedRecommendation.PENDING,
        comment: String? = null,
        status: HLAdherence.AdherenceStatus = HLAdherence.AdherenceStatus.PENDING,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = HLAdherence(
        id = id,
        personId = personId,
        taskId = taskId,
        validationHistory = validationHistory,
        acceptedRecommendation = acceptedRecommendation,
        comment = comment,
        status = status,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildExternalAppointmentSchedule(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        slotId: String = "123",
        verificationCode: String? = null,
        appointmentType: ScheduleAppointmentType? = ScheduleAppointmentType.DOCTOR_FAMILY,
        startTime: LocalDateTime = LocalDateTime.now(),
        endTime: LocalDateTime? = LocalDateTime.now(),
        provider: ScheduleProvider = ScheduleProvider.CIA,
        status: ScheduleStatus = ScheduleStatus.SCHEDULED,
        type: ScheduleType = ScheduleType.PRESENTIAL,
        location: ScheduleLocation? = null,
        professional: Professional = buildDuquesaScheduleProfessional(),
        attendanceLink: String? = null,
        appointmentScheduleId: UUID? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        version: Int = 0
    ) = ExternalAppointmentSchedule(
        id = id,
        personId = personId,
        slotId = slotId,
        verificationCode = verificationCode,
        appointmentType = appointmentType,
        startTime = startTime,
        endTime = endTime,
        provider = provider,
        status = status,
        type = type,
        location = location,
        professional = professional,
        attendanceLink = attendanceLink,
        appointmentScheduleId = appointmentScheduleId,
        createdAt = createdAt,
        updatedAt = updatedAt,
        version = version,
    )

    fun buildHealthPlanTaskReferrals(
        id: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        personId: PersonId = PersonId(),
        healthPlanTaskId: UUID = RangeUUID.generate(),
        releasedAt: LocalDateTime? = LocalDateTime.now(),
        deadlineAt: LocalDateTime? = LocalDateTime.now().plusWeeks(1),
        status: HealthPlanTaskStatus = HealthPlanTaskStatus.ACTIVE,
        sessionsQuantity: Int = 10,
        specialty: ReferralSpecialty? = ReferralSpecialty(name = "Ortopedia", id = RangeUUID.generate()),
        subSpecialty: ReferralSpecialty? = ReferralSpecialty(name = "Joelho", id = RangeUUID.generate()),
        suggestedSpecialist: SuggestedSpecialist? = SuggestedSpecialist(
            name = "João",
            id = RangeUUID.generate(),
            type = SpecialistType.STAFF
        ),
        caseRecordDetails: List<CaseRecordDetails> = emptyList(),
        counterReferrals: List<CounterReferrals> = emptyList(),
        additionalCounterReferrals: List<UUID> = emptyList(),
    ) = HealthPlanTaskReferrals(
        id = id,
        createdAt = createdAt,
        updatedAt = updatedAt,
        personId = personId,
        healthPlanTaskId = healthPlanTaskId,
        releasedAt = releasedAt,
        deadlineAt = deadlineAt,
        status = status,
        sessionsQuantity = sessionsQuantity,
        specialty = specialty,
        subSpecialty = subSpecialty,
        suggestedSpecialist = suggestedSpecialist,
        caseRecordDetails = caseRecordDetails,
        counterReferrals = counterReferrals,
        additionalCounterReferrals = additionalCounterReferrals
    )

    fun buildScreeningNavigation(
        status: ScreeningNavigationStatus = ScreeningNavigationStatus.FINISHED,
        personId: PersonId = PersonId(),
        id: UUID = RangeUUID.generate()
    ) = ScreeningNavigation(
        status = status,
        personId = personId,
        id = id
    )

    fun buildScreenData(
        screenId: UUID = RangeUUID.generate(),
        placeholderValues: Map<String, String> = emptyMap(),
        referencedModelClass: ScreenDataReferenceModel = ScreenDataReferenceModel.SERVICE_SCRIPT_NODE,
        referencedModelId: UUID = RangeUUID.generate(),
        answerMapping: Map<String, UUID> = emptyMap(),
    ) = ScreenData(
        screenId = screenId,
        placeholderValues = placeholderValues,
        referencedModelClass = referencedModelClass,
        referencedModelId = referencedModelId,
        answerMapping = answerMapping,
    )

    fun buildFinancialData(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        bankCode: String = "001",
        bankName: String? = null,
        bankAgency: String = "0001",
        accountNumber: String = "000001",
        nationalId: String = "***********",
        accountNickname: String? = null,
        active: Boolean = false,
    ) = FinancialData(
        id = id,
        personId = personId,
        bankCode = bankCode,
        bankName = bankName,
        bankAgency = bankAgency,
        accountNumber = accountNumber,
        nationalId = nationalId,
        accountNickname = accountNickname,
        active = active
    )

    fun buildRefund(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        healthEventType: RefundHealthEventType,
        financialDataId: UUID? = null
    ) = Refund(
        id = id,
        personId = personId,
        healthEventType = healthEventType,
        financialDataId = financialDataId,
    )

    fun buildRefundFile(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        refundId: UUID = RangeUUID.generate(),
        fileVaultId: UUID = RangeUUID.generate(),
        type: RefundFileType,
        sizeBytes: Int = Random.nextInt(),
        extension: String = "JPG",
        filename: String = "screenshot_01"
    ) = RefundFile(
        id = id,
        refundId = refundId,
        personId = personId,
        type = type,
        fileVaultId = fileVaultId,
        sizeBytes = sizeBytes,
        extension = extension,
        filename = filename
    )

    fun buildRefundCounterReferral(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        refundId: UUID = RangeUUID.generate(),
        medicalSpecialtyId: UUID = RangeUUID.generate(),
        attendanceAt: LocalDateTime = LocalDateTime.now(),
        healthConditionIds: List<UUID> = emptyList(),
        staffName: String? = null,
        staffCouncilState: State? = null,
        staffCouncilNumber: String? = null,
        clinicalEvaluation: String? = null,
        medicalOrientation: RefundMedicalOrientation? = null,
        diagnosticHypothesis: String? = null,
        sessionsQuantity: Int? = null,
    ) = RefundCounterReferral(
        id = id,
        refundId = refundId,
        personId = personId,
        medicalSpecialtyId = medicalSpecialtyId,
        attendanceAt = attendanceAt,
        healthConditionIds = healthConditionIds,
        staffName = staffName,
        staffCouncilState = staffCouncilState,
        staffCouncilNumber = staffCouncilNumber,
        clinicalEvaluation = clinicalEvaluation,
        medicalOrientation = medicalOrientation,
        diagnosticHypothesis = diagnosticHypothesis,
        sessionsQuantity = sessionsQuantity
    )

    fun buildAppointmentAutofillHistory(
        id: UUID = RangeUUID.generate(),
        requestPrompt: String = "here prompt is",
        channelContent: String = "here prompt is",
        channelId: String = "channel_id",
        appointmentId: UUID = RangeUUID.generate(),
        responseRaw: JsonRaw = JsonRaw("{}"),
        componentAppointment: AppointmentComponentType = AppointmentComponentType.OBJECTIVE,
        responseType: ResponseType = ResponseType.SUCCESS,
        autofillClient: AutoFillClient = AutoFillClient(client = AutofillClientType.OPEN_AI, "2023_version"),
        messageResponse: String? = "patient is good",
        requestDuration: RequestDurationRequestDuration = RequestDurationRequestDuration(
            processStartedAt = Timestamp.now(),
            channelEndedAt = Timestamp.now(),
            aiEndedAt = Timestamp.now()
        ),
    ) = AppointmentAutofillHistory(
        id = id,
        requestPrompt = requestPrompt,
        channelContent = channelContent,
        channelId = channelId,
        appointmentId = appointmentId,
        responseRaw = responseRaw,
        componentAppointment = componentAppointment,
        responseType = responseType,
        autofillClient = autofillClient,
        messageResponse = messageResponse,
        requestDuration = requestDuration,
    )

    fun buildStaffSignToken(
        id: UUID = RangeUUID.generate(),
        staffId: UUID = RangeUUID.generate(),
        token: String = "d7c00ccb6d00f246266f48f3909dc42d252291b2"
    ) = StaffSignToken(
        id = id,
        staffId = staffId,
        token = token
    )

    fun buildHippocratesHealthProfessional(
        fullName: String = "HP",
        councilName: HippocratesHealthProfessionalCouncil = HippocratesHealthProfessionalCouncil.CRM,
        councilState: State = State.SP,
        councilNumber: String = "1"
    ) = HippocratesHealthcareProfessional(
        fullName = fullName,
        councilName = councilName,
        councilNumber = councilNumber,
        councilState = councilState
    )

    fun buildHealthcareResourceGroup(
        name: String = "",
        type: HealthcareResourceType = HealthcareResourceType.EXAM,
    ) = HealthcareResourceGroup(
        name = name,
        type = type,
    )

    fun buildHealthcareResourceGroupAssociation(
        healthcareResourceGroupId: UUID = RangeUUID.generate(),
        healthcareResourceId: UUID = RangeUUID.generate(),
    ) = HealthcareResourceGroupAssociation(
        healthcareResourceGroupId = healthcareResourceGroupId,
        healthcareResourceId = healthcareResourceId,
    )

    fun buildHealthcareBundle(
        code: String? = "",
        primaryTuss: String? = "",
        status: HealthcareBundleStatus = HealthcareBundleStatus.ACTIVE,
        category: HealthcareBundleCategory = HealthcareBundleCategory.HEALTH_SPECIALIST,
        hasMedicalFees: Boolean = false,
        validAfter: LocalDate = LocalDate.now(),
        validBefore: LocalDate? = null,
        name: String = "Apendicectomia",
        providerName: String? = null,
        groups: List<UUID> = emptyList(),
        includedResources: List<UUID> = emptyList(),
        excludedResources: List<UUID> = emptyList(),
        compositionHash: String? = null,
        providerUnitGroupId: UUID? = null,
        primaryTussHealthcareResourceId: UUID? = null,
    ) = HealthcareBundle(
        code = code,
        primaryTuss = primaryTuss,
        status = status,
        category = category,
        hasMedicalFees = hasMedicalFees,
        validAfter = validAfter,
        validBefore = validBefore,
        name = name,
        providerName = providerName,
        groups = groups,
        includedResources = includedResources,
        excludedResources = excludedResources,
        compositionHash = compositionHash,
        providerUnitGroupId = providerUnitGroupId,
        primaryTussHealthcareResourceId = primaryTussHealthcareResourceId,
    )

    fun buildSalesFirm(
        id: UUID = RangeUUID.generate(),
        name: String = "Corretora Tricolor",
        legalName: String = "Tricolor Corretora de Seguros LTDA",
        cnpj: String = "32246413000147",
        email: String = "<EMAIL>",
        phoneNumber: String = "**********",
    ) = SalesFirm(
        id = id,
        name = name,
        legalName = legalName,
        cnpj = cnpj,
        email = email,
        phoneNumber = phoneNumber,
    )

    fun buildOngoingCompanyDeal(
        name: String = "Calleri Participações.",
        salesFirmId: UUID = RangeUUID.generate(),
        cnpj: String = "60517984000104",
        salesAgentDocument: String? = null,
        salesAgentId: UUID? = null,
        status: DealStage = DealStage.RISK_FLOW,
        legalName: String? = "Calleri Inc",
        dealDetails: OngoingCompanyDealDetails = OngoingCompanyDealDetails(
            employeeCount = 10,
            livesCount = 13,
            contractModel = "???" // Still unsure of this field's content
        ),
        sourceCreatedAt: LocalDateTime = LocalDateTime.now(),
        sourceUpdatedAt: LocalDateTime = LocalDateTime.now(),
        sourceId: String = "1234",
        channel: DealChannel = DealChannel.BROKER,
        companyId: UUID? = RangeUUID.generate()
    ) = OngoingCompanyDeal(
        name = name,
        salesFirmId = salesFirmId,
        cnpj = cnpj,
        salesAgentDocument = salesAgentDocument,
        salesAgentId = salesAgentId,
        status = status,
        legalName = legalName,
        dealDetails = dealDetails,
        sourceCreatedAt = sourceCreatedAt,
        sourceUpdatedAt = sourceUpdatedAt,
        sourceId = sourceId,
        channel = channel,
        companyId = companyId
    )

    fun buildHubspotTicket(
        id: UUID = RangeUUID.generate(),
        dealId: String = "dealId",
        ongoingCompanyDealId: UUID = RangeUUID.generate(),
        ticketId: String = "ticketId",
        pendingTaskDescription: String? = "pendingTaskDescription",
        stage: TicketStage = TicketStage.SALES_PENDING,
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = HubspotTicket(
        id = id,
        dealId = dealId,
        ongoingCompanyDealId = ongoingCompanyDealId,
        ticketId = ticketId,
        pendingTaskDescription = pendingTaskDescription,
        stage = stage,
        updatedAt = updatedAt
    )


    fun buildSalesFirmStaff(
        id: UUID = RangeUUID.generate(),
        salesFirmId: UUID = UUID.randomUUID(),
        firstName: String = "Rogério",
        lastName: String = "Ceni",
        email: String = "<EMAIL>",
    ) = SalesFirmStaff(
        id = id,
        salesFirmId = salesFirmId,
        firstName = firstName,
        lastName = lastName,
        email = email,
        status = Status.ACTIVE,
        role = SalesFirmStaffRole.MAIN_STAFF,
    )

    fun buildAssistanceSummary(
        personId: PersonId = PersonId(),
        curiosity: String = "curiosity",
        comorbiditiesConditions: List<ComorbidityCondition> = listOf(
            ComorbidityCondition(
                healthConditionId = RangeUUID.generate(),
                caseId = RangeUUID.generate(),
                addedAt = LocalDateTime.now(),
                description = DiseaseDetails(
                    id = "id",
                    type = Disease.Type.SYMPTOM,
                    value = "value",
                ),
                seriousness = CaseSeriousness.MEDIUM,
                severity = CaseSeverity.DECOMPENSATED,
                startedAt = LocalDateTime.now(),
                responsibleStaffId = RangeUUID.generate(),
            )
        ),
        pregnancy: AssistanceSummaryPregnancy = AssistanceSummaryPregnancy(
            id = RangeUUID.generate(),
            createdAt = LocalDateTime.now(),
            firstUsgDate = LocalDate.now().minusDays(2),
            lastPeriodDate = LocalDate.now(),
        ),
        imc: BigDecimal = BigDecimal("20")
    ) =
        AssistanceSummary(
            personId = personId,
            curiosity = curiosity,
            comorbiditiesConditions = comorbiditiesConditions,
            pregnancy = pregnancy,
            imc = imc
        )

    fun buildCassiSpecialist(
        id: UUID = RangeUUID.generate(),
        name: String = "Iago",
        specialtyId: UUID = RangeUUID.generate(),
        subSpecialtyIds: List<UUID> = emptyList(),
        email: String? = null,
        gender: Gender? = null,
        council: Council = Council("001", State.RJ),
        phones: List<PhoneNumber> = emptyList(),
        address: List<StructuredAddress> = emptyList(),
        imageUrl: String? = null,
        minAttendanceAge: Int? = null,
        maxAttendanceAge: Int? = null,
        appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
        status: SpecialistStatus = SpecialistStatus.ACTIVE,
        urlSlug: String = "iago",
        searchTokens: String? = null,
        contactIds: List<UUID> = emptyList(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = CassiSpecialist(
        id = id,
        name = name,
        specialtyId = specialtyId,
        subSpecialtyIds = subSpecialtyIds,
        email = email,
        gender = gender,
        council = council,
        phones = phones,
        address = address,
        imageUrl = imageUrl,
        minAttendanceAge = minAttendanceAge,
        maxAttendanceAge = maxAttendanceAge,
        appointmentTypes = appointmentTypes,
        status = status,
        urlSlug = urlSlug,
        searchTokens = searchTokens,
        contactIds = contactIds,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildSuggestedSpecialist(
        id: UUID = RangeUUID.generate(),
        name: String = "Ana",
        speciality: String = "Cardiologista",
        type: SpecialistType = SpecialistType.CLINICAL_COMMUNITY,
        tier: SpecialistTier = SpecialistTier.EXPERT,
        memberTier: ReferralMemberTier? = null
    ) = SuggestedSpecialist(
        id = id,
        name = name,
        speciality = speciality,
        type = type,
        tier = tier,
        memberTier = memberTier
    )

    fun buildCompanyActivationFiles(
        id: UUID = RangeUUID.generate(),
        fileVaultId: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        owner: String? = null,
        fileName: String = "file.pdf",
        type: CompanyActivationFileType = CompanyActivationFileType.NATIONAL_ID
    ) =
        CompanyActivationFiles(
            id = id,
            fileVaultId = fileVaultId,
            companyId = companyId,
            owner = owner,
            fileName = fileName,
            type = type
        )

    fun buildTotvsGuia(
        id: UUID = RangeUUID.generate(),
        code: String = "*********",
        externalCode: String? = null,
        origin: TotvsGuiaOrigin = TotvsGuiaOrigin.EHR,
        requestedAt: LocalDate = LocalDate.now(),
        personId: PersonId = PersonId(),
        type: MvUtil.TISS = MvUtil.TISS.EXAM,
        requestedByProfessional: ProfessionalIdentification = ProfessionalIdentification.DEFAULT_PROFESSIONAL_IDENTIFICATION,
        status: TotvsGuiaStatus = TotvsGuiaStatus.PENDING,
        medicalRequestFileIds: List<UUID> = emptyList(),
        executedOnCreation: Boolean? = false,
        referenceTotvsGuiaId: UUID? = null,
        newBorn: Boolean = false,
        cnpj: String? = null,
        providerUnitId: UUID? = null
    ) = TotvsGuia(
        id = id,
        code = code,
        externalCode = externalCode,
        origin = origin,
        requestedAt = requestedAt,
        personId = personId,
        type = type,
        requestedByProfessional = requestedByProfessional,
        status = status,
        medicalRequestFileIds = medicalRequestFileIds,
        executedOnCreation = executedOnCreation,
        referenceTotvsGuiaId = referenceTotvsGuiaId,
        newBorn = newBorn,
        cnpj = cnpj,
        providerUnitId = providerUnitId
    )

    fun buildCoveredGeoRegion(
        id: UUID = RangeUUID.generate(),
        name: String = "Casa Alice Pinheiros Region",
        latitude: String = "-23.5725362",
        longitude: String = "-46.6928091",
        rangeInMeters: Int = 1000,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = CoveredGeoRegion(
        name = name,
        latitude = latitude,
        longitude = longitude,
        rangeInMeters = rangeInMeters,
        id = id,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildCompanyProductPriceListing(
        id: UUID = RangeUUID.generate(),
        priceListingId: UUID? = null,
        productId: UUID = RangeUUID.generate(),
        ansNumber: String = "ans-number",
        companyId: UUID = RangeUUID.generate(),
        companySubContractId: UUID = RangeUUID.generate(),
        startDate: LocalDate = LocalDate.now(),
        endDate: LocalDate? = LocalDate.now(),
        priceListItems: List<PriceListingItem> = emptyList(),
        product: Product? = null
    ) = CompanyProductPriceListing(
        id = id,
        priceListingId = priceListingId,
        productId = productId,
        ansNumber = ansNumber,
        companyId = companyId,
        companySubContractId = companySubContractId,
        startDate = startDate,
        endDate = endDate,
        priceListItems = priceListItems,
        product = product
    )

    fun buildHealthInstitutionNegotiation(
        id: UUID = RangeUUID.generate(),
        code: String = "code",
        description: String = "description",
        tableType: String = "tableType",
        providerUnitGroupId: UUID = RangeUUID.generate(),
        healthcareResourceId: UUID = RangeUUID.generate(),
        validAfter: LocalDate = LocalDate.now(),
        validBefore: LocalDate? = null,
        activeHealthcareResource: Boolean = true,
        externalId: String = "externalId"
    ) = HealthInstitutionNegotiation(
        id = id,
        code = code,
        description = description,
        tableType = tableType,
        providerUnitGroupId = providerUnitGroupId,
        healthcareResourceId = healthcareResourceId,
        validAfter = validAfter,
        validBefore = validBefore,
        activeHealthcareResource = activeHealthcareResource,
        externalId = externalId
    )

    fun buildHealthSpecialistResourceBundle(
        id: UUID = RangeUUID.generate(),
        primaryTuss: String = "primaryTuss",
        description: String = "description",
        code: String = "code",
        serviceType: HealthSpecialistResourceBundleServiceType = HealthSpecialistResourceBundleServiceType.CONSULTATION,
        status: Status = Status.ACTIVE,
        secondaryResources: List<UUID> = emptyList(),
        tempOldAliceCode: String? = null,
        executionEnvironment: HealthSpecialistProcedureExecutionEnvironment = HealthSpecialistProcedureExecutionEnvironment.DOES_NOT_APPLY
    ) = HealthSpecialistResourceBundle(
        id = id,
        primaryTuss = primaryTuss,
        description = description,
        code = code,
        serviceType = serviceType,
        status = status,
        secondaryResources = secondaryResources,
        tempOldAliceCode = tempOldAliceCode,
        executionEnvironment = executionEnvironment
    )

    fun buildConsolidatedAccreditedNetwork(
        id: UUID = RangeUUID.generate(),
        additionalInfo: AdditionalInfo = AdditionalInfo(
            name = "Alice Provider",
            gender = Gender.FEMALE,
            imageUrl = "web.assets.alice.com.br/providers/accredited-network/logo.png",
            address = buildStructuredAddress(
                latitude = "-23.5718116",
                longitude = "-46.69273700000001"
            ),
            scheduleAvailabilityDays = 3,
        ),
        bundleIds: List<UUID> = emptyList(),
        type: ConsolidatedAccreditedNetworkType = ConsolidatedAccreditedNetworkType.HOSPITAL,
        addressId: UUID = RangeUUID.generate(),
        providerId: UUID? = RangeUUID.generate(),
        tiers: List<SpecialistTier>? = null,
        specialtyIds: List<UUID> = listOf(RangeUUID.generate()),
        subSpecialtyIds: List<UUID> = listOf(RangeUUID.generate()),
        referencedId: UUID = RangeUUID.generate(),
        brand: Brand = Brand.ALICE_DUQUESA,
        appointmentTypes: List<ConsolidatedAccreditedNetworkAppointmentType> = emptyList()
    ) = ConsolidatedAccreditedNetwork(
        id = id,
        additionalInfo = additionalInfo,
        bundleIds = bundleIds,
        type = type,
        addressId = addressId,
        providerId = providerId,
        tiers = tiers,
        specialtyIds = specialtyIds,
        subSpecialtyIds = subSpecialtyIds,
        latitude = additionalInfo.address?.latitude,
        longitude = additionalInfo.address?.longitude,
        referencedId = referencedId,
        brand = brand,
        appointmentTypes = appointmentTypes
    )

    fun buildGuiaWithProcedures(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        code: String = "*********",
        externalCode: String = "00098765456789",
        passcode: String = "************",
        requestedAt: LocalDate = LocalDate.now(),
        status: TotvsGuiaStatus = TotvsGuiaStatus.PENDING,
        requestedByProfessional: ProfessionalIdentification? = ProfessionalIdentification.DEFAULT_PROFESSIONAL_IDENTIFICATION,
        type: MvUtil.TISS = MvUtil.TISS.EXAM,
        procedures: List<GuiaProcedures> = emptyList(),
        closed: Boolean = false
    ) = GuiaWithProcedures(
        id = id,
        personId = personId,
        code = code,
        externalCode = externalCode,
        passcode = passcode,
        requestedAt = requestedAt,
        status = status,
        requestedByProfessional = requestedByProfessional,
        type = type,
        procedures = procedures,
        closed = closed
    )

    fun buildProductInfo(
        healthcareModelType: HealthcareModelType = HealthcareModelType.V3,
        brand: Brand = Brand.ALICE,
        primaryAttention: PrimaryAttentionType = PrimaryAttentionType.ALICE,
        coPayment: CoPaymentType = CoPaymentType.FULL,
        refund: RefundType = RefundType.NONE,
        tierType: TierType = TierType.TIER_1,
        productType: ProductType = ProductType.B2B
    ) = ProductInfo(
        healthcareModelType = healthcareModelType,
        brand = brand,
        primaryAttention = primaryAttention,
        coPayment = coPayment,
        refund = refund,
        tier = tierType,
        productType = productType
    )

    fun buildCsatTemplate(
        title: String = "Avalie seu atendimento no Alice Agora",
        type: String = "CSAT_AA",
        rating: List<CsatRatingOption> = listOf(
            CsatRatingOption(
                value = 1,
                label = "Muito Ruim",
                questions = listOf("improvements"),
                showComments = true
            ),
            CsatRatingOption(
                value = 2,
                label = "Ruim",
                questions = listOf("improvements"),
                showComments = true
            ),
            CsatRatingOption(
                value = 3,
                label = "Razoável",
                questions = listOf("improvements"),
                showComments = true
            ),
            CsatRatingOption(
                value = 4,
                label = "Bom",
                questions = listOf("improvements"),
                showComments = true
            ),
            CsatRatingOption(
                value = 5,
                label = "Excelente",
                questions = listOf("favorites"),
                showComments = true
            )
        ),
        questions: List<CsatQuestion> = listOf(
            CsatQuestion(
                key = "improvements",
                title = "O que poderia ser melhor?",
                tileLabels = listOf(
                    "Comunicação",
                    "Tempo de resposta",
                    "Outro"
                )
            ),
            CsatQuestion(
                key = "favorites",
                title = "Do que mais você gostou?",
                tileLabels = listOf(
                    "Comunicação",
                    "Tempo de resposta",
                    "Outro"
                )
            ),
        ),
        comments: CsatComment = CsatComment(
            title = "Deixar comentário (opcional)",
            placeholder = "Conte sobre esse atendimento"
        ),
        buttonLabel: String = "Avaliar"
    ) =
        CsatTemplate(
            title = title,
            type = type,
            rating = rating,
            questions = questions,
            comments = comments,
            buttonLabel = buttonLabel
        )

    fun buildCsat(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        contextId: String = "Test",
        contextType: ContextType = ContextType.CHANNELS,
        templateId: UUID = RangeUUID.generate(),
        feedback: CsatFeedback = CsatFeedback(title = "title", rate = 5, comment = "Very good", answers = emptyList()),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = Csat(
        id = id,
        personId = personId,
        contextId = contextId,
        contextType = contextType,
        templateId = templateId,
        feedback = feedback,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildZendeskExternalReference(
        id: UUID = RangeUUID.generate(),
        originModel: ZendeskExternalReference.OriginModel = ZendeskExternalReference.OriginModel.PERSON,
        originModelId: String = RangeUUID.generate().toString(),
        destinationModel: ZendeskExternalReference.DestinationModel = ZendeskExternalReference.DestinationModel.USER,
        destinationId: Long = 787,
        syncedAt: LocalDateTime = LocalDateTime.now(),
        destinationNameSuffix: String = "J8k0",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = ZendeskExternalReference(
        id = id,
        originModel = originModel,
        originModelId = originModelId,
        destinationModel = destinationModel,
        destinationNameSuffix = destinationNameSuffix,
        destinationId = destinationId,
        syncedAt = syncedAt,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildMemberAccreditedNetworkTracker(
        specialities: List<SpecialityAccreditedNetworkTracker> = emptyList(),
        subSpecialities: List<SpecialityAccreditedNetworkTracker> = emptyList(),
        types: List<AccreditedNetworkTrackerFilterType> = listOf(AccreditedNetworkTrackerFilterType.SPECIALIST_AND_CLINICAL),
        latitude: String = "0",
        longitude: String = "0",
        range: String = "1000",
        results: List<AccreditedNetworkTrackerResult> = emptyList(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId()
    ) = MemberAccreditedNetworkTracker(
        specialities = specialities,
        subSpecialities = subSpecialities,
        types = types,
        latitude = latitude,
        longitude = longitude,
        range = range,
        results = results,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        id = id,
        personId = personId,
    )

    fun buildSalesAgent(
        name: String = "Homer Simpson",
        documentNumber: String = "00100100100",
        email: String = "<EMAIL>",
        phoneNumber: String = "11999888777",
        birthDate: LocalDate = LocalDate.of(2000, 1, 1),
        salesFirmId: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        isActiveInCampaign: Boolean = false,
        totalLeadsFromCampaign: Int = 0,
        leadsFromCampaignRound: Int = 0,
        image: String? = null,
        additionalInfo: SalesAgentAdditionalInfo? = SalesAgentAdditionalInfo(),
    ) = SalesAgent(
        name = name,
        documentNumber = documentNumber,
        email = email,
        phoneNumber = phoneNumber,
        birthDate = birthDate,
        salesFirmId = salesFirmId,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        isActiveInCampaign = isActiveInCampaign,
        totalLeadsFromCampaign = totalLeadsFromCampaign,
        leadsFromCampaignRound = leadsFromCampaignRound,
        image = image,
        additionalInfo = additionalInfo,
    )

    fun buildVicProductOption(
        id: UUID = RangeUUID.generate(),
        productId: UUID = RangeUUID.generate(),
        productName: String = "Conforto +",
        productCategory: ProductCategory = ProductCategory.BALANCE,
        accommodation: Accommodation = Accommodation.NURSERY,
        coverage: Coverage = Coverage.NATIONAL,
        siteAccreditedNetworkId: UUID = RangeUUID.generate(),
        coPayment: Boolean = false,
        mainHospitals: List<ProviderRepresentation> = listOf(
            ProviderRepresentation(
                id = RangeUUID.generate(),
                name = "Hospital Albert Einstein",
                icon = "http://hospital-albert-einstein.com.br/icon.png",
            )
        ),
        mainLaboratories: List<ProviderRepresentation> = listOf(
            ProviderRepresentation(
                id = RangeUUID.generate(),
                name = "Laboratório Fleury",
                icon = "http://laboratorio-fleury.com.br/icon.png",
            )
        ),
        type: ProductOptionType = ProductOptionType.ME
    ) = VicProductOption(
        id = id,
        productId = productId,
        productName = productName,
        productCategory = productCategory,
        accommodation = accommodation,
        coverage = coverage,
        siteAccreditedNetworkId = siteAccreditedNetworkId,
        coPayment = coPayment,
        mainLaboratories = mainLaboratories,
        mainHospitals = mainHospitals,
        type = type
    )

    fun buildMemberProductChangeSchedule(
        id: UUID = RangeUUID.generate(),
        memberId: UUID = RangeUUID.generate(),
        productId: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        applyAt: LocalDateTime = LocalDateTime.now()
    ) = MemberProductChangeSchedule(
        id = id,
        memberId = memberId,
        productId = productId,
        personId = personId,
        applyAt = applyAt
    )

    fun buildAIModel(
        id: UUID = RangeUUID.generate(),
        operationType: AIOperationType = AIOperationType.TEXT_TEXT,
        provider: String = "Alice",
        name: String = "model",
        apiUrl: String? = null,
        costPerKToken: BigDecimal? = null,
        internal: Boolean = false,
        interfaceHash: String? = null,
        interfaceContract: Map<String, Any>? = null,
        params: List<AIModelParams> = listOf(
            AIModelParams(
                name = "silliness",
                value = "9000",
                type = "integer"
            )
        )
    ) = AIModel(
        id = id,
        operationType = operationType,
        provider = provider,
        name = name,
        apiUrl = apiUrl,
        costPerKToken = costPerKToken,
        params = params,
        internal = internal,
        interfaceHash = interfaceHash,
        interfaceContract = interfaceContract
    )

    fun buildAISetup(
        id: UUID = RangeUUID.generate(),
        name: String = "setup",
        staffId: UUID = RangeUUID.generate(),
        operationType: AIOperationType = AIOperationType.TEXT_TEXT,
        prompt: String = "prompt",
        interfaceHash: String? = null,
        models: List<AIModelInstance> = listOf()
    ) = AISetup(
        id = id,
        name = name,
        staffId = staffId,
        operationType = operationType,
        prompt = prompt,
        models = models,
        interfaceHash = interfaceHash
    )

    fun buildMedicalSpecialtyProfile(
        specialtyId: UUID = RangeUUID.generate(),
        subSpecialtyIds: List<UUID> = listOf(RangeUUID.generate())
    ): MedicalSpecialtyProfile =
        MedicalSpecialtyProfile(
            specialtyId = specialtyId,
            subSpecialtyIds = subSpecialtyIds
        )

    fun buildAITextInference(
        id: UUID = RangeUUID.generate(),
        setupId: UUID = RangeUUID.generate(),
        setupName: String? = null,
        context: String = "context",
        caller: String = "caller",
        result: AIInferenceResult = AIInferenceResult.SUCCESS,
        estimatedCost: BigDecimal = BigDecimal.ZERO,
        outputs: List<AITextInferenceOutput> = listOf(),
        referenceModel: AITextInferenceReferenceModel? = null
    ) = AITextInference(
        id = id,
        setupId = setupId,
        setupName = setupName,
        context = context,
        caller = caller,
        result = result,
        estimatedCost = estimatedCost,
        outputs = outputs,
        referenceModel = referenceModel
    )

    fun buildHospitalizationInfo(
        providerUnitId: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        suggestedDate: LocalDate = LocalDate.now(),
        suggestedDischarceDate: LocalDate = LocalDate.now(),
        clinicalIndication: String? = "teste",
        healthCondition: HospitalizationHealthCondition = HospitalizationHealthCondition(
            name = "Triquinose",
            code = "B75",
        ),
        attendanceCharacter: AttendanceCharacter = AttendanceCharacter.ELECTIVE,
        type: HospitalizationType = HospitalizationType.CLINICAL,
        numberOfDays: Int = 1,
        dischargeDate: LocalDate? = null,
        startDate: LocalDate? = null,
        opmeIndication: Boolean = false,
        chemotherapyIndication: Boolean = false,
    ) = HospitalizationInfo(
        providerUnitId = providerUnitId,
        totvsGuiaId = totvsGuiaId,
        suggestedDate = suggestedDate,
        suggestedDischargeDate = suggestedDischarceDate,
        clinicalIndication = clinicalIndication,
        healthCondition = healthCondition,
        attendanceCharacter = attendanceCharacter,
        type = type,
        numberOfDays = numberOfDays,
        dischargeDate = dischargeDate,
        startDate = startDate,
        opmeIndication = opmeIndication,
        chemotherapyIndication = chemotherapyIndication,
    )

    fun buildSiteAccreditedNetworkCategory(
        title: String = "Hospital Albert Einstein",
        referencedModelValue: String = "hospital",
        referencedModelOrigin: ReferencedModelOrigin = ReferencedModelOrigin.PROVIDER,
    ) = SiteAccreditedNetworkCategory(
        title = title,
        referencedModelValue = referencedModelValue,
        referencedModelOrigin = referencedModelOrigin,
        active = true,
    )

    fun buildAttachmentOpme(
        id: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        status: AttachmentStatus? = AttachmentStatus.PENDING,
        technicalJustification: String = "Uma justificativa técnica bem legal",
        materialSpecification: String = "Uma especificação de material bem legal",
        requestedOpmes: List<RequestedOPME> = emptyList(),
        observation: String? = null,
    ) = AttachmentOpme(
        id = id,
        totvsGuiaId = totvsGuiaId,
        status = status,
        technicalJustification = technicalJustification,
        materialSpecification = materialSpecification,
        requestedOpmes = requestedOpmes,
        observation = observation,
    )

    fun buildGlossAuthorizationInfo(
        id: UUID = RangeUUID.generate(),
        code: String = "000",
        title: String = "Não pertinente",
        description: String = "Esse procedimento não é pertinente."
    ) = GlossAuthorizationInfo(
        id = id,
        code = code,
        title = title,
        description = description,
    )

    fun buildAttachmentChemotherapy(
        id: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        status: AttachmentStatus = AttachmentStatus.PENDING,
        chemotherapyOncologicalDiagnosis: OncologicalDiagnosis = OncologicalDiagnosis(
            diagnosisDate = LocalDate.now(),
            stage = AnsStage.FIRST,
            type = AnsChemotherapyType.FIRST_LINE,
            purpose = AnsPurpose.ADJUVANT,
            tumor = AnsTumor.NAO_SE_APLICA,
            nodule = AnsNodule.NAO_SE_APLICA,
            metastasis = AnsMetastasis.NAO_SE_APLICA,
            healthCondition = ChemotherapyHealthCondition(
                name = "name",
                code = "123",
            ),
            ecoGt = AnsEcoGT.FULL_ACTIVE
        ),
        requestedDrugs: List<RequestedDrugs> = listOf(
            RequestedDrugs(
                startDate = LocalDate.now(),
                totalCycleDosage = 1.0,
                unitOfMeasurement = AnsUnitOfMeasurement.MG,
                administrationRoute = AnsAdministrationRoute.ORAL,
                frequency = 1,
                status = MvAuthorizedProcedureStatus.PENDING,
                drugsIdentification = DrugsIdentification(
                    table = "19",
                    code = "1245678",
                    description = "droga01",
                )
            )
        ),
        cyclesQuantity: Int = 5,
        currentCycle: Int = 1,
        currentCycleDays: Int = 10,
        cyclesInterval: Int = 10,
        observation: String? = null,
        height: BigDecimal = BigDecimal("180"),
        weight: BigDecimal = BigDecimal("70"),
    ) = AttachmentChemotherapy(
        id = id,
        totvsGuiaId = totvsGuiaId,
        status = status,
        chemotherapyOncologicalDiagnosis = chemotherapyOncologicalDiagnosis,
        requestedDrugs = requestedDrugs,
        cyclesQuantity = cyclesQuantity,
        currentCycle = currentCycle,
        currentCycleDays = currentCycleDays,
        cyclesInterval = cyclesInterval,
        height = height,
        weight = weight,
        observation = observation,
    )

    private fun buildDuquesaScheduleProfessional() = Professional(
        id = "123",
        name = "Alice",
        council = ScheduleProfessionalCouncil(
            number = "1",
            state = State.SP,
            type = ""
        )
    )

    private fun buildHaocProfissional(
        nome: String = "John Doe",
        ocupacao: String = "Médico",
        uf: String = "SP",
        conselho: String = "abc",
        numeroRegistro: String = "12345",
    ) = HaocProfissional(
        nome = nome,
        ocupacao = ocupacao,
        uf = uf,
        conselho = conselho,
        numeroRegistro = numeroRegistro,
    )

    private fun buildHaocDesfecho(
        motivo: String = "",
        dataSaida: LocalDateTime = LocalDateTime.now(),
        profissionalDeAlta: HaocProfissional = buildHaocProfissional()
    ) = HaocDesfecho(
        motivo = motivo,
        dataSaida = dataSaida,
        profissionalDeAlta = profissionalDeAlta
    )

    private fun buildCaracterizacaoAtendimento(
        local: Unidade = Unidade.VERGUEIRO,
        procedencia: String = "",
        dataChegada: LocalDateTime = LocalDateTime.now(),
    ) = CaracterizacaoAtendimento(
        local = local,
        procedencia = procedencia,
        dataChegada = dataChegada
    )

    private fun buildDasaServiceRequest(
        lastUpdated: String = "2021-03-06T21:26:15Z",
        requesterName: String = "Requester name",
    ) = ResourceServiceRequest(
        id = RangeUUID.generate().toString(),
        resourceType = "ServiceRequest",
        meta = ResourceMeta(
            lastUpdated = lastUpdated,
            profile = emptyList()
        ),
        identifier = emptyList(),
        status = "Status",
        code = ResourceCode(
            coding = emptyList()
        ),
        subject = ResourceReference(
            reference = "subject",
            type = "type",
            display = "Subject Name"
        ),
        requester = ResourceReference(
            reference = "requester",
            type = "type",
            display = requesterName
        ),
        insurance = emptyList(),
        requisition = ResourceIdentifier(
            use = "use",
            system = "system",
            value = "value",
        ),
        intent = "intent",
        category = emptyList()
    )

    private fun buildEncounter() = ResourceDiagnosticEncounter(
        id = "Encounter",
        resourceType = "Encounter",
        meta = null,
        identifier = emptyList(),
        status = null,
        `class` = ResourceCoding(),
        serviceType = ResourceCode(coding = emptyList()),
        period = ResourcePeriod(
            start = "2021-05-14T11:33:35.000Z",
            end = "2021-05-14T11:33:35.000Z"
        ),
        participant = listOf(
            ResourceParticipant(
                ResourceReference(
                    reference = "subject",
                    type = "type",
                    display = "Subject Name"
                )
            )
        ),
        serviceProvider = ResourceReference(
            reference = "subject",
            type = "type",
            display = "Subject Name"
        ),
        basedOn = emptyList()
    )

    fun buildMemberOnboarding(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        completed: Boolean = false,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        steps: List<Step> = listOf(
            Step(
                templateType = MemberOnboarding.MemberOnboardingStepType.VIDEO,
                status = MemberOnboarding.MemberOnboardingStepStatus.PENDING
            )
        ),
        referencedLinks: List<MemberOnboardingReferencedLink> = listOf(
            MemberOnboardingReferencedLink(
                id = RangeUUID.generate(),
                model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK
            )
        ),
        flowType: MemberOnboardingFlowType = MemberOnboardingFlowType.CHILD
    ) = MemberOnboarding(
        id = id,
        personId = personId,
        steps = steps,
        completed = completed,
        referencedLinks = referencedLinks,
        flowType = flowType,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildAttachmentRadiotherapy(
        id: UUID = RangeUUID.generate(),
        totvsGuiaId: UUID = RangeUUID.generate(),
        status: AttachmentStatus = AttachmentStatus.PENDING,
        oncologicalDiagnosisRadio: OncologicalDiagnosisRadio = OncologicalDiagnosisRadio(
            diagnosisDate = LocalDate.now(),
            imageDiagnosis = AnsImageDiagnosis.MAGNETIC_RESONANCE,
            stage = AnsStage.FIRST,
            purpose = AnsPurpose.ADJUVANT,
            healthCondition = RadiotherapyHealthCondition(
                name = "name",
                code = "123",
            ),
            ecoGt = AnsEcoGT.FULL_ACTIVE
        ),
        fieldsQuantity: Int = 5,
        fieldDose: Int = 1,
        totalDose: Int = 10,
        daysQuantity: Int = 10,
        expectedStartDate: LocalDate = LocalDate.now().plusDays(1),
        observation: String? = null,
    ) = AttachmentRadiotherapy(
        id = id,
        totvsGuiaId = totvsGuiaId,
        status = status,
        oncologicalDiagnosisRadio = oncologicalDiagnosisRadio,
        fieldsQuantity = fieldsQuantity,
        fieldDose = fieldDose,
        totalDose = totalDose,
        daysQuantity = daysQuantity,
        expectedStartDate = expectedStartDate,
        observation = observation,
    )

    fun buildContact(
        id: UUID = RangeUUID.generate(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        version: Int = 0,
        addressId: UUID? = null,
        website: String? = null,
        phones: List<PhoneNumber> = emptyList(),
        modality: ModalityType = ModalityType.REMOTE,
        scheduleAvailabilityDays: Int? = null,
    ) = Contact(
        id = id,
        updatedAt = updatedAt,
        createdAt = createdAt,
        version = version,
        addressId = addressId,
        website = website,
        phones = phones,
        modality = modality,
        scheduleAvailabilityDays = scheduleAvailabilityDays,
    )

    fun buildChannelHistory(
        channelId: String = "channelId"
    ) = ChannelHistory(
        personId = PersonId(),
        channelId = channelId,
        name = "name",
        type = ChannelType.CHAT,
        status = ChannelStatus.ACTIVE,
        staffIds = listOf(RangeUUID.generate()),
        tags = listOf("tag1"),
        origin = "origin",
        mergedWith = "channel_id2",
        action = ChannelChangeAction.MERGE,
        lastSync = LocalDateTime.now(),
        kind = ChannelKind.CHAT,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.ACUTE,
        subCategoryClassifier = ChannelSubCategoryClassifier.HEALTH_TEAM,
        removed = "nothing",
        screeningStatus = ScreeningStatus.FINISHED,
        screeningFinishedAt = LocalDateTime.now(),
        extraInfos = listOf(
            ChannelHistoryExtraInfo(
                key = ExtraInfoType.QUESTIONNAIRE_ID,
                value = "questionnaire_id"
            ),
            ChannelHistoryExtraInfo(
                key = ExtraInfoType.REJECT_VIDEO_CALL_REASON,
                value = RejectVideoCallReason.WAITING_TIME_TOO_LONGER.name
            )
        ),
        appVersion = "3.30.0"
    )

    fun buildFhirProviderAccess(
        id: UUID = RangeUUID.generate(),
        provider: ProviderIntegration = ProviderIntegration.DASA,
        clientId: UUID = RangeUUID.generate(),
        clientSecret: UUID = RangeUUID.generate(),
        active: Boolean = true,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FhirProviderAccess(
        id = id,
        provider = provider,
        clientId = clientId,
        clientSecret = clientSecret,
        active = active,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildCoPaymentTierPrice() = listOf(
        CoPaymentTierPrice(
            tier = TierType.TIER_1,
            value = BigDecimal("10.00")
        ),
        CoPaymentTierPrice(
            tier = TierType.TIER_2,
            value = BigDecimal("20.00")
        ),
        CoPaymentTierPrice(
            tier = TierType.TIER_3,
            value = BigDecimal("30.00")
        ),
        CoPaymentTierPrice(
            tier = TierType.TIER_4,
            value = BigDecimal("40.00")
        ),
    )

    fun buildCoPaymentCostInfo(
        event: String = "Consulta",
        description: String? = "Consulta com especialista",
        chargeType: CoPaymentChargeType = CoPaymentChargeType.FIXED_VALUE,
        prices: List<CoPaymentTierPrice> = buildCoPaymentTierPrice()
    ) = CoPaymentCostInfo(
        event = event,
        description = description,
        chargeType = chargeType,
        prices = prices
    )

    fun buildMemberTelegramTracking(
        externalId: String = "W123456",
        name: String? = null,
        tracking: String = "MW012297144BR"
    ) =
        MemberTelegramTracking(externalId = externalId, name = name, tracking = tracking)

    fun buildConsolidatedRating(
        id: UUID = RangeUUID.generate(),
        healthProfessionalId: UUID = RangeUUID.generate(),
        aiGeneratedComment: String? = "comment",
        ratingDetail: RatingDetail = RatingDetail(),
    ) = ConsolidatedRating(
        id = id,
        healthProfessionalId = healthProfessionalId,
        aiGeneratedComment = aiGeneratedComment,
        ratingDetail = ratingDetail,
    )

    fun buildHealthProfessionalOpsProfile(
        id: UUID = RangeUUID.generate(),
        healthProfessionalId: UUID = RangeUUID.generate(),
        attendsToOnCall: Boolean = false,
        onCallPaymentMethod: OnCallPaymentMethod? = null,
    ) = HealthProfessionalOpsProfile(
        id = id,
        healthProfessionalId = healthProfessionalId,
        attendsToOnCall = attendsToOnCall,
        onCallPaymentMethod = onCallPaymentMethod,
    )

    fun buildConsolidatedPopulationData() =
        ConsolidatedPopulationData(
            activeMembers = 65,
            medianAge = 27,
            holders = SectionData(name = "Titular", count = 40, percentage = 61F),
            dependents = SectionData(name = "Dependente", count = 25, percentage = 39F),
            riskClassification = listOf(
                SectionData(
                    name = "Alto risco",
                    percentage = 4F,
                ),
                SectionData(name = "Médio risco", percentage = 8F),
                SectionData(name = "Baixo risco", percentage = 28F),
                SectionData(name = "Ausência de risco", percentage = 60F)
            ),
            healthConditions = listOf(
                SectionData(name = "Ansiedade", percentage = 14F),
                SectionData(name = "Transtorno não identificado", percentage = 12F),
                SectionData(name = "Diarréia e Gastroenterite", percentage = 8F)
            ),
            sex = listOf(
                SectionData(name = "Feminino", percentage = 60F),
                SectionData(name = "Masculino", percentage = 40F)
            ),
        )

    fun buildConsolidatedFrequencyCoordinationData() =
        JourneyData(
            coordinationRate = 33.333333F,
            frequencyRate = FrequencyRate(
                aa = FrequencyRateItem(
                    totalEvents = 100,
                    uniqueMembers = 33,
                    historic = listOf(
                        HistoricItem(
                            period = "2021-01-10",
                            value = 33.333332F,
                            benchmark = 33.333332F,
                            activeMembers = 33,
                        )
                    )
                ),
                exams = FrequencyRateItem(
                    totalEvents = 100,
                    uniqueMembers = 33,
                    historic = listOf(
                        HistoricItem(
                            period = "2021-01-10",
                            value = 33.333332F,
                            benchmark = 33.333332F,
                            activeMembers = 33,
                        )
                    )
                ),
                familyDoctor = FrequencyRateItem(
                    totalEvents = 100,
                    uniqueMembers = 33,
                    historic = listOf(
                        HistoricItem(
                            period = "2021-01-10",
                            value = 33.333332F,
                            benchmark = 33.333332F,
                            activeMembers = 33,
                        )
                    )
                ),
                therapies = FrequencyRateItem(
                    totalEvents = 100,
                    uniqueMembers = 33,
                    historic = listOf(
                        HistoricItem(
                            period = "2021-01-10",
                            value = 33.333332F,
                            benchmark = 33.333332F,
                            activeMembers = 33,
                        )
                    )
                ),
                specialist = FrequencyRateItem(
                    totalEvents = 100,
                    uniqueMembers = 33,
                    historic = listOf(
                        HistoricItem(
                            period = "2021-01-10",
                            value = 33.333332F,
                            benchmark = 33.333332F,
                            activeMembers = 33,
                        )
                    )
                ),
            )
        )

    fun buildConsolidatedHRCompanyReport(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        populationData: ConsolidatedPopulationData = buildConsolidatedPopulationData(),
        frequencyCoordinationData: JourneyData = buildConsolidatedFrequencyCoordinationData(),
    ) =
        ConsolidatedHRCompanyReport(
            id = id,
            companyId = companyId,
            populationData = populationData,
            journey = frequencyCoordinationData,
            populationDataUpdatedAt = LocalDateTime.now(),
        )

    fun buildAppointmentProcedureExecuted(
        id: UUID = RangeUUID.generate(),
        appointmentId: UUID = RangeUUID.generate(),
        name: String? = null,
        typeOfService: TypeOfService? = null,
        status: ProcedureExecutedStatus = ProcedureExecutedStatus.DONE,
        tussCode: String = "123456",
        tussProcedureAliceCode: String? = null,
        healthSpecialistResourceBundleCode: String? = null,
        isPriced: Boolean = false,
    ) = AppointmentProcedureExecuted(
        id = id,
        appointmentId = appointmentId,
        status = status,
        tussCode = tussCode,
        tussProcedureAliceCode = tussProcedureAliceCode,
        healthSpecialistResourceBundleCode = healthSpecialistResourceBundleCode,
        isPriced = isPriced,
        name = name,
        typeOfService = typeOfService
    )

    fun buildCompanyScoreMagenta(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        referenceStartDate: LocalDateTime = LocalDateTime.now().minusMonths(3).atBeginningOfTheDay(),
        referenceEndDate: LocalDateTime = LocalDateTime.now().atEndOfTheDay(),
        generalScore: Float = 700f,
        eatingScore: Float = 650f,
        physicalActivityScore: Float = 750f,
        mentalHealthScore: Float = 800f,
        sleepScore: Float = 600f,
        habitsScore: Float = 700f,
        lifeQualityScore: Float = 750f,
        adhesion: Float = 100f,
        answersQuantity: Int = 100,
        descriptionByKey: DescriptionByKeyList = DescriptionByKeyList(
            listOf(
                DescriptionByKey(
                    key = "MSQ_SCORE_MAGENTA",
                    excellent = 60f,
                    good = 30f,
                    bad = 10f,
                )
            )
        )
    ) = CompanyScoreMagenta(
        id = id,
        companyId = companyId,
        referenceStartDate = referenceStartDate,
        referenceEndDate = referenceEndDate,
        generalScore = generalScore,
        eatingScore = eatingScore,
        physicalActivityScore = physicalActivityScore,
        mentalHealthScore = mentalHealthScore,
        sleepScore = sleepScore,
        habitsScore = habitsScore,
        lifeQualityScore = lifeQualityScore,
        adhesion = adhesion,
        answersQuantity = answersQuantity,
        descriptionByKey = descriptionByKey
    )

    fun buildCompanyRefundCostInfo(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        prices: List<CompanyRefundCostInfoPrices> = listOf(
            CompanyRefundCostInfoPrices(
                value = BigDecimal("10.00"),
                eventType = RefundEventType.THERAPY,
                event = "Psicologia",
            ),
        ),
        tier: TierType = TierType.TIER_1,
        referenceDate: LocalDate = LocalDate.now(),
        fileVaultId: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = CompanyRefundCostInfo(
        id = id,
        companyId = companyId,
        prices = prices,
        tier = tier,
        referenceDate = referenceDate,
        fileVaultId = fileVaultId,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
    )

    fun buildMemberLifeCycleEvents(
        reason: MemberLifecycleReasonEvents = MemberLifecycleReasonEvents.COURT_ORDER,
        observation: String? = "Observation",
        type: MemberLifecycleEventType = MemberLifecycleEventType.REACTIVATION,
        actionAt: LocalDate = LocalDate.now(),
        memberId: UUID = RangeUUID.generate(),
    ) = MemberLifeCycleEvents(
        reason = reason,
        observation = observation,
        type = type,
        actionAt = actionAt,
        memberId = memberId
    )

    fun buildSalesFirmAgentPartnership(
        salesFirmId: UUID = RangeUUID.generate(),
        salesAgentId: UUID = RangeUUID.generate(),
    ) = SalesFirmAgentPartnership(
        id = RangeUUID.generate(),
        salesAgentId = salesAgentId,
        salesFirmId = salesFirmId,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    fun buildInvoiceLiquidationTaxReceipt(
        id: UUID = RangeUUID.generate(),
        status: TaxReceiptStatus = TaxReceiptStatus.ISSUED,
        issuedAt: LocalDateTime = LocalDateTime.now(),
        pdfUrl: String = "http://www.example.com",
        invoiceLiquidationId: UUID = RangeUUID.generate(),
    ) = InvoiceLiquidationTaxReceipt(
        id = id,
        status = status,
        issuedAt = issuedAt,
        pdfUrl = pdfUrl,
        invoiceLiquidationId = invoiceLiquidationId
    )

    fun buildAppointmentProcedureExecutedAliceCodeSource(
        appointmentProcedureExecutedId: UUID = RangeUUID.generate(),
        aliceCodeSource: AliceCodeSource = AliceCodeSource.HEALTH_SPECIALIST_RESOURCE_BUNDLE,
    ) = AppointmentProcedureExecutedAliceCodeSource(
        appointmentProcedureExecutedId = appointmentProcedureExecutedId,
        aliceCodeSource = aliceCodeSource,
    )

    fun buildAccreditedNetworkFavorite(
        id: UUID = RangeUUID.generate(),
        personId: PersonId = PersonId(),
        referenceId: UUID = RangeUUID.generate(),
        referenceType: ConsolidatedAccreditedNetworkType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
        specialtyIds: List<UUID> = emptyList(),
        deletedAt: LocalDateTime? = null,
    ): AccreditedNetworkFavorite = AccreditedNetworkFavorite(
        id = id,
        personId = personId,
        referenceId = referenceId,
        referenceType = referenceType,
        specialtyIds = specialtyIds,
        deletedAt = deletedAt,
    )

    fun buildConsolidatedRewards(
        id: UUID = RangeUUID.generate(),
        healthProfessionalId: UUID = RangeUUID.generate(),
        answers: List<ConsolidatedRewardsAnswers> = listOf(
            ConsolidatedRewardsAnswers(
                id = RangeUUID.generate(),
                value = "3",
                healthFormId = RangeUUID.generate(),
                healthFormQuestionId = RangeUUID.generate(),
                sourceId = RangeUUID.generate(),
                sourceType = ConsolidatedRewardsType.COUNTER_REFERRAL
            )
        ),
        clinicalRecords: List<ConsolidatedRewardsClinicalRecords> = listOf(
            ConsolidatedRewardsClinicalRecords(
                id = RangeUUID.generate(),
                type = ConsolidatedRewardsType.COUNTER_REFERRAL,
                createdAt = LocalDate.now(),
                appointmentDate = LocalDate.now()
            )
        ),
        limitDate: LocalDate = LocalDate.now(),
        referenceDate: LocalDate = LocalDate.now(),
        rewardsSummary: RewardsSummary? = RewardsSummary(
            clinicalRecords = GoalClinicalRecords(
                sameDayRecords = 1,
                otherRecords = 0,
                metGoal = true,
                recordSuccessPercentValue = 100
            ),
            answers = GoalAnswers(
                totalConfidenceAnswers = 1,
                confidencePercentValue = 100,
                metGoal = true,
                highConfidenceAnswers = 1,
                lowConfidenceAnswers = 0,
                mediumConfidenceAnswers = 0,
            )
        )
    ): ConsolidatedRewards = ConsolidatedRewards(
        id = id,
        healthProfessionalId = healthProfessionalId,
        answers = answers,
        clinicalRecords = clinicalRecords,
        limitDate = limitDate,
        referenceDate = referenceDate,
        rewardsSummary = rewardsSummary
    )

    fun buildStandardCost(
        id: UUID = RangeUUID.generate(),
        companyBusinessUnit: CompanyBusinessUnit = CompanyBusinessUnit.CORRETORES,
        companySize: CompanySize = CompanySize.MICRO,
        adhesion: Adhesion = Adhesion.OPTIONAL,
        ansNumber: String = "123456",
        costByPersonDetail: CostByPersonDetail = CostByPersonDetail(
            holder = GenderDetail(
                male = listOf(
                    CostByAgeRange(
                        minAge = 0,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(150.0)
                    )
                ),
                female = listOf(
                    CostByAgeRange(
                        minAge = 8,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(285.0)
                    )
                )
            ),
            dependent = GenderDetail(
                male = listOf(
                    CostByAgeRange(
                        minAge = 0,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(150.0)
                    )
                ),
                female = listOf(
                    CostByAgeRange(
                        minAge = 8,
                        maxAge = 18,
                        cost = BigDecimal.valueOf(285.0)
                    )
                )
            )
        ),
        updatedBy: UpdatedBy? = null
    ) = StandardCost(
        id = id,
        companySize = companySize,
        companyBusinessUnit = companyBusinessUnit,
        adhesion = adhesion,
        ansNumber = ansNumber,
        costByPersonDetail = costByPersonDetail,
        updatedBy = updatedBy
    )

    fun buildAliceTestResultFile(
        id: UUID = RangeUUID.generate(),
        personId: PersonId,
    ) = AliceTestResultFile(
        id = id,
        personId = personId,
        referencedModelId = RangeUUID.generate(),
        referencedFileId = RangeUUID.generate().toString(),
        referencedModelClass = "ALICE-TEST-RESULT",
    )

    fun buildServiceScriptExecution(
        triggerId: String,
        triggerType: String,
    ) = ServiceScriptExecution(
        triggerId = triggerId,
        triggerType = triggerType,
        originalConditions = emptyList(),
        triggerConditions = emptyMap(),
        originalActions = emptyList(),
        executedActions = emptyList(),
    )

    fun buildFirstPaymentSchedule(
        id: UUID = RangeUUID.generate(),
        preActivationPaymentId: UUID = RangeUUID.generate(),
        externalId: String? = null,
        companyId: UUID = RangeUUID.generate(),
        companySubcontractId: UUID = RangeUUID.generate(),
        memberInvoiceGroupId: UUID? = null,
        statusHistory: List<FirstPaymentScheduleStatusHistoryEntry> = listOf(
            FirstPaymentScheduleStatusHistoryEntry(
                status = FirstPaymentScheduleStatus.PENDING,
                createdAt = LocalDateTime.now().toString()
            )
        ),
        status: FirstPaymentScheduleStatus = FirstPaymentScheduleStatus.PENDING,
        error: String? = null,
        scheduledDate: LocalDate = LocalDate.now(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FirstPaymentSchedule(
        id = id,
        preActivationPaymentId = preActivationPaymentId,
        companyId = companyId,
        companySubcontractId = companySubcontractId,
        memberInvoiceGroupId = memberInvoiceGroupId,
        externalId = externalId,
        statusHistory = statusHistory,
        status = status,
        error = error,
        scheduledDate = scheduledDate,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildResourceBundleSpecialty(
        id: UUID = RangeUUID.generate(),
        resourceBundleId: UUID = RangeUUID.generate(),
        specialtyId: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        status: Status = Status.ACTIVE,
        appointmentRecommendationLevel: AppointmentRecommendationLevel = AppointmentRecommendationLevel.NONE,
    ) = ResourceBundleSpecialty(
        id = id,
        healthSpecialistResourceBundleId = resourceBundleId,
        medicalSpecialtyId = specialtyId,
        status = status,
        pricingStatus = PricingStatus.PRICED,
        appointmentRecommendationLevel = appointmentRecommendationLevel
    )

    fun buildResourceBundleSpecialtyPricing(
        id: UUID = RangeUUID.generate(),
        resourceBundleSpecialtyId: UUID = RangeUUID.generate(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        prices: List<ResourceBundleSpecialtyPrice> = emptyList(),
    ) = ResourceBundleSpecialtyPricing(
        id = id,
        resourceBundleSpecialtyId = resourceBundleSpecialtyId,
        createdAt = createdAt,
        beginAt = LocalDate.now(),
        prices = prices
    )

    fun buildResourceBundleSpecialtyPricingUpdate(
        failedRowsErrors: List<CSVPricingUpdateError> = emptyList(),
        fileVaultId: UUID = RangeUUID.generate(),
        fileName: String = "file.csv",
        createdByStaffId: UUID = RangeUUID.generate(),
    ) = ResourceBundleSpecialtyPricingUpdate(
        id = RangeUUID.generate(),
        fileName = "file.csv",
        fileVaultId = RangeUUID.generate(),
        createdByStaffId = RangeUUID.generate(),
        processingAt = LocalDateTime.now(),
        completedAt = LocalDateTime.now(),
        rowsCount = 10,
        failedRowsCount = 0,
        failedRowsErrors = failedRowsErrors,
        parsingError = null,
        pricesBeginAt = LocalDate.now()
    )

    fun buildResourceSignToken(
        id: UUID = RangeUUID.generate(),
        signUuid: UUIDv7,
        resourceType: ResourceSignTokenType = ResourceSignTokenType.INVOICE_PAYMENT,
        resourceId: String = "resource-id",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        deletedAt: LocalDateTime? = null,
    ) = ResourceSignToken(
        id = id,
        signUuid = signUuid,
        resourceType = resourceType,
        resourceId = resourceId,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        deletedAt = deletedAt
    )

    fun buildFaqGroupModel(
        id: UUID = RangeUUID.generate(),
        title: String = "Test",
        featured: Boolean = false,
        active: Boolean = true,
        brand: Brand? = Brand.ALICE,
        groupType: FaqGroupType? = null,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FaqGroupModel(
        id = id,
        title = title,
        featured = featured,
        active = active,
        brand = brand,
        groupType = groupType,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildFaqGroup(
        id: UUID = RangeUUID.generate(),
        title: String = "Test",
        featured: Boolean = false,
        active: Boolean = true,
        brand: Brand? = Brand.ALICE,
        groupType: FaqGroupType? = null,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FaqGroup(
        id = id,
        title = title,
        featured = featured,
        active = active,
        brand = brand,
        groupType = groupType,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildFaqContentModel(
        id: UUID = RangeUUID.generate(),
        title: String = "Test",
        description: String? = null,
        groupIds: List<UUID> = emptyList(),
        imageUrl: String? = null,
        active: Boolean = true,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FaqContentModel(
        id = id,
        title = title,
        description = description,
        groupIds = groupIds,
        imageUrl = imageUrl,
        active = active,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildFaqContent(
        id: UUID = RangeUUID.generate(),
        title: String = "Test",
        description: String? = null,
        groupIds: List<UUID> = emptyList(),
        imageUrl: String? = null,
        active: Boolean = true,
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FaqContent(
        id = id,
        title = title,
        description = description,
        groupIds = groupIds,
        imageUrl = imageUrl,
        active = active,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildFaqFeedbackModel(
        id: UUID = RangeUUID.generate(),
        faqContentId: UUID = RangeUUID.generate(),
        useful: Boolean = false,
        personId: PersonId? = PersonId(),
        feedback: String? = "test",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FaqFeedbackModel(
        id = id,
        faqContentId = faqContentId,
        useful = useful,
        personId = personId,
        feedback = feedback,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildFaqFeedback(
        id: UUID = RangeUUID.generate(),
        faqContentId: UUID = RangeUUID.generate(),
        useful: Boolean = false,
        personId: PersonId? = PersonId(),
        feedback: String? = "test",
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) = FaqFeedback(
        id = id,
        faqContentId = faqContentId,
        useful = useful,
        personId = personId,
        feedback = feedback,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt
    )

    fun buildStaffSignupRequest(
        id: UUID = RangeUUID.generate(),
        version: Int = 0,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
        status: StaffSignupRequestStatus = StaffSignupRequestStatus.PENDING,
        rejectionReason: String? = null,
        reviewedBy: UUID? = null,
        staffId: UUID? = null,
        integrationContent: StaffSignupRequestIntegrationContent = StaffSignupRequestIntegrationContent(
            healthProfessional = StaffSignupRequestHealthProfessional(
                fullName = "firstName lastName",
                email = "email",
                nationalId = "nationalId",
                birthdate = "2015-10-10",
                gender = "MALE",
                profileImageUrl = "profileImageUrl",
                profileBio = "profileBio",
                education = "education",
                curiosity = "curiosity",
                councilType = "CRM",
                councilNumber = "123456",
                councilState = "SP",
                specialty = "specialty",
                subSpecialties = emptyList(),
                contacts = emptyList()
            ),
            provider = StaffSignupRequestProvider(
                name = "name",
                cnpj = "cnpj",
                cnes = "cnes",
                bankCode = "bankCode",
                agencyNumber = "agencyNumber",
                accountNumber = "accountNumber",
                phones = emptyList(),
                address = StaffSignupRequestAddress(
                    street = "street",
                    number = "number",
                    complement = "complement",
                    neighborhood = "neighborhood",
                    state = "state",
                    city = "city",
                    zipcode = "zipcode",
                    country = "country"
                )
            )
        ),
        type: StaffSignupRequestType = StaffSignupRequestType.HEALTH_PROFESSIONAL,
        additionalInfo: StaffSignupRequestAdditionalInfo? = null,
    ) = StaffSignupRequest(
        id = id,
        version = version,
        createdAt = createdAt,
        updatedAt = updatedAt,
        status = status,
        rejectionReason = rejectionReason,
        reviewedBy = reviewedBy,
        staffId = staffId,
        integrationContent = integrationContent,
        type = type,
        additionalInfo = additionalInfo
    )

    fun buildHrMemberUploadTracking(
        id: UUID = RangeUUID.generate(),
        companyId: UUID = RangeUUID.generate(),
        companyStaffId: UUID = RangeUUID.generate(),
        uploadId: UUID = RangeUUID.generate(),
        memberNationalId: String = "*********09",
        status: HrMemberUploadTrackingStatus = HrMemberUploadTrackingStatus.QUEUED,
        errors: List<HrMemberUploadTrackingError>? = null,
        reportNotifiedAt: LocalDateTime? = null,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ) = HrMemberUploadTracking(
        id = id,
        memberNationalId = memberNationalId,
        companyId = companyId,
        companyStaffId = companyStaffId,
        status = status,
        createdAt = createdAt,
        updatedAt = updatedAt,
        uploadId = uploadId,
        errors = errors,
        reportNotifiedAt = reportNotifiedAt,
    )
}
