package br.com.alice.moneyin.services.internal.companyinvoicedetailer.xlsx.processors

import br.com.alice.moneyin.services.internal.companyinvoicedetailer.xlsx.XlsxTestHelper
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProcessorHelpersTest : XlsxTestHelper() {

    @Test
    fun `findRowWithText should return correct row index when text is found`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        // Create rows with different content
        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("Header 1")
        row0.createCell(1).setCellValue("Header 2")

        val row1 = sheet.createRow(1)
        row1.createCell(0).setCellValue("Data 1")
        row1.createCell(1).setCellValue("Data 2")

        val row2 = sheet.createRow(2)
        row2.createCell(0).setCellValue("Target Text Here")
        row2.createCell(1).setCellValue("Other Data")

        val row3 = sheet.createRow(3)
        row3.createCell(0).setCellValue("More Data")
        row3.createCell(1).setCellValue("Final Data")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "Target Text")

        // Assert
        assertEquals(2, result)

        workbook.close()
    }

    @Test
    fun `findRowWithText should return correct row index when text is found in different column`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("Header 1")
        row0.createCell(1).setCellValue("Header 2")
        row0.createCell(2).setCellValue("Search Target")

        val row1 = sheet.createRow(1)
        row1.createCell(0).setCellValue("Data 1")
        row1.createCell(1).setCellValue("Data 2")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "Search Target")

        // Assert
        assertEquals(0, result)

        workbook.close()
    }

    @Test
    fun `findRowWithText should return correct row index when text is partial match`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("This contains the search term inside")

        val row1 = sheet.createRow(1)
        row1.createCell(0).setCellValue("Different content")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "search term")

        // Assert
        assertEquals(0, result)

        workbook.close()
    }

    @Test
    fun `findRowWithText should return -1 when text is not found`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("Header 1")
        row0.createCell(1).setCellValue("Header 2")

        val row1 = sheet.createRow(1)
        row1.createCell(0).setCellValue("Data 1")
        row1.createCell(1).setCellValue("Data 2")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "Non-existent text")

        // Assert
        assertEquals(-1, result)

        workbook.close()
    }

    @Test
    fun `findRowWithText should return -1 when sheet is empty`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "Any text")

        // Assert
        assertEquals(-1, result)

        workbook.close()
    }

    @Test
    fun `findRowWithText should handle null rows gracefully`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        // Create row 0 and 2, leaving row 1 as null
        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("First row")

        val row2 = sheet.createRow(2)
        row2.createCell(0).setCellValue("Target text in third row")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "Target text")

        // Assert
        assertEquals(2, result)

        workbook.close()
    }

    @Test
    fun `findRowWithText should handle null cells gracefully`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("First cell")
        // Skip cell 1 (null)
        row0.createCell(2).setCellValue("Target text in third cell")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "Target text")

        // Assert
        assertEquals(0, result)

        workbook.close()
    }

    @Test
    fun `findRowWithText should handle non-string cells gracefully`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue(123.45) // Numeric cell
        row0.createCell(1).setCellValue(true) // Boolean cell
        row0.createCell(2).setCellValue("Target text here")

        // Act
        val result = ProcessorHelpers.findRowWithText(sheet, "Target text")

        // Assert
        assertEquals(0, result)

        workbook.close()
    }

    @Test
    fun `shiftRowsDown should move rows correctly`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        // Create initial rows
        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("Row 0")

        val row1 = sheet.createRow(1)
        row1.createCell(0).setCellValue("Row 1")

        val row2 = sheet.createRow(2)
        row2.createCell(0).setCellValue("Row 2")

        val row3 = sheet.createRow(3)
        row3.createCell(0).setCellValue("Row 3")

        // Act - Shift rows starting from row 1 down by 2 positions
        ProcessorHelpers.shiftRowsDown(sheet, 1, 2)

        // Assert
        // Row 0 should remain unchanged
        assertEquals("Row 0", sheet.getRow(0).getCell(0).stringCellValue)

        // Row 1 and 2 should be empty (null) now
        assertNull(sheet.getRow(1))
        assertNull(sheet.getRow(2))

        // Original row 1 should now be at row 3
        assertEquals("Row 1", sheet.getRow(3).getCell(0).stringCellValue)

        // Original row 2 should now be at row 4
        assertEquals("Row 2", sheet.getRow(4).getCell(0).stringCellValue)

        // Original row 3 should now be at row 5
        assertEquals("Row 3", sheet.getRow(5).getCell(0).stringCellValue)

        workbook.close()
    }

    @Test
    fun `addDataToRow should add data to existing cell`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)
        val existingCell = row.createCell(0)
        existingCell.setCellValue("Original Value")

        // Act
        ProcessorHelpers.addDataToRow(row, 0, "New Value")

        // Assert
        assertEquals("New Value", row.getCell(0).stringCellValue)
        assertEquals(CellType.STRING, row.getCell(0).cellType)

        workbook.close()
    }

    @Test
    fun `addDataToRow should create new cell when cell does not exist`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)

        // Act
        ProcessorHelpers.addDataToRow(row, 2, "New Cell Value")

        // Assert
        assertNotNull(row.getCell(2))
        assertEquals("New Cell Value", row.getCell(2).stringCellValue)
        assertEquals(CellType.STRING, row.getCell(2).cellType)

        workbook.close()
    }

    @Test
    fun `addDataToRow should handle empty string value`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)

        // Act
        ProcessorHelpers.addDataToRow(row, 0, "")

        // Assert
        assertNotNull(row.getCell(0))
        assertEquals("", row.getCell(0).stringCellValue)
        assertEquals(CellType.STRING, row.getCell(0).cellType)

        workbook.close()
    }

    @Test
    fun `addDataToRow should handle special characters and unicode`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)
        val specialText = "Special chars: áéíóú ñ ç @#$%^&*() 中文 🎉"

        // Act
        ProcessorHelpers.addDataToRow(row, 0, specialText)

        // Assert
        assertEquals(specialText, row.getCell(0).stringCellValue)

        workbook.close()
    }

    @Test
    fun `addDataToRow should handle very long text`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)
        val longText = "A".repeat(1000) // 1000 character string

        // Act
        ProcessorHelpers.addDataToRow(row, 0, longText)

        // Assert
        assertEquals(longText, row.getCell(0).stringCellValue)
        assertEquals(1000, row.getCell(0).stringCellValue.length)

        workbook.close()
    }

    @Test
    fun `addDataToRow should overwrite different cell types`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)

        // Create a numeric cell
        val numericCell = row.createCell(0)
        numericCell.setCellValue(123.45)
        assertEquals(CellType.NUMERIC, numericCell.cellType)

        // Act
        ProcessorHelpers.addDataToRow(row, 0, "Text Value")

        // Assert
        assertEquals("Text Value", row.getCell(0).stringCellValue)
        assertEquals(CellType.STRING, row.getCell(0).cellType)

        workbook.close()
    }

    @Test
    fun `addDataToRow should work with multiple columns in same row`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)

        // Act
        ProcessorHelpers.addDataToRow(row, 0, "Column 0")
        ProcessorHelpers.addDataToRow(row, 2, "Column 2")
        ProcessorHelpers.addDataToRow(row, 5, "Column 5")

        // Assert
        assertEquals("Column 0", row.getCell(0).stringCellValue)
        assertNull(row.getCell(1)) // Should remain null
        assertEquals("Column 2", row.getCell(2).stringCellValue)
        assertNull(row.getCell(3)) // Should remain null
        assertNull(row.getCell(4)) // Should remain null
        assertEquals("Column 5", row.getCell(5).stringCellValue)

        workbook.close()
    }

    @Test
    fun `addDataToRow should handle numeric strings correctly`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)

        // Act
        ProcessorHelpers.addDataToRow(row, 0, "123.45")
        ProcessorHelpers.addDataToRow(row, 1, "0")
        ProcessorHelpers.addDataToRow(row, 2, "-999")

        // Assert
        assertEquals("123.45", row.getCell(0).stringCellValue)
        assertEquals("0", row.getCell(1).stringCellValue)
        assertEquals("-999", row.getCell(2).stringCellValue)

        // All should be string type, not numeric
        assertEquals(CellType.STRING, row.getCell(0).cellType)
        assertEquals(CellType.STRING, row.getCell(1).cellType)
        assertEquals(CellType.STRING, row.getCell(2).cellType)

        workbook.close()
    }

    @Test
    fun `addDataToRow should handle whitespace and formatting`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")
        val row = sheet.createRow(0)

        // Act
        ProcessorHelpers.addDataToRow(row, 0, "  Leading spaces")
        ProcessorHelpers.addDataToRow(row, 1, "Trailing spaces  ")
        ProcessorHelpers.addDataToRow(row, 2, "  Both sides  ")
        ProcessorHelpers.addDataToRow(row, 3, "Line\nBreak")
        ProcessorHelpers.addDataToRow(row, 4, "Tab\tCharacter")

        // Assert
        assertEquals("  Leading spaces", row.getCell(0).stringCellValue)
        assertEquals("Trailing spaces  ", row.getCell(1).stringCellValue)
        assertEquals("  Both sides  ", row.getCell(2).stringCellValue)
        assertEquals("Line\nBreak", row.getCell(3).stringCellValue)
        assertEquals("Tab\tCharacter", row.getCell(4).stringCellValue)

        workbook.close()
    }

    @Test
    fun `integration test - findRowWithText and addDataToRow working together`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        // Create initial structure
        val headerRow = sheet.createRow(0)
        headerRow.createCell(0).setCellValue("Name")
        headerRow.createCell(1).setCellValue("Value")

        val dataRow = sheet.createRow(1)
        dataRow.createCell(0).setCellValue("Target Row")
        dataRow.createCell(1).setCellValue("Original")

        // Act
        val targetRowIndex = ProcessorHelpers.findRowWithText(sheet, "Target Row")
        assertTrue(targetRowIndex >= 0)

        val targetRow = sheet.getRow(targetRowIndex)
        ProcessorHelpers.addDataToRow(targetRow, 1, "Updated Value")
        ProcessorHelpers.addDataToRow(targetRow, 2, "New Column")

        // Assert
        assertEquals(1, targetRowIndex)
        assertEquals("Target Row", targetRow.getCell(0).stringCellValue)
        assertEquals("Updated Value", targetRow.getCell(1).stringCellValue)
        assertEquals("New Column", targetRow.getCell(2).stringCellValue)

        workbook.close()
    }

    @Test
    fun `integration test - shiftRowsDown and addDataToRow working together`() {
        // Arrange
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("TestSheet")

        // Create initial rows
        val row0 = sheet.createRow(0)
        row0.createCell(0).setCellValue("Header")

        val row1 = sheet.createRow(1)
        row1.createCell(0).setCellValue("Data 1")

        val row2 = sheet.createRow(2)
        row2.createCell(0).setCellValue("Data 2")

        // Act
        ProcessorHelpers.shiftRowsDown(sheet, 1, 1) // Shift rows 1+ down by 1

        // Add new data in the gap created
        val newRow = sheet.createRow(1)
        ProcessorHelpers.addDataToRow(newRow, 0, "Inserted Row")
        ProcessorHelpers.addDataToRow(newRow, 1, "New Data")

        // Assert
        assertEquals("Header", sheet.getRow(0).getCell(0).stringCellValue)
        assertEquals("Inserted Row", sheet.getRow(1).getCell(0).stringCellValue)
        assertEquals("New Data", sheet.getRow(1).getCell(1).stringCellValue)
        assertEquals("Data 1", sheet.getRow(2).getCell(0).stringCellValue)
        assertEquals("Data 2", sheet.getRow(3).getCell(0).stringCellValue)

        workbook.close()
    }
}
