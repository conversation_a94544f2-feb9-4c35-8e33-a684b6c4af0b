package br.com.alice.healthcondition.routes

import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.healthcondition.controllers.CaseRecordBackfillController
import br.com.alice.healthcondition.controllers.HealthConditionBackfillController
import br.com.alice.healthcondition.controllers.HealthConditionRelatedController
import br.com.alice.healthcondition.controllers.MonitoringTriggerConfigurationController
import br.com.alice.healthcondition.controllers.MonitoringTriggerController
import br.com.alice.healthcondition.controllers.MonitoringTriggerRecordController
import br.com.alice.healthcondition.controllers.PersonCaseBackfillController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route


fun Routing.apiRoutes() {
    val personCaseBackfillController by inject<PersonCaseBackfillController>()
    val caseRecordBackfillController by inject<CaseRecordBackfillController>()
    val healthConditionRelatedController by inject<HealthConditionRelatedController>()
    val healthConditionBackfillController by inject<HealthConditionBackfillController>()

    route("/backfill") {
        post("/update_code_description") {
            asyncLayer {
                coHandler(personCaseBackfillController::updaterCodeDescription)
            }
        }
        post("/inactive_conditions") {
            asyncLayer {
                coHandler(caseRecordBackfillController::inactiveConditions)
            }
        }
        post("/create_person_case") {
            asyncLayer {
                coHandler(caseRecordBackfillController::cretePersonCaseByRecordId)
            }
        }
        post("/update_manager_physician") {
            asyncLayer {
                coHandler(caseRecordBackfillController::setMemberFormerManagerPhysicianOnCaseRecord)
            }
        }
        post("/create_person_case_by_people_case_record") {
            asyncLayer {
                coHandler(caseRecordBackfillController::createMissingPersonCaseByInternalCode)
            }
        }
        post("/create_related") {
            asyncLayer {
                coHandler(healthConditionRelatedController::create)
            }
        }
        post("/update_health_condition_risk_rating") {
            asyncLayer {
                coHandler(healthConditionBackfillController::updateRiskRating)
            }
        }
        post("/update_health_condition_cpt_application_rule") {
            asyncLayer {
                coHandler(healthConditionBackfillController::updateCptApplicationRule)
            }
        }
        post("/upsert_health_condition_template") {
            asyncLayer {
                coHandler(healthConditionBackfillController::upsertTemplate)
            }
        }
        post("/update_health_condition") {
            asyncLayer {
                coHandler(healthConditionBackfillController::updateHealthCondition)
            }
        }
    }

    route("/recurring_subscribers") {
        val monitoringTriggerController by inject<MonitoringTriggerController>()

        post("/process_monitoring_triggers") { coHandler(monitoringTriggerController::process) }

    }

    route("/internal") {
        route("/monitoring_trigger_configuration") {
            val monitoringTriggerConfigurationController by inject<MonitoringTriggerConfigurationController>()

            post("/") { coHandler(monitoringTriggerConfigurationController::create) }
            get("/{id}") { coHandler("id", monitoringTriggerConfigurationController::get) }
            put("/{id}") { coHandler("id", monitoringTriggerConfigurationController::update) }
        }

        route("/monitoring_trigger_record") {
            val monitoringTriggerRecordController by inject<MonitoringTriggerRecordController>()

            post("/") { coHandler(monitoringTriggerRecordController::create) }
        }
    }
}
