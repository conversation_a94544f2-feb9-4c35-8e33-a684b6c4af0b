package br.com.alice.healthcondition

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.channel.ioc.ChannelDomainClientModule
import br.com.alice.client.datagateway.DataGatewayClient
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.communication.whatsapp.aws.WhatsAppSocialMessagingService
import br.com.alice.data.layer.HEALTH_CONDITION_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.CaseRecordDataService
import br.com.alice.data.layer.services.CaseRecordDataServiceClient
import br.com.alice.data.layer.services.HealthConditionAxisDataService
import br.com.alice.data.layer.services.HealthConditionAxisDataServiceClient
import br.com.alice.data.layer.services.HealthConditionDataService
import br.com.alice.data.layer.services.HealthConditionDataServiceClient
import br.com.alice.data.layer.services.HealthConditionRelatedDataService
import br.com.alice.data.layer.services.HealthConditionRelatedDataServiceClient
import br.com.alice.data.layer.services.HealthConditionTemplateDataService
import br.com.alice.data.layer.services.HealthConditionTemplateDataServiceClient
import br.com.alice.data.layer.services.MonitoringTriggerConfigurationDataService
import br.com.alice.data.layer.services.MonitoringTriggerConfigurationDataServiceClient
import br.com.alice.data.layer.services.MonitoringTriggerRecordActionDataService
import br.com.alice.data.layer.services.MonitoringTriggerRecordActionDataServiceClient
import br.com.alice.data.layer.services.MonitoringTriggerRecordDataService
import br.com.alice.data.layer.services.MonitoringTriggerRecordDataServiceClient
import br.com.alice.data.layer.services.PersonCaseDataService
import br.com.alice.data.layer.services.PersonCaseDataServiceClient
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.client.HealthAnswersToCaseRecordService
import br.com.alice.healthcondition.client.HealthConditionAxisService
import br.com.alice.healthcondition.client.HealthConditionRelatedService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.healthcondition.client.HealthConditionTemplateService
import br.com.alice.healthcondition.client.PersonCaseService
import br.com.alice.healthcondition.consumers.AppointmentConsumer
import br.com.alice.healthcondition.consumers.AppointmentScheduleConsumer
import br.com.alice.healthcondition.consumers.ArchivedChannelMonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.BUDConsumer
import br.com.alice.healthcondition.consumers.BackfillConsumer
import br.com.alice.healthcondition.consumers.CaseRecordConsumer
import br.com.alice.healthcondition.consumers.ChannelConsumer
import br.com.alice.healthcondition.consumers.CounterReferralConsumer
import br.com.alice.healthcondition.consumers.CreateChannelMonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.HealthDeclarationFinishedConsumer
import br.com.alice.healthcondition.consumers.LowRiskMemberStratificationConsumer
import br.com.alice.healthcondition.consumers.MonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.OutcomeRecommendationActionEventConsumer
import br.com.alice.healthcondition.consumers.PersonHealthEventConsumer
import br.com.alice.healthcondition.consumers.SendFirstMessageMonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.TertiaryIntentionTouchPointConsumer
import br.com.alice.healthcondition.controllers.CaseRecordBackfillController
import br.com.alice.healthcondition.controllers.HealthConditionBackfillController
import br.com.alice.healthcondition.controllers.HealthConditionRelatedController
import br.com.alice.healthcondition.controllers.MonitoringTriggerConfigurationController
import br.com.alice.healthcondition.controllers.MonitoringTriggerController
import br.com.alice.healthcondition.controllers.MonitoringTriggerRecordController
import br.com.alice.healthcondition.controllers.PersonCaseBackfillController
import br.com.alice.healthcondition.routes.apiRoutes
import br.com.alice.healthcondition.routes.kafkaRoutes
import br.com.alice.healthcondition.services.CaseRecordServiceImpl
import br.com.alice.healthcondition.services.HealthAnswersToCaseRecordServiceImpl
import br.com.alice.healthcondition.services.HealthConditionAxisServiceImpl
import br.com.alice.healthcondition.services.HealthConditionRelatedServiceImpl
import br.com.alice.healthcondition.services.HealthConditionServiceImpl
import br.com.alice.healthcondition.services.HealthConditionTemplateServiceImpl
import br.com.alice.healthcondition.services.MonitoringTriggerConfigurationService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import br.com.alice.healthcondition.services.PersonCaseServiceImpl
import br.com.alice.healthcondition.services.internal.AppointmentCaseRecordService
import br.com.alice.healthcondition.services.internal.CreateChannelMonitoringTriggerService
import br.com.alice.healthcondition.services.internal.HealthDeclarationCaseService
import br.com.alice.healthcondition.services.internal.SearchMonitoringTriggerService
import br.com.alice.healthcondition.services.internal.SendWhatsAppMessageMonitoringService
import br.com.alice.healthcondition.services.whatsapp.EntityAttributeResolverSelector
import br.com.alice.healthcondition.services.whatsapp.EntitySearcherSelector
import br.com.alice.healthcondition.services.whatsapp.PersonEntitySearcher
import br.com.alice.healthcondition.services.whatsapp.PersonNameAttributeResolver
import br.com.alice.healthcondition.services.whatsapp.StaffEntitySearcher
import br.com.alice.healthcondition.services.whatsapp.StaffNameAttributeResolver
import br.com.alice.healthcondition.services.whatsapp.WhatsAppParameterLoader
import br.com.alice.healthcondition.services.whatsapp.model.AvailableEntity
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.questionnaire.ioc.QuestionnaireDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.netty.EngineMain
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.socialmessaging.SocialMessagingAsyncClient

fun main(args: Array<String>): Unit = EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        KafkaProducerModule,
        MembershipClientModule,
        QuestionnaireDomainClientModule,
        HealthPlanDomainClientModule,
        StaffDomainClientModule,
        ClinicalAccountDomainClientModule,
        ChannelDomainClientModule,
        PersonDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }
            single<Invoker> { DataLayerClientConfiguration.build() }

            //Health Controller
            single { HealthController(SERVICE_NAME) }

            //Load services
            loadServiceServers("br.com.alice.healthcondition.services")

            // Data Services
            single<CaseRecordDataService> { CaseRecordDataServiceClient(get()) }
            single<HealthConditionDataService> { HealthConditionDataServiceClient(get()) }
            single<HealthConditionAxisDataService> { HealthConditionAxisDataServiceClient(get()) }
            single<HealthConditionRelatedDataService> { HealthConditionRelatedDataServiceClient(get()) }
            single<HealthConditionTemplateDataService> { HealthConditionTemplateDataServiceClient(get()) }
            single<PersonCaseDataService> { PersonCaseDataServiceClient(get()) }
            single<MonitoringTriggerConfigurationDataService> { MonitoringTriggerConfigurationDataServiceClient(get()) }
            single<MonitoringTriggerRecordDataService> { MonitoringTriggerRecordDataServiceClient(get()) }
            single<MonitoringTriggerRecordActionDataService> { MonitoringTriggerRecordActionDataServiceClient(get()) }

            // Clients
            single { DataGatewayClient() }

            //Internal Services
            single { HealthDeclarationCaseService(get(), get()) }
            single { AppointmentCaseRecordService(get(), get()) }
            single { SearchMonitoringTriggerService(get(), get(), get(), get(), get(), get(), get()) }
            single { MonitoringTriggerRecordService(get()) }
            single { MonitoringTriggerConfigurationService(get()) }
            single { MonitoringTriggerRecordActionService(get(), get()) }
            single { CreateChannelMonitoringTriggerService(get(), get(), get(), get(), get()) }
            single { PersonEntitySearcher(get()) }
            single { StaffEntitySearcher(get()) }
            single {
                EntitySearcherSelector(
                    mapOf(
                        AvailableEntity.PERSON to get<PersonEntitySearcher>(),
                        AvailableEntity.STAFF to get<StaffEntitySearcher>()
                    )
                )
            }
            single {
                EntityAttributeResolverSelector(
                    mapOf(
                        "STAFF::name" to StaffNameAttributeResolver(),
                        "PERSON::name" to PersonNameAttributeResolver()
                    )
                )
            }
            single {
                SocialMessagingAsyncClient.builder()
                    .region(Region.of(ServiceConfig.Aws.SocialMessaging.region))
                    .build()
            }
            single { WhatsAppSocialMessagingService(get()) }
            single { WhatsAppParameterLoader(get(), get()) }
            single { SendWhatsAppMessageMonitoringService(get(), get(), get()) }

            //Consumers
            single { HealthDeclarationFinishedConsumer(get()) }
            single { AppointmentScheduleConsumer(get()) }
            single { CaseRecordConsumer(get<PersonCaseService>() as PersonCaseServiceImpl) }
            single { LowRiskMemberStratificationConsumer(get(), get()) }
            single { PersonHealthEventConsumer(get()) }
            single { AppointmentConsumer(get()) }
            single { ChannelConsumer(get<PersonCaseService>() as PersonCaseServiceImpl, get()) }
            single { CounterReferralConsumer(get(), get(), get()) }
            single { OutcomeRecommendationActionEventConsumer(get()) }
            single { BUDConsumer(get(), get(), get()) }
            single { TertiaryIntentionTouchPointConsumer(get(), get()) }
            single { BackfillConsumer(get()) }
            single { MonitoringTriggerConsumer(get()) }
            single { CreateChannelMonitoringTriggerConsumer(get(), get(), get()) }
            single { ArchivedChannelMonitoringTriggerConsumer(get(), get()) }
            single { SendFirstMessageMonitoringTriggerConsumer(get(), get(), get(), get()) }

            // Exposed Services
            single<CaseRecordService> { CaseRecordServiceImpl(get(), get(), get(), get()) }
            single<HealthConditionService> { HealthConditionServiceImpl(get(), get(), get()) }
            single<HealthConditionTemplateService> { HealthConditionTemplateServiceImpl(get()) }
            single<HealthConditionAxisService> { HealthConditionAxisServiceImpl(get()) }
            single<HealthConditionRelatedService> { HealthConditionRelatedServiceImpl(get()) }
            single<HealthAnswersToCaseRecordService> { HealthAnswersToCaseRecordServiceImpl(get(), get()) }
            single<PersonCaseService> { PersonCaseServiceImpl(get(), get()) }

            // Controllers
            single { PersonCaseBackfillController(get<PersonCaseService>() as PersonCaseServiceImpl, get()) }
            single { CaseRecordBackfillController(get<PersonCaseService>() as PersonCaseServiceImpl, get(), get()) }
            single { HealthConditionRelatedController(get(), get()) }
            single { HealthConditionBackfillController(get(), get(), get()) }
            single { MonitoringTriggerController(get()) }
            single { MonitoringTriggerConfigurationController(get()) }
            single { MonitoringTriggerRecordController(get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
    startRoutesSync: Boolean = true,
) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, HEALTH_CONDITION_DOMAIN_ROOT_SERVICE_NAME)
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(
            FeatureNamespace.EHR,
            FeatureNamespace.HEALTH_LOGICS,
            FeatureNamespace.HEALTH_CONDITION
        )
    }
}
