package br.com.alice.healthcondition.controllers

import br.com.alice.common.Response
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.data.layer.models.MonitoringTriggerRecordExtraData
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import java.time.LocalDateTime
import java.util.UUID

class MonitoringTriggerRecordController(
    private val monitoringTriggerRecordService: MonitoringTriggerRecordService,
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService
): HealthConditionController() {

    suspend fun create(request: MonitoringTriggerRecordRequest): Response =
        withHealthConditionEnvironment {
            val monitoringTriggerRecord = MonitoringTriggerRecord(
                personId = request.personId.toPersonId(),
                triggerId = request.triggerId,
                staffIds = request.staffIds,
                externalTriggerId = request.externalTriggerId,
                identifiedAt = LocalDateTime.now(),
                status = MonitoringTriggerRecordStatus.CREATED,
                currentStep = request.currentStep,
                extraData = request.extraData
            )

            monitoringTriggerRecordService.addAll(listOf(monitoringTriggerRecord))
                .then { monitoringList ->
                    monitoringTriggerRecordActionService.createAll(
                        records = monitoringList,
                        action = MonitoringTriggerRecordActionType.NEW_TRIGGER_IDENTIFIED
                    )
                }
                .foldResponse()
        }

    data class MonitoringTriggerRecordRequest(
        val personId: UUID,
        val triggerId: UUID,
        val staffIds: List<UUID>,
        val externalTriggerId: String,
        val currentStep: MonitoringTriggerFeatureType,
        val extraData: MonitoringTriggerRecordExtraData? = null
    )
}
