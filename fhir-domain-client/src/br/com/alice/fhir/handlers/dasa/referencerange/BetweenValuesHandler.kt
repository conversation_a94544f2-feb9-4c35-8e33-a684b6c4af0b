package br.com.alice.fhir.handlers.dasa.referencerange

import br.com.alice.common.core.extensions.toDotDouble
import br.com.alice.data.layer.models.ReferenceRange
import org.hl7.fhir.r4.model.Observation.ObservationReferenceRangeComponent

class BetweenValuesHandler(
    private val nextHandler: ReferenceRangeHandler? = null
) : ReferenceRangeHandler {

    override fun parseResult(
        referenceRange: List<ObservationReferenceRangeComponent>?, resultValue: String?
    ): ReferenceRange? {
        return try {


            val referenceRangeValue = if (referenceRange.isNullOrEmpty()) null else referenceRange.first()
            val hasLow = referenceRangeValue?.hasLow() ?: false
            val hasHigh = referenceRangeValue?.hasHigh() ?: false

            if (!hasLow || !hasHigh || resultValue.isNullOrEmpty())
                return nextHandler?.parseResult(referenceRange, resultValue)



            val result = resultValue.replace("(\\||${referenceRangeValue?.low?.unit ?: ""})".toRegex(), "")
                .toDotDouble()
            val lowValue = referenceRangeValue?.low?.value?.toDouble() ?: Double.MIN_VALUE
            val highValue = referenceRangeValue?.high?.value?.toDouble() ?: Double.MAX_VALUE

            return when {
                result < lowValue -> ReferenceRange.LOWER_REFERENCE_LIMIT
                result in lowValue..highValue -> ReferenceRange.WITHIN_NORMAL_LIMIT
                else -> ReferenceRange.UPPER_REFERENCE_LIMIT
            }

        } catch (ex: Throwable) {
            nextHandler?.parseResult(referenceRange, resultValue)
        }
    }
}
