package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.converters.ServiceScriptConverter
import br.com.alice.api.ops.models.ScriptRelationshipWithConditionsRequest
import br.com.alice.common.Converter
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.plusSafe
import br.com.alice.common.core.extensions.snakeToCamelCase
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.map
import br.com.alice.data.layer.models.Answer
import br.com.alice.data.layer.models.BudNode
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ExternalSource
import br.com.alice.data.layer.models.Option
import br.com.alice.data.layer.models.OptionType
import br.com.alice.data.layer.models.ScriptAction
import br.com.alice.data.layer.models.ScriptActionType
import br.com.alice.data.layer.models.SelectionType
import br.com.alice.data.layer.models.ServiceScriptAction
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType.HEALTH_PLAN_TASK_GROUP_TEMPLATE
import br.com.alice.data.layer.models.ServiceScriptAction.ActionType.HEALTH_PLAN_TASK_TEMPLATE
import br.com.alice.data.layer.models.ServiceScriptNode
import br.com.alice.data.layer.models.ServiceScriptRelationship
import br.com.alice.data.layer.models.ServiceScriptStatus
import br.com.alice.healthlogic.client.BudAdministrationService
import br.com.alice.healthlogic.client.HealthLogicsService
import br.com.alice.healthlogic.models.bud.OrderBy
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders.ContentRange
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.Parameters
import java.util.UUID


class ServiceScriptController(
    private val budAdministrationService: BudAdministrationService,
    private val healthLogicsService: HealthLogicsService,
) : Controller() {

    suspend fun index(queryParams: Parameters): Response {
        val ids = parseIds(queryParams)
        val filterType = parseType(queryParams)?.let { BudNode.BudNodeType.valueOf(it) }
        val orderBy = parseOrder(queryParams)
        val filterQuery = parseQuery(queryParams)
        val range = parseRange(queryParams)

        val (nodes, totalCount) = budAdministrationService
            .getNodes(
                ids = ids?.map { it.toUUID() } ?: emptyList(),
                name = filterQuery,
                type = filterType,
                orderBy = orderBy,
                range = range
            ).get()

        return Response(OK, nodes, mapOf(ContentRange to totalCount.toString()))
    }

    suspend fun getById(nodeId: String) =
        budAdministrationService.getNode(UUID.fromString(nodeId))
            .flatMapPair {
                if (it.serviceScriptActionIds.isNotEmpty()) {
                    healthLogicsService.getActions(it.serviceScriptActionIds)
                } else emptyList<ServiceScriptAction>().success()
            }.map { (actions, node) ->
                val actionsByType = getActionsIdsByType(actions)
                ScriptNodeResponseConverter.convert(node, actionsByType.first, actionsByType.second)
            }
            .coFoldResponse(
                { it },
                NotFoundException::class to { ErrorResponse("service_script_node_not_found") }
            )

    suspend fun create(request: ScriptNodeRequest): Response {
        request.validateSize()
        val actions = getActions(request)

        return budAdministrationService.createNode(
            BudNodeConverter.convert(
                source = request,
                serviceScriptActions = actions
            )
        ).flatMapPair {
            logger.info("Added BudNode", "current_staff_id" to currentUserIdKey(), "model" to it)
            if (it.serviceScriptActionIds.isNotEmpty()) {
                healthLogicsService.getActions(it.serviceScriptActionIds)
            } else emptyList<ServiceScriptAction>().success()
        }.map { (actions, node) ->
            val actionsByType = getActionsIdsByType(actions)
            ScriptNodeResponseConverter.convert(node, actionsByType.first, actionsByType.second)
        }.foldResponse()
    }

    private suspend fun getActions(request: ScriptNodeRequest): List<ServiceScriptAction> =
        getActionsByTaskGroupTemplateIds(request.healthPlanTaskGroupTemplateIds)
            .plusSafe(getActionsByTaskTemplateIds(request.healthPlanTaskTemplateIds))

    private suspend fun getActionsByTaskGroupTemplateIds(healthPlanTaskGroupTemplateIds: List<UUID>?): List<ServiceScriptAction> =
        healthPlanTaskGroupTemplateIds?.let { templateIds ->
            templateIds.pmap {
                healthLogicsService.getOrCreateAction(HEALTH_PLAN_TASK_GROUP_TEMPLATE, it).get()
            }
        } ?: emptyList()

    private suspend fun getActionsByTaskTemplateIds(healthPlanTaskTemplateIds: List<UUID>?): List<ServiceScriptAction> =
        healthPlanTaskTemplateIds?.let { templateIds ->
            templateIds.pmap {
                healthLogicsService.getOrCreateAction(HEALTH_PLAN_TASK_TEMPLATE, it).get()
            }
        } ?: emptyList()

    suspend fun update(nodeId: String, request: ScriptNodeRequest): Response {
        request.validateSize()
        val actions = getActions(request)
        val current = budAdministrationService.getNode(nodeId.toUUID()).get()
        val new = BudNodeConverter.convert(
            source = request,
            id = current.id,
            serviceScriptActions = actions
        )
        val toUpdate = current.copy(
            name = new.name,
            content = new.content,
            actions = new.actions,
            serviceScriptActionIds = new.serviceScriptActionIds,
            internalOrientation = new.internalOrientation,
            type = new.type,
            status = new.status,
            privateOrientation = new.privateOrientation,
            rootNodeId = new.rootNodeId,
            memberFriendlyContent = new.memberFriendlyContent,
            memberFriendlySubContent = new.memberFriendlySubContent,
            option = new.option,
            externalSource = new.externalSource

        )
        return budAdministrationService.updateNode(toUpdate)
            .flatMapPair {
                logger.info("Updated BudNode", "current_staff_id" to currentUserIdKey(), "model" to it)
                if (it.serviceScriptActionIds.isNotEmpty()) {
                    healthLogicsService.getActions(it.serviceScriptActionIds)
                } else emptyList<ServiceScriptAction>().success()
            }.map { (actions, node) ->
                val actionsByType = getActionsIdsByType(actions)
                ScriptNodeResponseConverter.convert(
                    node,
                    actionsByType.first,
                    actionsByType.second
                )
            }.foldResponse()
    }

    private fun getActionsIdsByType(actions: List<ServiceScriptAction>): Pair<List<UUID>, List<UUID>> =
        actions.filter { it.type == HEALTH_PLAN_TASK_TEMPLATE }.map { it.externalId } to
                actions.filter { it.type == HEALTH_PLAN_TASK_GROUP_TEMPLATE }.map { it.externalId }

    suspend fun indexRelationship(queryParams: Parameters): Response {
        val range = parseRange(queryParams)
        val orderBy = parseOrder(queryParams)
        val filterStatus = parseStatus(queryParams)?.let { ServiceScriptStatus.valueOf(it) }
        val filterQuery = parseQuery(queryParams)

        val (relationships, totalCount) = budAdministrationService
            .getRelationships(
                name = filterQuery,
                status = filterStatus,
                orderBy = orderBy,
                range = range
            ).get()


        val relationshipToResponse = relationships.pmap { ServiceScriptConverter.toResponse(it) }
        return Response(OK, relationshipToResponse, mapOf(ContentRange to totalCount.toString()))
    }

    suspend fun getRelationshipById(relationshipId: String) =
        budAdministrationService.getRelationship(relationshipId.toUUID()).coFoldResponse(
            { ServiceScriptConverter.toResponse(it) },
            NotFoundException::class to { ErrorResponse("service_script_relationship_not_found") }
        )

    suspend fun createRelationship(request: ScriptRelationshipWithConditionsRequest): Response {
        val relationship = ServiceScriptConverter.toInternal(request)

        return budAdministrationService.createRelationship(relationship)
            .then {
                logger.info(
                    "Added ServiceScriptRelationship",
                    "current_staff_id" to currentUserIdKey(),
                    "model" to it
                )
            }.coFoldResponse(
                { ServiceScriptConverter.toResponse(it) },
                InvalidArgumentException::class to { ErrorResponse("service_script_relationship_invalid") },
                Exception::class to { ErrorResponse("service_script_relationship_not_created") }
            )
    }

    suspend fun updateRelationship(relationshipId: String, request: ScriptRelationshipWithConditionsRequest): Response {
        val relationship = budAdministrationService.getRelationship(relationshipId.toUUID()).get()
        val relationshipCopy = relationship.copy(
            nodeParentId = request.nodeParentId.toSafeUUID(),
            nodeChildId = request.nodeChildId.toSafeUUID(),
            name = request.name,
            status = request.status,
            conditions = ServiceScriptConverter.buildConditions(request),
            priority = request.priority
        )

        return budAdministrationService.updateRelationship(relationshipCopy)
            .then {
                logger.info(
                    "Updated ServiceScriptRelationship",
                    "current_staff_id" to currentUserIdKey(),
                    "model" to it
                )
            }.coFoldResponse(
                { ServiceScriptConverter.toResponse(it) },
                InvalidArgumentException::class to { ErrorResponse("service_script_relationship_invalid") },
                NotFoundException::class to { ErrorResponse("service_script_relationship_not_found") }
            )
    }

    private fun parseOrder(queryParams: Parameters) =
        parseSort(queryParams)?.let { OrderBy(it.first, it.second) } ?: OrderBy()
}

private const val contentLengthLimit = 2_000_000
data class ScriptNodeRequest(
    val name: String,
    val internalOrientation: String? = null,
    val content: String,
    val type: BudNode.BudNodeType,
    val status: ServiceScriptStatus,
    val privateOrientation: Boolean = false,
    val actions: List<ScriptActionRequest>? = null,
    val healthPlanTaskGroupTemplateIds: List<UUID>? = emptyList(),
    val healthPlanTaskTemplateIds: List<UUID>? = emptyList(),
    val rootNodeId: UUID? = null,
    val memberFriendlyContent: String? = null,
    val memberFriendlySubContent: String? = null,
    val option: OptionRequest? = null,
    val externalSource: ExternalSource? = null
) {
    fun validateSize() {
        if (content.length > contentLengthLimit
            || (internalOrientation != null && internalOrientation.length > contentLengthLimit)
        ) throw InvalidArgumentException(code = "content_length_exceeded", message = "content length has exceeded")
    }
}

data class OptionRequest(
    val selectionType: SelectionType,
    val type: OptionType,
    val answers: List<AnswerRequest> = emptyList()
)

data class AnswerRequest(
    val title: String,
    val externalId: UUID? = null,
    val value: Any? = null,
    val id: UUID? = RangeUUID.generate()
)

data class ScriptNodeResponse(
    val id: UUID,
    val name: String,
    val internalOrientation: String? = null,
    val content: String,
    val type: BudNode.BudNodeType,
    val status: ServiceScriptStatus,
    val privateOrientation: Boolean = false,
    val actions: List<ScriptAction> = emptyList(),
    val healthPlanTaskGroupTemplateIds: List<UUID>? = emptyList(),
    val healthPlanTaskTemplateIds: List<UUID>? = emptyList(),
    val rootNodeId: UUID? = null,
    val memberFriendlyContent: String? = null,
    val memberFriendlySubContent: String? = null,
    val option: Option? = null,
    val externalSource: ExternalSource? = null
)

data class ScriptNodeWithRelationshipsResponse(
    val node: ServiceScriptNode,
    val relationships: List<ServiceScriptRelationship>,
)

data class ScriptActionRequest(
    val id: UUID? = null,
    val type: ScriptActionType,
    val content: Map<String, Any?>,
)

data class ScriptRelationshipRequest(
    val name: String,
    val nodeParentId: String,
    val nodeChildId: String,
    val status: ServiceScriptStatus,
    val conditions: List<Condition>? = emptyList(),
)

data class ServiceScriptActionEnrichedResponse(
    val id: UUID,
    val type: ServiceScriptAction.ActionType,
    val externalId: UUID,
    val action: Any
)

private object BudNodeConverter : Converter<ScriptNodeRequest, BudNode>(
    ScriptNodeRequest::class, BudNode::class
) {
    fun convert(
        source: ScriptNodeRequest,
        id: UUID? = null,
        serviceScriptActions: List<ServiceScriptAction> = emptyList()
    ) =
        convert(
            source,
            map(BudNode::id) from (id ?: RangeUUID.generate()),
            map(BudNode::serviceScriptActionIds) from (serviceScriptActions.map { it.id }),
            map(BudNode::actions) from (source.actions?.map {
                ScriptAction(
                    id = it.id ?: RangeUUID.generate(),
                    type = it.type,
                    content = covertQuestionnaireToCamelCase(source.type, it.content)
                )
            } ?: emptyList()),
            map(BudNode::option) from (
                    source.option?.let { option ->
                    Option(
                        selectionType = option.selectionType,
                        type = option.type,
                        answers = option.answers.map { answer ->
                            Answer(
                                title = answer.title,
                                id = answer.id ?: RangeUUID.generate(),
                                externalId = answer.externalId,
                                value = answer.value
                            )
                        }
                    )
                }
            ),
        )

    private fun covertQuestionnaireToCamelCase(type: BudNode.BudNodeType, content: Map<String, Any?>) =
        content.takeIf { type == BudNode.BudNodeType.QUESTION }
            ?.map { (key, value) -> key.snakeToCamelCase() to value }?.toMap() ?: content
}

private object OptionConverter : Converter<OptionRequest, Option>(
    OptionRequest::class, Option::class
) {
    fun convert(
        source: OptionRequest
    ) =
        convert(
            source,
            map(Option::answers) from (
                    source.answers.map {
                        Answer(
                            id = it.id ?: RangeUUID.generate(),
                            title = it.title,
                            externalId = it.externalId,
                            value = it.value
                        )
                    }
            )
        )
}

private object ScriptNodeResponseConverter : Converter<BudNode, ScriptNodeResponse>(
    BudNode::class, ScriptNodeResponse::class
) {
    fun convert(
        source: BudNode,
        healthPlanTaskTemplateIds: List<UUID> = emptyList(),
        healthPlanTaskGroupTemplateIds: List<UUID> = emptyList()
    ) =
        convert(
            source,
            map(ScriptNodeResponse::healthPlanTaskTemplateIds) from healthPlanTaskTemplateIds,
            map(ScriptNodeResponse::healthPlanTaskGroupTemplateIds) from healthPlanTaskGroupTemplateIds
        )
}
