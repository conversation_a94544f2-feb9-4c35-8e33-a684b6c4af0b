package br.com.alice.nullvs.services.internals.members.b2c

import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.converters.NullvsMemberConverter
import br.com.alice.nullvs.logics.NullvsIntegrationRecordLogic.totvsBeneficiaryRegistryTofamilyCode
import br.com.alice.nullvs.logics.TotvsRequestLogic
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.nullvs.services.internals.members.CancelMemberTotvs
import br.com.alice.nullvs.services.internals.members.LogGenerator
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class B2CMemberTotvsCanceller(
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val personService: PersonService,
    private val logGenerator: LogGenerator,
) :
    CancelMemberTotvs(nullvsIntegrationRecordService, personService, logGenerator) {
    override suspend fun mountBeneficiary(payload: MountBeneficiaryPayload): Result<NullvsMemberBatchRequest, Throwable> {
        val member = payload.member
        val person = payload.person
        val nullvsIntegrationRecord = payload.nullvsIntegrationRecord!!
        val meta = payload.meta

        val idTotvs = nullvsIntegrationRecord.externalId

        val groupCompany = TotvsRequestLogic.getB2CGroupCompany(person, member)

        val action = NullvsActionType.CANCEL

        val beneficiaryTotvs =
            NullvsMemberConverter.generateTotvsBeneficiary(
                NullvsMemberConverter.GenerateTotvsBeneficiaryPayload(
                    person,
                    member,
                    ansNumber = null,
                    idTotvs = idTotvs,
                    action = action,
                    relationType = null,
                )
            )

        val familyCode = totvsBeneficiaryRegistryTofamilyCode(idTotvs).get()

        val totvsMemberRequest =
            TotvsMemberRequest(
                company = groupCompany,
                ANSProductId = null,
                idPayload = 1,
                createdAt = member.activationDate ?: member.createdAt,
                beneficiaries = listOf(beneficiaryTotvs),
                familyTotvsCode = familyCode
            )

        return NullvsMemberBatchRequest(
            metadata = meta,
            action = action,
            totvsMemberRequest = totvsMemberRequest,
        ).success()
    }
}
