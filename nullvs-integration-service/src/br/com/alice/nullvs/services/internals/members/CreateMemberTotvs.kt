package br.com.alice.nullvs.services.internals.members

import br.com.alice.common.extensions.coFlatMapError
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.exceptions.NullANSFieldException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.services.internals.AnsNumberCacheService
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

abstract class CreateMemberTotvs(
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val personService: PersonService,
    private val logGenerator: LogGenerator,
    private val personValidationService: PersonValidationService,
    private val ansNumberCacheService: AnsNumberCacheService,
) : MemberTotvs(
    nullvsIntegrationRecordService,
    personService,
) {
    override suspend fun execute(meta: Meta, member: Member): Result<NullvsMemberBatchRequest, Throwable> =
        coResultOf {
            coroutineScope {
                val ansNumberDef = async { ansNumberCacheService.getByProductId(member.productId) }
                val personDef = async { getPerson(member.personId).get() }

                val person = personDef.await()
                val ansNumber = ansNumberDef.await()
                val action = NullvsActionType.CREATE

                validateBeneficiary(member.productId, ansNumber, person, action)
                    .coFlatMapError { personValidationService.updateWhenAddressExceptionHappens(it, person, action) }
                    .flatMap {
                        logGenerator.log(meta, member, "Beneficiary - ${member.productType}") {
                            mountBeneficiary(
                                MountBeneficiaryPayload(
                                    meta,
                                    person = it,
                                    member,
                                    nullvsIntegrationRecord = null,
                                    ansNumber = ansNumber
                                )
                            )
                        }
                    }.get()
            }
        }

    private fun validateBeneficiary(productId: UUID, ansNumber: String?, person: Person, action: NullvsActionType) =
        resultOf<Person, Throwable> {
            if (ansNumber == null) throw NullANSFieldException(productId)
            personValidationService.check(person, action).get()
        }.thenError {
            logger.info(
                "Something was wrong when trying to validate the beneficiary",
                "message" to it.message,
                "exception" to it,
            )
        }
}
