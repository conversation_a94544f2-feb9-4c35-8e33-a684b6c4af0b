package br.com.alice.nullvs.consumers.company

import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.CompanyStatus
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.CompanySubContractIntegrationStatus
import br.com.alice.data.layer.models.CompanySubContractStatus
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.LogStatus
import br.com.alice.data.layer.models.NullvsIntegrationLog
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.event.InvoiceItemCreatedEvent
import br.com.alice.nullvs.consumers.Consumer
import br.com.alice.nullvs.converters.NullvsCompanyConverter.toNullvsIntegrationRecord
import br.com.alice.nullvs.events.NullvsCompanySubContractActivatedEvent
import br.com.alice.nullvs.events.NullvsCompanySubcontractWebhookReceivedEvent
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Method
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.Status
import br.com.alice.nullvs.metrics.TotvsIntegrationMetric.countNullvsWebhookConsumer
import br.com.alice.nullvs.models.TotvsStatus
import br.com.alice.nullvs.models.TotvsStatus.Companion.toNullvsLog
import br.com.alice.nullvs.models.company.NullvsCompanySubcontractWebhookReceived
import br.com.alice.nullvs.services.internals.NullvsIntegrationLogService
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class NullvsCompanySubcontractWebhookReceivedConsumer(
    private val nullvsIntegrationLogService: NullvsIntegrationLogService,
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val companySubcontractService: CompanySubContractService,
    private val invoiceItemService: InvoiceItemService,
    private val kafkaProducerService: KafkaProducerService,
    private val companyService: CompanyService,
) : Consumer() {

    suspend fun processedAtTotvs(event: NullvsCompanySubcontractWebhookReceivedEvent) =
        withSubscribersEnvironment {
            val response = event.payload.response

            logger.info(
                "Consume the NullvsCompanySubcontractWebhookReceivedEvent message",
                "message_event_id" to event.messageId,
                "event_date" to event.eventDate,
                "nullvs_webhook_response" to response,
                "inherited_auth_token" to event.inheritedAuthToken,
                "inherited_auth_token_is_null" to (event.inheritedAuthToken == null)
            )

            nullvsIntegrationLogService.findByBatchIdSocAndPayloadId(
                response.batchId,
                response.idSoc,
                1
            )
                .coFoldNotFound {
                    logger.info(
                        "NullvsCompanySubcontractWebhookReceivedEvent.processedAtTotvs: Creating the log when it is not found",
                        "message_event_id" to event.messageId,
                        "event_date" to event.eventDate,
                    )

                    nullvsIntegrationLogService.add(
                        NullvsIntegrationLog(
                            eventId = event.messageId,
                            eventName = event.name,
                            integrationEventName = event.name,
                            internalId = response.internalId!!,
                            internalModelName = InternalModelType.SUBCONTRACT,
                            externalModelName = ExternalModelType.SUBCONTRACT,
                            batchId = response.batchId,
                            idSoc = response.idSoc,
                            batchType = response.type,
                            payloadSequenceId = 1,
                            description = response.description,
                            status = response.status.toNullvsLog(),
                        )
                    )
                }
                .flatMap {
                    logger.info("found the integration log", "model" to it)
                    when (it.batchType) {
                        BatchType.CREATE -> createRecord(response, it)
                        BatchType.CANCEL -> cancelRecord(response, it)
                        BatchType.UPDATE -> updateRecord(response, it)
                        else -> Exception("TODO").failure()
                    }
                }.then {
                    logger.info("The ${it.batchType} integration worked", "nullvs_log_id" to it.id)
                    countNullvsWebhookConsumer(Method.SUBCONTRACT, Status.SUCCESS)
                }
                .thenError {
                    countNullvsWebhookConsumer(Method.SUBCONTRACT, Status.FAILURE)
                    logger.info(
                        "NullvsCompanySubcontractWebhookReceivedConsumer:: Error on consumingSubcontract subcontract webhook",
                        "error_message" to response.errorMessage,
                        "batch_id" to response.batchId,
                        "id_soc," to response.idSoc,
                        "action_type" to response.type
                    )
                    throw it
                }
        }

    private suspend fun createRecord(response: NullvsCompanySubcontractWebhookReceived, log: NullvsIntegrationLog) =
        when (response.status) {
            TotvsStatus.SUCCESS ->
                getAndUpdateContractAndCompany(response)
                    .flatMap { nullvsIntegrationRecordService.add(response.toNullvsIntegrationRecord(log.id)) }
                    .then { logger.info("The nullvs record is registered", "record_id" to it.id) }
                    .flatMap { nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED)) }
                    .then {
                        kafkaProducerService.produce(
                            NullvsCompanySubContractActivatedEvent(
                                response.internalId!!,
                                response.externalId!!,
                                response.groupCompany!!.code,
                                response.contractNumber!!
                            )
                        )
                    }

            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = response.errorMessage,
                )
            ).andThen {
                val internalId = response.internalId?.let { response.internalId } ?: log.internalId
                logger.info("Updating subcontract status to FAILED", "subcontract_id" to internalId)
                updateSubcontractStatus(internalId, CompanySubContractIntegrationStatus.FAILED)
            }
        }

    private suspend fun cancelRecord(response: NullvsCompanySubcontractWebhookReceived, log: NullvsIntegrationLog) =
        when (response.status) {
            TotvsStatus.SUCCESS ->
                nullvsIntegrationRecordService.findByInternalIdAndModel(log.internalId, InternalModelType.SUBCONTRACT)
                    .flatMap { nullvsIntegrationRecordService.update(it.copy(canceledAt = LocalDateTime.now())) }
                    .flatMap { nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED)) }
                    .map { it }

            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = response.errorMessage,
                )
            )
        }


    private suspend fun updateRecord(response: NullvsCompanySubcontractWebhookReceived, log: NullvsIntegrationLog) =
        when (response.status) {
            TotvsStatus.SUCCESS -> nullvsIntegrationLogService.update(log.copy(status = LogStatus.FINISHED))
            TotvsStatus.FAILURE -> nullvsIntegrationLogService.update(
                log.copy(
                    status = LogStatus.FAILURE,
                    description = response.errorMessage,
                )
            )
        }

    private suspend fun getAndUpdateContractAndCompany(response: NullvsCompanySubcontractWebhookReceived) =
        coroutineScope {
            companySubcontractService.get(response.internalId!!)
                .map {
                    logger.info(
                        "The subcontract is found",
                        "subcontract_id" to it.id,
                        "subcontract_title" to it.title,
                        "totvs_subcontract_number" to response.externalId,
                    )
                    it.copy(
                        externalId = response.externalId,
                        status = CompanySubContractStatus.ACTIVE,
                        integrationStatus = CompanySubContractIntegrationStatus.PROCESSED,
                    )
                }
                .flatMap {
                    val companyDef = async { updateCompanyStatusBySubcontract(it, CompanyStatus.ACTIVE) }

                    val subcontractUpdatedDef = async { companySubcontractService.update(it, false) }

                    companyDef.await()
                    subcontractUpdatedDef.await()
                }
                .then { logger.info("The subcontract is updated") }
                .then { sendPendingInvoiceItems(it) }
                .thenError {
                    logger.info(
                        "Something is wrong when tried to update the subcontract",
                        "subcontract_id" to response.internalId,
                        "ex" to it
                    )
                }
        }

    private suspend fun updateSubcontractStatus(
        subcontractId: UUID,
        status: CompanySubContractIntegrationStatus
    ) = companySubcontractService.get(subcontractId)
        .flatMap { companySubcontractService.update(it.copy(integrationStatus = status), false) }

    private suspend fun sendPendingInvoiceItems(subcontract: CompanySubContract) {
        val invoiceItems =
            invoiceItemService.listInvoiceItemsByCompanyAndSubcontract(subcontract.companyId, subcontract.id).get()
        if (invoiceItems.isNotEmpty()) {
            val firstInvoiceItem = invoiceItems.minBy { it.referenceDate }
            invoiceItems.forEach { invoiceItem ->
                if (invoiceItem != firstInvoiceItem) {
                    kafkaProducerService.produce(InvoiceItemCreatedEvent(invoiceItem))
                }
            }
        }
    }

    private suspend fun updateCompanyStatusBySubcontract(subcontract: CompanySubContract, status: CompanyStatus) =
        companyService.get(subcontract.companyId).map {
            companyService.update(it.copy(status = status))
        }

}


