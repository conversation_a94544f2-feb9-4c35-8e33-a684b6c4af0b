package br.com.alice.data.layer.tables

import br.com.alice.data.layer.models.StaffSignupRequestAddressModel
import org.assertj.core.api.Assertions.assertThat
import br.com.alice.data.layer.models.StaffSignupRequestHealthProfessionalModel
import br.com.alice.data.layer.models.StaffSignupRequestIntegrationContentModel
import br.com.alice.data.layer.models.StaffSignupRequestProviderModel
import br.com.alice.data.layer.models.StaffSignupRequestStatusModel
import br.com.alice.data.layer.models.StaffSignupRequestTypeModel
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class StaffSignupRequestTableTest {
    private val staffSignupRequest = StaffSignupRequestTable(
        version = 1,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        status = StaffSignupRequestStatusModel.PENDING,
        integrationContent = buildIntegrationContent(),
        type = StaffSignupRequestTypeModel.HEALTH_PROFESSIONAL,
    )

    @Test
    fun `#build is correct`() {
        assertThat(staffSignupRequest).isEqualTo(
            StaffSignupRequestTable(
                id = staffSignupRequest.id,
                version = staffSignupRequest.version,
                createdAt = staffSignupRequest.createdAt,
                updatedAt = staffSignupRequest.updatedAt,
                status = staffSignupRequest.status,
                integrationContent = staffSignupRequest.integrationContent,
                type = staffSignupRequest.type,
            )
        )
    }

    @Test
    fun `#copyTable should copy the table correctly`() {
        val yesterday = LocalDateTime.now().minusDays(1)
        assertThat(
            staffSignupRequest.copyTable(
                version = 2,
                updatedAt = yesterday,
                createdAt = yesterday,
            )
        ).isEqualTo(
            StaffSignupRequestTable(
                id = staffSignupRequest.id,
                version = 2,
                createdAt = yesterday,
                updatedAt = yesterday,
                status = staffSignupRequest.status,
                integrationContent = staffSignupRequest.integrationContent,
                type = staffSignupRequest.type,
            )
        )
    }

    private fun buildIntegrationContent() = StaffSignupRequestIntegrationContentModel(
        healthProfessional = StaffSignupRequestHealthProfessionalModel(
            fullName = "firstName lastName",
            email = "email",
            nationalId = "nationalId",
            birthdate = "birthdate",
            gender = "gender",
            profileImageUrl = "profileImageUrl",
            profileBio = "profileBio",
            education = "education",
            curiosity = "curiosity",
            councilType = "councilType",
            councilNumber = "councilNumber",
            councilState = "councilState",
            specialty = "specialty",
            subSpecialties = emptyList(),
            contacts = emptyList()
        ),
        provider = StaffSignupRequestProviderModel(
            name = "name",
            cnpj = "cnpj",
            cnes = "cnes",
            bankCode = "bankCode",
            agencyNumber = "agencyNumber",
            accountNumber = "accountNumber",
            phones = emptyList(),
            address = StaffSignupRequestAddressModel(
                street = "street",
                number = "number",
                complement = "complement",
                neighborhood = "neighborhood",
                state = "state",
                city = "city",
                zipcode = "zipcode",
                country = "country"
            )
        )
    )
}
