{"rules": [{"_comment": "Unauthenticated", "conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"_comment": "common policy", "resources": ["AppointmentTemplate", "CassiSpecialistModel", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "OutcomeConf"], "actions": ["view"]}, {"resources": ["ContactModel", "HealthProfessionalModel", "StaffModel", "StructuredAddress"], "actions": ["view"]}, {"_comment": "common policy", "resources": ["Appointment", "ContactModel", "StructuredAddress"], "actions": ["count", "view"]}, {"_comment": "common policy", "resources": ["HealthEventsModel"], "actions": ["count", "delete", "view"]}, {"resources": ["MagicNumbersModel"], "actions": ["create", "update", "view"]}], "branches": [{"_comment": "<PERSON><PERSON><PERSON>", "conditions": ["${resource.id} == 074c79e3-a4d3-4fc3-9d72-6370d28b0701"], "allow": [{"resources": ["PersonModel"], "actions": ["view"]}]}, {"_comment": "any HealthPlanTask with same token", "conditions": ["${resource.opaType} == HealthPlanTask", "${subject.key} == ${resource.contentToken}"], "allow": [{"resources": ["HealthPlanTask"], "actions": ["view"]}]}]}, {"_comment": "Health Professionals", "conditions": ["${subject.opaType} == HealthProfessionalSubject || HealthProfessionalSubject in ${subject.opaSuperTypes} || ${subject.opaType} == CareCoordinationSubject"], "allow": [{"resources": ["AppointmentMacro", "AppointmentTemplate", "AssistanceSummaryModel", "ChannelFup", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "ScreeningNavigation"], "actions": ["view"]}, {"resources": ["AppointmentMacro", "PersonHealthEvent"], "actions": ["count"]}, {"resources": ["RefundCounterReferralModel", "TimelineAiSummaryReview"], "actions": ["create", "update", "view"]}, {"resources": ["AppointmentProcedureExecuted"], "actions": ["count", "create", "delete", "view"]}], "branches": [{"_comment": "can view, update, and create PersonHealthEvent associated with her", "conditions": ["${resource.staffId} == ${subject.id}"], "allow": [{"resources": ["PersonHealthEvent"], "actions": ["view", "update", "create"]}]}]}, {"_comment": "External Health Professionals with person in portfolio", "conditions": ["${subject.opaType} == ExternalHealthProfessionalSubject", "${subject.memberInPortfolio} == true"], "allow": [{"resources": ["AliceTestResultBundle", "AliceTestResultFile", "Appointment", "AppointmentEvolution", "AppointmentScheduleModel", "AssistanceCare", "ConsentRegistration", "CounterReferral", "DasaDiagnosticReport", "DbLaboratoryTestResult", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "FleuryTestResult", "FleuryTestResultFile", "HaocDocument", "HaocProntoAtendimentoResult", "HaocSumarioDeAltaResult", "HealthDeclaration", "InsurancePortabilityRequestModel", "MemberModel", "MemberModel", "MemberProductPriceModel", "OutcomeConf", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonModel", "PersonOnboardingModel", "PersonTaskModel", "ProviderHealthDocumentModel", "PublicTokenIntegration", "TertiaryIntentionTouchPoint", "TestResultFileModel", "Timeline"], "actions": ["view"]}, {"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AssistanceCare", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthcareMap", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["count"]}, {"resources": ["ActionPlanTask", "AppContentScreenDetail", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthcareMap", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "PersonAdditionalInfoModel", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonInternalReference", "PersonModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "Risk", "ServiceScriptNode", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["create", "update", "view"]}], "branches": [{"_comment": "can view any exam document", "conditions": ["${resource.domain} == interop", "${resource.namespace} == exam_by_provider"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"_comment": "can view any exam document in vault", "conditions": ["${resource.domain} == eita", "${resource.namespace} == provider_health_document"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}]}, {"_comment": "External Health Professionals", "conditions": ["${subject.opaType} == ExternalHealthProfessionalSubject"], "allow": [{"resources": ["Timeline"], "actions": ["view"]}, {"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AssistanceCare", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthcareMap", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["count"]}, {"resources": ["AppointmentEvolution"], "actions": ["create", "view"]}, {"resources": ["HealthSpecialistResourceBundleModel", "OngoingCompanyDeal", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel"], "actions": ["count", "view"]}, {"resources": ["AppointmentProcedureExecuted"], "actions": ["count", "create", "delete", "view"]}]}, {"_comment": "Chief <PERSON>", "conditions": ["${subject.opaType} == ChiefRiskSubject"], "allow": [{"resources": ["ActionPlanTask", "AliceTestResultBundle", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AppointmentMacro", "AppointmentProcedureExecuted", "AppointmentScheduleModel", "AssistanceCare", "AssistanceCare", "AssistanceSummaryModel", "Beneficiary<PERSON><PERSON>l", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalBackground", "ClinicalOutcomeRecord", "CompanyModel", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CounterReferral", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLActionRecommendation", "HLAdherence", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTask", "HealthPlanTaskGroup", "HealthPlanTaskReferrals", "HealthSpecialistResourceBundleModel", "HealthcareMap", "HealthcareResourceModel", "HealthcareTeamModel", "InsurancePortabilityRequestModel", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "MemberModel", "MemberProductPriceModel", "OnboardingContractModel", "OngoingCompanyDeal", "PersonCase", "PersonCase", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonIdentityValidationModel", "PersonOnboardingModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "PriceListingModel", "ProductBundleModel", "ProductModel", "ProductPriceListingModel", "ProviderModel", "ProviderUnitModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "Timeline", "VideoCall", "WandaComment"], "actions": ["count", "view"]}, {"resources": ["CaseRecord", "PersonInternalReference"], "actions": ["count", "create", "view"]}, {"resources": ["PersonModel"], "actions": ["count", "update", "view"]}, {"resources": ["Appointment", "AppointmentEvent", "AppointmentEvolution", "AppointmentTemplate", "CuriosityNoteModel", "DraftCommandModel", "HealthDeclaration", "HealthMeasurementModel", "HealthPlan", "HealthPlanTaskGroup", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "PersonAdditionalInfoModel", "PersonGracePeriod", "PregnancyModel", "Risk", "TestResultFileModel"], "actions": ["count", "create", "update", "view"]}]}, {"_comment": "Alice Health Professionals", "conditions": ["${subject.opaType} == InternalHealthProfessionalSubject || ${subject.opaType} == DigitalCareProfessionalSubject || ${subject.opaType} == CareCoordinationSubject || ${subject.opaType} == OnSiteProfessionalSubject"], "allow": [{"resources": ["AliceTestResultBundle", "AliceTestResultFile", "AppointmentMacro", "AppointmentScheduleModel", "AssistanceCare", "AssistanceSummaryModel", "CaseRecord", "ConsentRegistration", "DasaDiagnosticReport", "DbLaboratoryTestResult", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "FileVault", "FleuryTestResult", "FleuryTestResultFile", "HaocDocument", "HaocProntoAtendimentoResult", "HaocSumarioDeAltaResult", "HealthDeclaration", "HealthcareResourceModel", "InsurancePortabilityRequestModel", "MemberModel", "MemberProductPriceModel", "PersonOnboardingModel", "OngoingCompanyDeal", "OutcomeConf", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonGracePeriod", "PersonIdentityValidationModel", "PersonTaskModel", "PersonOnboardingModel", "ProductModel", "ProductBundleModel", "ProductPriceListingModel", "ProviderModel", "ProviderHealthDocumentModel", "PublicTokenIntegration", "Risk", "TussProcedureSpecialtyModel", "Appointment", "AppointmentEvolution", "AssistanceCare", "CounterReferral", "MemberModel", "PersonModel", "TertiaryIntentionTouchPoint", "TestResultFileModel", "Timeline"], "actions": ["view"]}, {"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AssistanceCare", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthcareMap", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["count"]}, {"resources": ["Generic<PERSON><PERSON><PERSON><PERSON>", "SpecialistOpinionMessage"], "actions": ["create"]}, {"resources": ["Generic<PERSON><PERSON><PERSON><PERSON>", "HealthPlanTaskReferrals", "ProviderUnitModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "SpecialistOpinion", "SpecialistOpinionMessage", "HealthSpecialistResourceBundleModel"], "actions": ["count", "view"]}, {"resources": ["SpecialistOpinion"], "actions": ["create", "update"]}, {"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AppointmentTemplate", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "DraftCommandModel", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementCategory", "HealthMeasurementModel", "HealthMeasurementTypeModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "HealthcareMap", "HLActionRecommendation", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "PersonAdditionalInfoModel", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonInternalReference", "PersonModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "Risk", "Risk", "ServiceScriptNode", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["create", "update", "view"]}, {"resources": ["AppointmentProcedureExecuted"], "actions": ["count", "create", "delete", "view"]}], "branches": [{"_comment": "can view any exam document", "conditions": ["${resource.domain} == interop", "${resource.namespace} == exam_by_provider"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"_comment": "can view any exam document in vault", "conditions": ["${resource.domain} == eita", "${resource.namespace} == provider_health_document"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}]}, {"_comment": "Navigator Ops", "conditions": ["${subject.role} @= NAVIGATOR_OPS"], "allow": [{"resources": ["CaseRecord", "PersonCase"], "actions": ["view"]}]}, {"_comment": "Health Declaration Nurse", "conditions": ["${subject.role} @= HEALTH_DECLARATION_NURSE"], "allow": [{"resources": ["InsurancePortabilityRequestModel", "MemberContractModel", "OnboardingContractModel"], "actions": ["view"]}, {"resources": ["Appointment", "HealthDeclaration", "PersonGracePeriod"], "actions": ["create", "update", "view"]}], "branches": [{"conditions": ["${resource.type} == STATEMENT_OF_HEALTH"], "allow": [{"resources": ["Appointment"], "actions": ["view", "update", "create"]}]}]}, {"_comment": "Med and Enf Risk", "conditions": ["${subject.role} @= MED_RISK || ${subject.role} @= RISK_NURSE"], "allow": [{"resources": ["OnboardingContractModel"], "actions": ["view"]}, {"resources": ["HealthDeclaration", "PersonGracePeriod"], "actions": ["create", "update", "view"]}], "branches": [{"conditions": ["${resource.type} == STATEMENT_OF_HEALTH"], "allow": [{"resources": ["Appointment"], "actions": ["view", "update", "create"]}]}]}, {"_comment": "AssistanceCare Professional", "conditions": ["${subject.role} @= ASSISTANCE_CARE", "${resource.type} == ASSISTANCE_CARE || ${resource.type} == ANNOTATION || ${resource.type} == ANNOTATION_HEALTH_COMMUNITY || ${resource.type} == ON_SITE || ${resource.opaType} == AssistanceCare"], "allow": [{"resources": ["Appointment", "AssistanceCare"], "actions": ["create", "update"]}]}, {"_comment": "Physician Health Professionals", "conditions": ["${subject.opaType} == OnSiteProfessionalSubject || ${subject.role} @= PHYSICIAN", "${resource.type} == ON_SITE || ${resource.type} == DEFAULT || ${resource.opaType} == AssistanceCare"], "allow": [{"resources": ["Appointment", "AssistanceCare"], "actions": ["create", "update"]}]}, {"_comment": "HealthCareTeamNurse Professional", "conditions": ["${subject.role} @= HEALTHCARE_TEAM_NURSE", "${resource.type} == HEALTH_PLAN_CARE || ${resource.opaType} == AssistanceCare"], "allow": [{"resources": ["Appointment", "AssistanceCare"], "actions": ["create", "update"]}]}, {"_comment": "Immersion Health Professional", "conditions": ["${subject.role} @= HEALTHCARE_TEAM_NURSE || ${subject.role} @= MANAGER_PHYSICIAN", "${resource.type} == IMMERSION"], "allow": [{"resources": ["Appointment"], "actions": ["create", "update"]}]}, {"_comment": "CX Ops", "conditions": ["${subject.role} @= CX_OPS"], "allow": [{"resources": ["MemberModel", "MemberProductPriceModel", "PersonAdditionalInfoModel", "PersonClinicalAccount", "PersonInternalReference", "PersonModel", "PersonOnboardingModel"], "actions": ["view"]}, {"resources": ["PersonHealthEvent"], "actions": ["count"]}, {"resources": ["PersonHealthEvent"], "actions": ["create", "update", "view"]}], "branches": [{"conditions": ["${resource.category} == DM_ATTENDANCE || ${resource.category} == ALICE_AT_HOME || ${resource.category} == INTERNAL_TASK || ${resource.category} == REFUND_REQUESTED"], "allow": [{"resources": ["PersonHealthEvent"], "actions": ["view"]}]}]}, {"_comment": "Insurance Ops - Health Institution Ops", "conditions": ["${subject.role} @= INSURANCE_OPS_HEALTH_INSTITUTION_OPS"], "allow": [{"resources": ["HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "MemberModel", "PersonModel", "PersonTaskModel"], "actions": ["view"]}, {"resources": ["ClinicalBackground", "HealthDeclaration", "HealthFormQuestionAnswer", "HealthMeasurementModel", "MemberModel", "PersonModel"], "actions": ["view"]}, {"resources": ["CuriosityNoteModel", "MemberModel", "OngoingCompanyDeal", "PersonAdditionalInfoModel", "PersonClinicalAccount", "PersonInternalReference", "PersonModel"], "actions": ["view"]}, {"resources": ["Appointment", "AppointmentEvolution", "AssistanceCare", "CounterReferral", "MemberModel", "PersonModel", "TertiaryIntentionTouchPoint", "TestResultFileModel", "Timeline"], "actions": ["view"]}], "branches": [{"_comment": "can view any exam document", "conditions": ["${resource.opaType} == FileVault", "${resource.domain} == interop", "${resource.namespace} == exam_by_provider"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"_comment": "can view any exam document in vault", "conditions": ["${resource.opaType} == FileVault", "${resource.domain} == eita", "${resource.namespace} == provider_health_document"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}]}, {"_comment": "HealthOps Lead", "conditions": ["${subject.role} @= HEALTH_OPS_LEAD"], "allow": [{"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AppointmentEvolution", "AssistanceCare", "AssistanceCare", "CaseRecord", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalBackground", "ClinicalOutcomeRecord", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalHealthInformation", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLActionRecommendation", "HLAdherence", "HaocFhirProcess", "HealthDeclaration", "HealthFormQuestionAnswer", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthSpecialistResourceBundleModel", "HealthcareMap", "InsurancePortabilityRequestModel", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "MemberModel", "MemberModel", "MemberModel", "MemberModel", "OngoingCompanyDeal", "OutcomeConf", "PersonAdditionalInfoModel", "PersonCase", "PersonCase", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonInternalReference", "PersonModel", "PersonModel", "PersonModel", "PersonModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "Risk", "TertiaryIntentionTouchPoint", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "TestResultFileModel", "Timeline", "Timeline", "VideoCall", "WandaComment"], "actions": ["view"]}, {"resources": ["HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "PersonTaskModel"], "actions": ["create", "update", "view"]}], "branches": [{"_comment": "can view any exam document", "conditions": ["${resource.opaType} == FileVault", "${resource.domain} == interop", "${resource.namespace} == exam_by_provider"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"_comment": "can view any exam document in vault", "conditions": ["${resource.opaType} == FileVault", "${resource.domain} == eita", "${resource.namespace} == provider_health_document"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}]}, {"_comment": "ProductModel and Tech", "conditions": ["${subject.role} @= PRODUCT_TECH"], "allow": [{"resources": ["HaocDocument", "HealthFormQuestionAnswer"], "actions": ["view"]}, {"resources": ["PersonHealthEvent"], "actions": ["count"]}, {"resources": ["ProviderTestCodeModel"], "actions": ["delete"]}, {"resources": ["AppointmentTemplate", "ContactModel", "HealthMeasurementCategoryModel", "HealthMeasurementTypeModel", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "MedicineModel", "ProviderTestCodeModel", "ProviderUnitModel", "ProviderUnitTestCodeModel", "ServiceScriptNode", "StructuredAddress", "TestCodeModel", "TestPreparationModel"], "actions": ["create", "update", "view"]}], "branches": [{"_comment": "her own health information", "conditions": ["${resource.staffId} == ${subject.id}"], "allow": [{"resources": ["StaffSignToken", "StaffSignTokenModel"], "actions": ["count"]}]}]}, {"_comment": "De-identified HI viewer", "conditions": ["${subject.role} == DE_IDENTIFIED_HI_VIEWER"], "allow": [{"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AssistanceCare", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthcareMap", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["view"]}]}, {"_comment": "StaffSubject", "conditions": ["HealthProfessionalSubject in ${subject.opaSuperTypes} || StaffSubject in ${subject.opaSuperTypes} || ${subject.opaType} == StaffSubject"], "allow": [{"resources": ["AppointmentScheduleEventTypeModel", "AppointmentScheduleOptionModel", "BudNode", "CassiSpecialistModel", "ChannelMacro", "ChannelTag", "ConsolidatedAccreditedNetwork", "ContactModel", "HealthCondition", "HealthConditionAxis", "HealthConditionRelated", "HealthConditionTemplate", "HealthForm", "HealthFormAnswerGroup", "HealthFormQuestion", "HealthFormSection", "HealthMeasurementCategoryModel", "HealthMeasurementTypeModel", "HealthProfessionalModel", "HealthProfessionalModel", "HealthcareAdditionalTeam", "HealthcareTeamModel", "MedicalSpecialtyModel", "MedicineModel", "MvAuthorizedProcedureModel", "PrescriptionSentenceModel", "PriceListingModel", "ProductBundleModel", "ProductModel", "ProductPriceListingModel", "Protocol", "ProviderModel", "ProviderTestCodeModel", "ProviderUnitTestCodeModel", "RefundCounterReferralModel", "ServiceScriptAction", "ServiceScriptNode", "ServiceScriptRelationship", "StaffModel", "StructuredAddress", "TestCodeModel", "TestCodePackageModel", "TotvsGuiaModel", "TrackPersonABModel", "TussProcedureSpecialtyModel"], "actions": ["view"]}, {"resources": ["AppointmentAutofillHistory"], "actions": ["create"]}, {"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "CompanyModel", "ProviderUnitModel"], "actions": ["count", "view"]}, {"resources": ["ServiceScriptExecution", "ServiceScriptNavigation", "ServiceScriptNavigationGroup"], "actions": ["create", "update", "view"]}], "branches": [{"conditions": ["${resource.namespace} == member-photo"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"conditions": ["${resource.staffId} == ${subject.id}"], "allow": [{"resources": ["Appointment"], "actions": ["update"]}]}, {"_comment": "herOwn", "conditions": ["${subject.id} == ${resource.id} || ${subject.id} == ${resource.staffId}"], "allow": [{"resources": ["StaffSignTokenModel"], "actions": ["create", "update", "view"]}]}, {"_comment": "herself", "conditions": ["${subject.id} == ${resource.id}"], "allow": [{"resources": ["StaffModel"], "actions": ["create", "update", "view"]}]}]}, {"_comment": "Health Specialist", "conditions": ["${subject.role} @= COMMUNITY"], "allow": [{"resources": ["ActionPlanTask", "AliceTestResultBundle", "AliceTestResultFile", "AppContentScreenDetail", "Appointment", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AssistanceCare", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "DasaDiagnosticReport", "DbLaboratoryTestResult", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirBundle", "FhirDiagnosticReport", "FhirDiagnosticReport", "FhirDocument", "FhirDocument", "FleuryTestResult", "FleuryTestResultFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocDocument", "HaocFhirProcess", "HaocProntoAtendimentoResult", "HaocSumarioDeAltaResult", "HealthDeclaration", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthcareMap", "InsurancePortabilityRequestModel", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "MemberModel", "MemberProductPriceModel", "PersonAdditionalInfoModel", "PersonCase", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonInternalReference", "PersonModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "ProviderHealthDocumentModel", "ProviderUnitModel", "PublicTokenIntegration", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["view"]}, {"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AssistanceCare", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthcareMap", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "PersonCase", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["count"]}, {"resources": ["SpecialistOpinionMessage"], "actions": ["create"]}, {"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "CompanyModel", "HealthProfessionalModel", "StaffModel"], "actions": ["count", "view"]}, {"resources": ["HealthPlanTaskReferrals"], "actions": ["count", "view"]}, {"resources": ["SpecialistOpinion"], "actions": ["create", "update"]}, {"resources": ["FileVault"], "actions": ["count", "view"]}, {"resources": ["CounterReferral", "CounterReferralRelevance", "HealthCommunityUnreferencedAccessModel"], "actions": ["create", "update", "view"]}, {"resources": ["Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["count", "create", "view"]}], "branches": [{"conditions": ["${resource.opaType} == HealthPlanTask", "${resource.type} == REFERRAL"], "allow": [{"resources": ["HealthPlanTask"], "actions": ["view", "update", "create"]}]}, {"conditions": ["${resource.type} == EXTERNAL_PAID_HEALTH_PROFESSIONAL"], "allow": [{"resources": ["HealthProfessionalModel", "StaffModel"], "actions": ["create"]}]}]}, {"_comment": "All StaffModel", "conditions": ["${subject.opaType} == StaffModel"], "allow": [{"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "CompanyModel"], "actions": ["count", "view"]}]}, {"_comment": "Insurance Ops - Community Success", "conditions": ["${subject.role} @= INSURANCE_OPS_COMMUNITY_SUCCESS"], "allow": [{"resources": ["AliceTestResultBundle", "AliceTestResultFile", "Appointment", "AppointmentEvolution", "AssistanceCare", "ClinicalBackground", "ConsentRegistration", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "DbLaboratoryTestResult", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "FleuryTestResult", "FleuryTestResultFile", "HaocDocument", "HaocProntoAtendimentoResult", "HaocSumarioDeAltaResult", "HealthDeclaration", "HealthFormQuestionAnswer", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "InsurancePortabilityRequestModel", "MemberModel", "MemberModel", "OngoingCompanyDeal", "PersonAdditionalInfoModel", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonInternalReference", "PersonModel", "PersonModel", "PersonTaskModel", "ProviderHealthDocumentModel", "PublicTokenIntegration", "TertiaryIntentionTouchPoint", "TestResultFileModel", "Timeline"], "actions": ["view"]}], "branches": [{"conditions": ["${resource.domain} == interop", "${resource.namespace} == exam_by_provider"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"conditions": ["${resource.domain} == eita", "${resource.namespace} == provider_health_document"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}]}, {"_comment": "Health - Técnica(o) de Enfermagem Casa Alice", "conditions": ["${subject.role} @= TECHNIQUE_NURSE"], "allow": [{"resources": ["AliceTestResultBundle", "AliceTestResultFile", "Appointment", "AppointmentEvolution", "AssistanceCare", "ConsentRegistration", "CounterReferral", "DasaDiagnosticReport", "DbLaboratoryTestResult", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "FleuryTestResult", "FleuryTestResultFile", "HaocDocument", "HaocProntoAtendimentoResult", "HaocSumarioDeAltaResult", "MemberModel", "MemberModel", "PersonEligibilityDuquesa", "PersonModel", "PersonModel", "ProviderHealthDocumentModel", "PublicTokenIntegration", "TertiaryIntentionTouchPoint", "TestResultFileModel", "Timeline"], "actions": ["view"]}, {"resources": ["HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "PersonTaskModel"], "actions": ["create", "update", "view"]}, {"resources": ["ClinicalBackground", "HealthDeclaration", "HealthMeasurementModel"], "actions": ["create", "update", "view"]}, {"resources": ["CuriosityNoteModel", "PersonAdditionalInfoModel", "PersonClinicalAccount", "PersonInternalReference"], "actions": ["create", "update", "view"]}], "branches": [{"conditions": ["${resource.domain} == interop", "${resource.namespace} == exam_by_provider"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}, {"conditions": ["${resource.domain} == eita", "${resource.namespace} == provider_health_document"], "allow": [{"resources": ["FileVault"], "actions": ["view"]}]}]}, {"_comment": "Tertiary Intention Touch Point", "conditions": ["${subject.role} @= ALICE_HEALTH_PROFESSIONAL"], "allow": [{"resources": ["TertiaryIntentionTouchPoint"], "actions": ["create", "update", "view"]}]}, {"_comment": "ProductModel Tech - Health", "conditions": ["${subject.role} @= PRODUCT_TECH_HEALTH"], "allow": [{"resources": ["ActionPlanTask", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AppointmentProcedureExecuted", "AssistanceCare", "Beneficiary<PERSON><PERSON>l", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "Discharge<PERSON><PERSON><PERSON><PERSON>", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirDiagnosticReport", "FhirDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocFhirProcess", "HealthDeclaration", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthSpecialistResourceBundleModel", "HealthcareMap", "HealthcareResourceModel", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "MemberModel", "PersonAdditionalInfoModel", "PersonCase", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonGracePeriod", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonInternalReference", "PersonModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["count", "create", "delete", "update", "view"]}, {"resource": ["AppointmentMacro"], "actions": ["count", "view"]}]}, {"_comment": "Health Audit Subject", "conditions": ["${subject.opaType} == HealthAuditSubject"], "allow": [{"resources": ["ActionPlanTask", "AliceTestResultBundle", "AliceTestResultFile", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AppointmentTemplate", "AssistanceCare", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "DasaDiagnosticReport", "DbLaboratoryTestResult", "Discharge<PERSON><PERSON><PERSON><PERSON>", "DraftCommandModel", "EinsteinAlergia", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirBundle", "FhirDiagnosticReport", "FhirDiagnosticReport", "FhirDocument", "FhirDocument", "FleuryTestResult", "FleuryTestResultFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocDocument", "HaocFhirProcess", "HaocProntoAtendimentoResult", "HaocSumarioDeAltaResult", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementCategoryModel", "HealthMeasurementModel", "HealthMeasurementTypeModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "HealthcareMap", "InsurancePortabilityRequestModel", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "MemberModel", "MemberProductPriceModel", "PersonAdditionalInfoModel", "PersonCase", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonEligibilityDuquesa", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonInternalReference", "PersonModel", "PersonOnboardingModel", "PersonTaskModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "ProviderHealthDocumentModel", "PublicTokenIntegration", "Risk", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["count", "view"]}]}, {"_comment": "Health Ops Subject", "conditions": ["${subject.opaType} == HealthOpsSubject"], "allow": [{"resources": ["ActionPlanTask", "AliceTestResultBundle", "AliceTestResultFile", "AppContentScreenDetail", "Appointment", "AppointmentCoordination", "AppointmentEvent", "AppointmentEvolution", "AppointmentProcedureExecuted", "AppointmentTemplate", "AssistanceCare", "Beneficiary<PERSON><PERSON>l", "CaseRecord", "ChannelComment", "ChannelHistory", "ChannelTheme", "ClinicalBackground", "ClinicalOutcomeRecord", "ConsentRegistration", "ConsentRegistration", "ConsolidatedRewards", "ConsolidatedRewardsModel", "CounterReferral", "CuriosityNoteModel", "DasaDiagnosticReport", "DasaDiagnosticReport", "DbLaboratoryTestResult", "Discharge<PERSON><PERSON><PERSON><PERSON>", "DraftCommandModel", "EinsteinAlergia", "EinsteinAlergia", "EinsteinAtendimento", "EinsteinAtendimento", "EinsteinAvaliacaoInicial", "EinsteinAvaliacaoInicial", "EinsteinDadosDeAlta", "EinsteinDadosDeAlta", "EinsteinDiagnostico", "EinsteinDiagnostico", "EinsteinEncaminhamento", "EinsteinEncaminhamento", "EinsteinMedicamento", "EinsteinMedicamento", "EinsteinProcedimento", "EinsteinProcedimento", "EinsteinResultadoExame", "EinsteinResultadoExame", "EinsteinResumoInternacao", "EinsteinResumoInternacao", "EinsteinStructuredTestResult", "EinsteinStructuredTestResult", "ExternalReferral", "ExternalReferralModel", "FhirBundle", "FhirBundle", "FhirDiagnosticReport", "FhirDiagnosticReport", "FhirDocument", "FhirDocument", "FleuryTestResult", "FleuryTestResultFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HDataOverview", "HLAdherence", "HaocDocument", "HaocFhirProcess", "HaocProntoAtendimentoResult", "HaocSumarioDeAltaResult", "HealthDeclaration", "HealthFormQuestionAnswer", "HealthLogicRecord", "HealthMeasurement", "HealthMeasurementCategoryModel", "HealthMeasurementModel", "HealthMeasurementTypeModel", "HealthPlan", "HealthPlanTask", "HealthPlanTaskGroup", "HealthPlanTaskGroupTemplate", "HealthPlanTaskTemplate", "HealthSpecialistResourceBundleModel", "HealthcareMap", "HealthcareResourceModel", "InsurancePortabilityRequestModel", "IntentionCoordination", "LaboratoryTestResult", "LaboratoryTestResultModel", "MemberModel", "MemberProductPriceModel", "PersonAdditionalInfoModel", "PersonCase", "PersonClinicalAccount", "PersonEligibilityDuquesa", "PersonEligibilityDuquesa", "PersonGracePeriod", "PersonHealthEvent", "PersonHealthGoalModel", "<PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "PersonHealthcareTeamRecommendationModel", "PersonInternalReference", "PersonModel", "PersonOnboardingModel", "PersonTaskModel", "PersonTeamAssociation", "Pregnancy", "PregnancyModel", "ProviderHealthDocumentModel", "PublicTokenIntegration", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "Risk", "StaffModel", "TertiaryIntentionTouchPoint", "TestResultFeedback", "TestResultFile", "TestResultFileModel", "Timeline", "VideoCall", "WandaComment"], "actions": ["count", "view"]}]}, {"_comment": "Alice Health Professional or Navigator", "conditions": ["${subject.role} @= ALICE_HEALTH_PROFESSIONAL_OR_NAVIGATOR"], "allow": [{"resources": ["TestResultFeedback"], "actions": ["create", "view"]}]}], "aggregate": ["AppointmentAutofillHistory", "AppointmentMacro", "AppointmentProcedureExecuted", "AppointmentScheduleEventTypeModel", "AppointmentScheduleOptionModel", "AppointmentTemplate", "AssistanceSummaryModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "BudNode", "CassiMemberModel", "CassiSpecialistModel", "ChannelFup", "ChannelMacro", "ChannelTag", "CompanyModel", "ConsolidatedAccreditedNetwork", "ContactModel", "CounterReferralRelevance", "DraftCommandModel", "ExternalHealthInformation", "Generic<PERSON><PERSON><PERSON><PERSON>", "HLActionRecommendation", "HealthCommunityUnreferencedAccessModel", "HealthCondition", "HealthConditionAxis", "HealthConditionRelated", "HealthConditionTemplate", "HealthEventsModel", "HealthForm", "HealthFormAnswerGroup", "HealthFormQuestion", "HealthFormSection", "HealthMeasurementCategory", "HealthMeasurementCategoryModel", "HealthMeasurementTypeModel", "HealthPlanTaskGroupTemplate", "HealthPlanTaskReferrals", "HealthPlanTaskTemplate", "HealthSpecialistResourceBundleModel", "HealthcareAdditionalTeam", "HealthcareResourceModel", "HealthcareTeamModel", "MagicNumbersModel", "MedicalSpecialtyModel", "MedicineModel", "MemberContractModel", "MvAuthorizedProcedureModel", "OnboardingContractModel", "OngoingCompanyDeal", "PersonGracePeriod", "PersonIdentityValidationModel", "PrescriptionSentenceModel", "PriceListingModel", "ProductBundleModel", "ProductModel", "ProductPriceListingModel", "Protocol", "ProviderModel", "ProviderTestCodeModel", "ProviderUnitModel", "ProviderUnitTestCodeModel", "RefundCounterReferralModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "ScreeningNavigation", "ServiceScriptAction", "ServiceScriptExecution", "ServiceScriptNavigation", "ServiceScriptNavigationGroup", "ServiceScriptRelationship", "SpecialistOpinion", "SpecialistOpinionMessage", "StructuredAddress", "TestCodeModel", "TestCodePackageModel", "TestPreparationModel", "TimelineAiSummaryReview", "TotvsGuiaModel", "TrackPersonABModel", "TussProcedureSpecialtyModel"]}