package app.fhir_domain_service_backfill

import rego.v1
import data.app.data_layer

allow := passed if {
    passed := data_layer.allow with data.rules as data.app.fhir_domain_service_backfill.rules
        with data.aggregate as data.app.fhir_domain_service_backfill.aggregate
}

is_aggregate := agg if {
    agg := data_layer.is_aggregate
        with data.aggregate as data.app.fhir_domain_service_backfill.aggregate
}
