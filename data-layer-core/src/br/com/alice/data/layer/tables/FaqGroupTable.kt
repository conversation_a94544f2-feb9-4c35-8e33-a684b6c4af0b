package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.FaqGroupType
import java.time.LocalDateTime
import java.util.UUID

internal data class FaqGroupTable(
    val title: String,
    val description: String? = null,
    val featured: <PERSON><PERSON>an,
    val active: Boolean,
    val brand: Brand? = Brand.ALICE,
    val groupType: FaqGroupType? = null,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now()
) : Table<FaqGroupTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
