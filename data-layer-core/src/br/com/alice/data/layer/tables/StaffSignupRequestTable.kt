package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.isNotBlank
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.StaffSignupRequestAdditionalInfoModel
import br.com.alice.data.layer.models.StaffSignupRequestIntegrationContentModel
import br.com.alice.data.layer.models.StaffSignupRequestStatusModel
import br.com.alice.data.layer.models.StaffSignupRequestTypeModel
import java.time.LocalDateTime
import java.util.UUID

internal data class StaffSignupRequestTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int,
    override val createdAt: LocalDateTime,
    override val updatedAt: LocalDateTime,
    val status: StaffSignupRequestStatusModel,
    val rejectionReason: String? = null,
    val reviewedBy: UUID? = null,
    val staffId: UUID? = null,
    val integrationContent: StaffSignupRequestIntegrationContentModel,
    val type: StaffSignupRequestTypeModel,
    val additionalInfo: StaffSignupRequestAdditionalInfoModel? = null,
    var searchTokens: TsVector? = null,
) : Table<StaffSignupRequestTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

    init {
        val fullName = integrationContent.healthProfessional.fullName
        val nationalId = integrationContent.healthProfessional.nationalId

        val value = listOfNotNull(fullName, nationalId)
            .filter { it.isNotBlank() }
            .joinToString(" ")
        searchTokens = TsVector(value.unaccent())
    }
}
