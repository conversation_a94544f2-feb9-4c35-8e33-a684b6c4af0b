package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.services.internal.AppointmentExecutedInternalService
import br.com.alice.common.Response
import br.com.alice.common.foldResponse
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import io.ktor.http.HttpStatusCode

class SuggestedProcedureController(
    staffService: StaffService,
    private val appointmentExecutedInternalService: AppointmentExecutedInternalService,
    private val healthProfessionalService: HealthProfessionalService
) : StaffController(staffService) {

    suspend fun suggested(type: String): Response = currentStaff().let { staff ->
        healthProfessionalService.findByStaffId(
            staffId = staff.id,
        ).flatMap { hp ->
            if (hp.specialtyId == null) return@let Response(HttpStatusCode.NoContent)
            appointmentExecutedInternalService.getSuggestedProcedures(
                hp.specialtyId!!,
                SuggestedProcedureType.valueOf(type),
            )
        }.foldResponse()
    }
    suspend fun suggestedV2(type: String): Response = currentStaff().let { staff ->
        healthProfessionalService.findByStaffId(
            staffId = staff.id,
        ).flatMap { hp ->
            if (hp.specialtyId == null) return@let Response(HttpStatusCode.NoContent)
            appointmentExecutedInternalService.getSuggestedProceduresV2(
                hp.specialtyId!!,
                SuggestedProcedureType.valueOf(type),
            )
        }.foldResponse()
    }
}

enum class SuggestedProcedureType {
    PROCEDURE_EXECUTED_DEFAULT,
    PROCEDURE_EXECUTED
}
