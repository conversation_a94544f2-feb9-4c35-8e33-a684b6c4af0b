package br.com.alice.api.ehr.services.internal

import br.com.alice.api.ehr.controllers.SuggestedProcedureType
import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapFirst
import br.com.alice.data.layer.models.AppointmentProcedureExecuted
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.TypeOfService
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.exec.indicator.client.HealthSpecialistProcedureService
import br.com.alice.exec.indicator.client.TussCodeWithAliceCodeRequest
import br.com.alice.exec.indicator.client.specialistResources.SpecialistResourceService
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import br.com.alice.exec.indicator.models.HealthSpecialistProcedureServiceType
import br.com.alice.exec.indicator.models.SpecialistResource
import br.com.alice.exec.indicator.models.SpecialistResourceFilters
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID
import kotlinx.coroutines.coroutineScope

class AppointmentExecutedInternalService(
    private val procedureExecutedService: AppointmentProcedureExecutedService,
    private val specialistResourceService: SpecialistResourceService,
    private val healthSpecialistProcedureService: HealthSpecialistProcedureService,
) {
    suspend fun getByAppointmentId(
        appointmentId: UUID
    ): Result<List<ProcedureResponse>, Throwable> {
        val procedures = procedureExecutedService.getByAppointmentId(appointmentId).get()
        val healthSpecialistProceduresMap = getHealthSpecialistProcedures(procedures)

        return procedures.map { procedure ->
            ProcedureResponse(
                id = procedure.id,
                name = healthSpecialistProceduresMap[procedure.tussCode]?.description ?: "",
                tussCode = procedure.tussCode,
                aliceCode = procedure.tussProcedureAliceCode,
                groupType = getGroupTypeFromServiceType(healthSpecialistProceduresMap[procedure.tussCode]?.serviceType),
                isPriced = procedure.isPriced
            )
        }.success()
    }

    suspend fun getDefaultProcedure(specialtyId: UUID): Result<ProcedureResponse, Throwable> =
        specialistResourceService.list(
            filter = SpecialistResourceFilters(
                medicalSpecialtyIds = listOf(specialtyId),
                status = Status.ACTIVE,
                recommendationLevel = listOf(AppointmentRecommendationLevel.DEFAULT),
            ),
            range = 0..1
        )
            .map { it.items.firstOrNull() ?: throw NotFoundException() }
            .flatMap { buildProcedureResponseWithPrice(specialtyId, listOf(it)) }
            .mapFirst()


    suspend fun add(
        procedure: AppointmentProcedureExecuted
    ): Result<ProcedureResponse, Throwable> = coroutineScope {
        procedureExecutedService.add(procedure).flatMapPair {
            healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                listOf(TussCodeWithAliceCodeRequest(procedure.tussCode, procedure.healthSpecialistResourceBundleCode))
            )
        }.map { (healthSpecialistProcedures, procedure) ->
            val healthSpecialistProcedure = healthSpecialistProcedures.firstOrNull()
            ProcedureResponse(
                id = procedure.id,
                name = healthSpecialistProcedure?.description ?: "",
                tussCode = procedure.tussCode,
                aliceCode = procedure.tussProcedureAliceCode,
                groupType = getGroupTypeFromServiceType(healthSpecialistProcedure?.serviceType),
                isPriced = procedure.isPriced
            )
        }
    }

    suspend fun addV2(
        procedure: AppointmentProcedureExecuted
    ): Result<ProcedureResponseV2, Throwable> =
        procedureExecutedService.add(procedure).map {
            ProcedureResponseV2(
                id = it.id,
                name = procedure.name ?: "",
                tussCode = it.tussCode,
                aliceCode = it.tussProcedureAliceCode,
                typeOfService = it.typeOfService,
                isPriced = it.isPriced
            )
        }

    suspend fun deleteDraftProcedure(id: UUID): Result<Boolean, Throwable> =
        procedureExecutedService.deleteDraftProcedure(id)

    suspend fun getSuggestedProcedures(
        specialtyId: UUID,
        type: SuggestedProcedureType,
    ): Result<List<ProcedureResponse>, Throwable> =
        getSuggested(specialtyId, type)
            .flatMap { buildProcedureResponseWithPrice(specialtyId, it) }

    suspend fun getSuggestedProceduresV2(
        specialtyId: UUID,
        type: SuggestedProcedureType,
    ): Result<List<ProcedureResponseV2>, Throwable> =
        getSuggested(specialtyId, type)
            .flatMap { buildProcedureResponseWithPriceV2(specialtyId, it) }

    suspend fun addProcedureDefault(
        appointmentId: UUID,
        procedure: ProcedureResponse
    ): Result<List<ProcedureResponse>, Throwable> =
        procedureExecutedService.addDefaultProcedureByAppointment(
            AppointmentProcedureExecuted(
                tussCode = procedure.tussCode,
                tussProcedureAliceCode = procedure.aliceCode,
                healthSpecialistResourceBundleCode = procedure.aliceCode,
                isPriced = procedure.isPriced,
                appointmentId = appointmentId
            )
        ).map {
            listOf(procedure.copy(id = it.id))
        }

    private suspend fun getHealthSpecialistProcedures(
        appointmentProceduresExecuted: List<AppointmentProcedureExecuted>
    ): Map<String, HealthSpecialistProcedure> {
        val request = appointmentProceduresExecuted.map {
            TussCodeWithAliceCodeRequest(it.tussCode, it.healthSpecialistResourceBundleCode)
        }
        return if (request.isEmpty()) emptyMap()
        else associateByTussCode(request)
    }

    private suspend fun getSuggested(specialtyId: UUID, type: SuggestedProcedureType) =
        specialistResourceService.list(
            filter = SpecialistResourceFilters(
                medicalSpecialtyIds = listOf(specialtyId),
                status = Status.ACTIVE,
                recommendationLevel = if (type == SuggestedProcedureType.PROCEDURE_EXECUTED) listOf(
                    AppointmentRecommendationLevel.RECOMMENDED
                )
                else listOf(AppointmentRecommendationLevel.DEFAULT),
            ),
            range = 0..20
        )
            .map { it.items }

    private suspend fun buildProcedureResponseWithPrice(
        specialtyId: UUID,
        specialistResources: List<SpecialistResource>
    ): Result<List<ProcedureResponse>, Throwable> = coResultOf {
        if (specialistResources.isEmpty()) return@coResultOf emptyList()

        specialistResources.map { resource ->
            ProcedureResponse(
                id = resource.id,
                name = resource.description,
                tussCode = resource.primaryTuss.code,
                aliceCode = resource.code,
                groupType = getGroupTypeFromResourceBundleServiceType(resource.serviceType),
                isPriced = resource.medicalSpecialties.any { it.medicalSpecialtyId == specialtyId && it.pricingStatus == PricingStatus.PRICED }
            )
        }
    }

    private suspend fun buildProcedureResponseWithPriceV2(
        specialtyId: UUID,
        specialistResources: List<SpecialistResource>
    ): Result<List<ProcedureResponseV2>, Throwable> = coResultOf {
        if (specialistResources.isEmpty()) return@coResultOf emptyList()

        specialistResources.map { resource ->
            ProcedureResponseV2(
                id = resource.id,
                name = resource.description,
                tussCode = resource.primaryTuss.code,
                aliceCode = resource.code,
                typeOfService = getGroupTypeFromResourceBundleServiceTypeV2(
                    resource.serviceType,
                    resource.executionEnvironment
                ),
                isPriced = resource.medicalSpecialties.any { it.medicalSpecialtyId == specialtyId && it.pricingStatus == PricingStatus.PRICED }
            )
        }
    }

    private suspend fun associateByTussCode(request: List<TussCodeWithAliceCodeRequest>) =
        healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(request).get()
            .associateBy { it.tussCode }

    private fun getGroupTypeFromServiceType(serviceType: HealthSpecialistProcedureServiceType?) = when (serviceType) {
        HealthSpecialistProcedureServiceType.CONSULTATION -> ProcedureGroupType.CONSULTA
        HealthSpecialistProcedureServiceType.OUTPATIENT_PROCEDURE -> ProcedureGroupType.EXAME
        HealthSpecialistProcedureServiceType.EXAM -> ProcedureGroupType.EXAME
        HealthSpecialistProcedureServiceType.SURGICAL_PROCEDURE -> ProcedureGroupType.PROCEDIMENTO_CIRURGICO
        else -> null
    }

    private fun getGroupTypeFromResourceBundleServiceType(serviceType: HealthSpecialistResourceBundleServiceType) =
        when (serviceType) {
            HealthSpecialistResourceBundleServiceType.CONSULTATION -> ProcedureGroupType.CONSULTA
            HealthSpecialistResourceBundleServiceType.EXAM -> ProcedureGroupType.EXAME
            HealthSpecialistResourceBundleServiceType.PROCEDURE -> ProcedureGroupType.PROCEDIMENTO_CIRURGICO
            else -> null
        }

    private fun getGroupTypeFromResourceBundleServiceTypeV2(
        serviceType: HealthSpecialistResourceBundleServiceType,
        executionEnvironment: HealthSpecialistProcedureExecutionEnvironment
    ) =
        when (serviceType) {
            HealthSpecialistResourceBundleServiceType.CONSULTATION -> TypeOfService.CONSULTATION
            HealthSpecialistResourceBundleServiceType.EXAM -> TypeOfService.EXAM
            HealthSpecialistResourceBundleServiceType.PROCEDURE -> when (executionEnvironment) {
                HealthSpecialistProcedureExecutionEnvironment.SURGICAL -> TypeOfService.SURGICAL_PROCEDURE
                else -> TypeOfService.OUTPATIENT_PROCEDURE
            }

            HealthSpecialistResourceBundleServiceType.UNDEFINED -> null
        }
}

data class ProcedureResponse(
    val id: UUID,
    val name: String,
    val tussCode: String,
    val aliceCode: String?,
    val groupType: ProcedureGroupType?,
    val isPriced: Boolean
)

data class ProcedureResponseV2(
    val id: UUID,
    val name: String,
    val tussCode: String,
    val aliceCode: String?,
    val typeOfService: TypeOfService?,
    val isPriced: Boolean
)

enum class ProcedureGroupType(val description: String) {
    CONSULTA("Consulta"),
    DIARIA("Diária"),
    EXAME("Exame"),
    MISTO("Misto"),
    PROCEDIMENTO_CIRURGICO("Procedimento Cirúrgico"),
    PROCEDIMENTO_CLINICO("Procedimento Clínico"),
    TABELA_PROPRIA("Tabela Própria"),
    TAXA("Taxa"),
    TERAPIA("Terapia");

    companion object {
        fun getByDescription(description: String?): ProcedureGroupType? =
            values().find { it.description.equals(description, ignoreCase = true) }
    }
}
