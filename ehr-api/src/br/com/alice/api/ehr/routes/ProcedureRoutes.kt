package br.com.alice.api.ehr.routes

import br.com.alice.api.ehr.controllers.ProcedureController
import br.com.alice.api.ehr.controllers.SuggestedProcedureController
import br.com.alice.api.ehr.controllers.TussProcedureController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get

fun Route.procedureRoutes() {
    val procedureController by inject<ProcedureController>()
    val tussProcedureController by inject<TussProcedureController>()
    val suggestedProcedureController by inject<SuggestedProcedureController>()

    authenticate {
        get("/procedure") { coHandler(procedureController::index) }
        get("/tuss_procedure") { coHandler(tussProcedureController::search)}
        get("/suggested/procedures/{type}") { coHand<PERSON>("type", suggestedProcedureController::suggested) }
        get("/v2/suggested/procedures/{type}") { coHand<PERSON>("type", suggestedProcedureController::suggestedV2) }
    }
}
