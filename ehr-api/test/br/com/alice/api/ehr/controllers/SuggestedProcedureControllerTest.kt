package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.api.ehr.services.internal.AppointmentExecutedInternalService
import br.com.alice.api.ehr.services.internal.ProcedureGroupType
import br.com.alice.api.ehr.services.internal.ProcedureResponse
import br.com.alice.api.ehr.services.internal.ProcedureResponseV2
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TypeOfService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class SuggestedProcedureControllerTest : ControllerTestHelper() {
    private val staffService: StaffService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val appointmentExecutedInternalService: AppointmentExecutedInternalService = mockk()

    val controller = SuggestedProcedureController(
        staffService,
        appointmentExecutedInternalService,
        healthProfessionalService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    private val hp = TestModelFactory.buildHealthProfessional(
        specialtyId = RangeUUID.generate(),
        staffId = staff.id
    )
    private val type = SuggestedProcedureType.PROCEDURE_EXECUTED


    private val procedureResponse = ProcedureResponse(
        id = RangeUUID.generate(),
        name = "Alice Exame",
        tussCode = "123456",
        aliceCode = "123456",
        groupType = ProcedureGroupType.EXAME,
        isPriced = true
    )

    private val procedureResponseV2 = ProcedureResponseV2(
        id = RangeUUID.generate(),
        name = "Alice Exame",
        tussCode = "123456",
        aliceCode = "123456",
        typeOfService = TypeOfService.EXAM,
        isPriced = true
    )

    @Test
    fun `suggested should return suggested procedures`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id, any()) } returns hp.success()
        coEvery {
            appointmentExecutedInternalService.getSuggestedProcedures(
                hp.specialtyId!!,
                type
            )
        } returns listOf(procedureResponse).success()

        val expectedValue = listOf(
            ProcedureResponse(
                id = procedureResponse.id,
                name = procedureResponse.name,
                tussCode = procedureResponse.tussCode,
                aliceCode = procedureResponse.aliceCode,
                groupType = ProcedureGroupType.EXAME,
                isPriced = true
            )
        )
        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/suggested/procedures/$type"
            ) { response ->
                assertThat(response).isOKWithData(expectedValue)
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
        coVerifyOnce { appointmentExecutedInternalService.getSuggestedProcedures(any(), any()) }
    }

    @Test
    fun `suggested should return no content when hp has specialty id is null`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id, any()) } returns hp.copy(specialtyId = null)
            .success()

        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/suggested/procedures/$type"
            ) { response ->
                assertThat(response).isNoContent()
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
    }

    @Test
    fun `suggested should return bad request when type is not valid`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id, any()) } returns hp.success()

        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/suggested/procedures/INVALID"
            ) { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
    }

    @Test
    fun `suggested should return empty when suggested procedure is empty`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id) } returns hp.success()
        coEvery {
            appointmentExecutedInternalService.getSuggestedProcedures(
                hp.specialtyId!!,
                type
            )
        } returns emptyList<ProcedureResponse>().success()

        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/suggested/procedures/$type"
            ) { response ->
                assertThat(response).isOKWithData(emptyList<ProcedureResponse>())
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { appointmentExecutedInternalService.getSuggestedProcedures(any(), any()) }
    }


    @Test
    fun `suggestedV2 should return suggested procedures`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id, any()) } returns hp.success()
        coEvery {
            appointmentExecutedInternalService.getSuggestedProceduresV2(
                hp.specialtyId!!,
                type
            )
        } returns listOf(procedureResponseV2).success()
        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/v2/suggested/procedures/$type"
            ) { response ->
                assertThat(response).isOKWithData(listOf(procedureResponseV2))
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
        coVerifyOnce { appointmentExecutedInternalService.getSuggestedProceduresV2(any(), any()) }
    }

    @Test
    fun `suggestedV2 should return no content when hp has specialty id is null`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id, any()) } returns hp.copy(specialtyId = null)
            .success()

        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/v2/suggested/procedures/$type"
            ) { response ->
                assertThat(response).isNoContent()
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
    }

    @Test
    fun `suggestedV2 should return bad request when type is not valid`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id, any()) } returns hp.success()

        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/v2/suggested/procedures/INVALID"
            ) { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
    }

    @Test
    fun `suggestedV2 should return empty when suggested procedure is empty`() {
        coEvery { staffService.get(staffTest.id) } returns staff.success()
        coEvery { healthProfessionalService.findByStaffId(staff.id) } returns hp.success()
        coEvery {
            appointmentExecutedInternalService.getSuggestedProceduresV2(
                hp.specialtyId!!,
                type
            )
        } returns emptyList<ProcedureResponseV2>().success()

        authenticatedAs(idToken, staffTest) {
            get(
                "/ehr/v2/suggested/procedures/$type"
            ) { response ->
                assertThat(response).isOKWithData(emptyList<ProcedureResponse>())
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { appointmentExecutedInternalService.getSuggestedProceduresV2(any(), any()) }
    }
}
