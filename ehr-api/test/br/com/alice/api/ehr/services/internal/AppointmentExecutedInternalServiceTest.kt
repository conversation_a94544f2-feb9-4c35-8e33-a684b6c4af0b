package br.com.alice.api.ehr.services.internal

import br.com.alice.api.ehr.controllers.SuggestedProcedureType
import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ProcedureExecutedStatus
import br.com.alice.data.layer.models.TypeOfService
import br.com.alice.exec.indicator.client.HealthSpecialistProcedureService
import br.com.alice.exec.indicator.client.TussCodeWithAliceCodeRequest
import br.com.alice.exec.indicator.client.specialistResources.SpecialistResourceService
import br.com.alice.exec.indicator.models.HealthSpecialistProcedure
import br.com.alice.exec.indicator.models.HealthSpecialistProcedureServiceType
import br.com.alice.exec.indicator.models.PaginatedList
import br.com.alice.exec.indicator.models.SpecialistResource
import br.com.alice.exec.indicator.models.SpecialistResourceFilters
import br.com.alice.exec.indicator.models.SpecialistResourceSpecialty
import br.com.alice.exec.indicator.models.SpecialistResourceTuss
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class AppointmentExecutedInternalServiceTest {
    private val procedureExecutedService: AppointmentProcedureExecutedService = mockk()
    private val specialistResourceService: SpecialistResourceService = mockk()
    private val healthSpecialistProcedureService: HealthSpecialistProcedureService = mockk()


    private val service = AppointmentExecutedInternalService(
        procedureExecutedService,
        specialistResourceService,
        healthSpecialistProcedureService,
    )

    private val appointmentId = RangeUUID.generate()
    private val procedureExecuted = TestModelFactory.buildAppointmentProcedureExecuted(
        id = RangeUUID.generate(),
        tussCode = "123",
        tussProcedureAliceCode = "456",
        isPriced = true,
        appointmentId = appointmentId,
        healthSpecialistResourceBundleCode = "456",
        status = ProcedureExecutedStatus.DRAFT

    )
    val procedure = ProcedureResponse(
        id = procedureExecuted.id,
        name = "Consulta Alice",
        tussCode = procedureExecuted.tussCode,
        aliceCode = procedureExecuted.tussProcedureAliceCode,
        groupType = ProcedureGroupType.CONSULTA,
        isPriced = procedureExecuted.isPriced
    )

    val procedureV2 = ProcedureResponseV2(
        id = procedureExecuted.id,
        name = "Consulta Alice",
        tussCode = procedureExecuted.tussCode,
        aliceCode = procedureExecuted.tussProcedureAliceCode,
        isPriced = procedureExecuted.isPriced,
        typeOfService = TypeOfService.CONSULTATION
    )

    private val healthSpecialistProcedures = listOf(
        HealthSpecialistProcedure(
            tussCode = procedureExecuted.tussCode,
            aliceCode = procedureExecuted.healthSpecialistResourceBundleCode,
            description = "Consulta Alice",
            serviceType = HealthSpecialistProcedureServiceType.CONSULTATION,
            hasPrice = true,
        )
    )
    private val specialtyId = RangeUUID.generate()

    @Test
    fun `#getByAppointmentId should return a list of ProcedureExecutedResponse with test code description`() =
        runBlocking {
            val expected = listOf(
                procedure.copy(
                    id = procedure.id,
                    name = "Consulta Alice",
                    tussCode = procedure.tussCode,
                    aliceCode = procedure.aliceCode,
                    groupType = ProcedureGroupType.CONSULTA,
                    isPriced = procedure.isPriced
                )
            )

            coEvery { procedureExecutedService.getByAppointmentId(appointmentId) } returns listOf(procedureExecuted).success()
            coEvery {
                healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                    listOf(
                        TussCodeWithAliceCodeRequest(
                            tussCode = procedureExecuted.tussCode,
                            aliceCode = procedureExecuted.healthSpecialistResourceBundleCode
                        )
                    )
                )
            } returns healthSpecialistProcedures.success()

            val result = service.getByAppointmentId(appointmentId)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { procedureExecutedService.getByAppointmentId(any()) }
            coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
        }

    @Test
    fun `#getByAppointmentId should return a list of ProcedureExecutedResponse with tuss description`() =
        runBlocking {
            val procedureExecuted = procedureExecuted.copy(
                tussProcedureAliceCode = null, healthSpecialistResourceBundleCode = null
            )
            val expected = listOf(
                procedure.copy(
                    id = procedure.id,
                    name = "Consulta Alice",
                    tussCode = procedure.tussCode,
                    aliceCode = null,
                    groupType = ProcedureGroupType.CONSULTA,
                    isPriced = procedure.isPriced
                )
            )

            coEvery { procedureExecutedService.getByAppointmentId(appointmentId) } returns listOf(procedureExecuted).success()
            coEvery {
                healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                    listOf(
                        TussCodeWithAliceCodeRequest(
                            tussCode = procedureExecuted.tussCode,
                            aliceCode = procedureExecuted.healthSpecialistResourceBundleCode
                        )
                    )
                )
            } returns healthSpecialistProcedures.success()

            val result = service.getByAppointmentId(appointmentId)
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { procedureExecutedService.getByAppointmentId(any()) }
            coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
        }

    @Test
    fun `#getDefaultProcedure get default procedure with test code description`() =
        runBlocking {
            val defaultProcedure = SpecialistResource(
                id = UUID.randomUUID(),
                primaryTuss = SpecialistResourceTuss("101012", "Teste"),
                secondaryResources = emptyList(),
                code = "ALICE-123",
                description = "Teste de recurso",
                medicalSpecialties = listOf(
                    SpecialistResourceSpecialty(
                        medicalSpecialtyId = specialtyId,
                        description = "Cardiologia",
                        pricingStatus = PricingStatus.PRICED,
                        recommendationLevel = AppointmentRecommendationLevel.DEFAULT
                    )
                ),
                serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION,
                updatedAt = LocalDateTime.now(),
                createdAt = LocalDateTime.now(),
                executionAmount = 1,
                executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL,
                status = Status.ACTIVE
            )

            val expectedValue = ProcedureResponse(
                id = defaultProcedure.id,
                name = defaultProcedure.description,
                tussCode = defaultProcedure.primaryTuss.code,
                aliceCode = defaultProcedure.code,
                groupType = ProcedureGroupType.CONSULTA,
                isPriced = true
            )

            coEvery {
                specialistResourceService.list(
                    filter = SpecialistResourceFilters(
                        medicalSpecialtyIds = listOf(specialtyId),
                        status = Status.ACTIVE,
                        recommendationLevel = listOf(AppointmentRecommendationLevel.DEFAULT),
                    ),
                    range = 0..1
                )
            } returns PaginatedList(
                items = listOf(defaultProcedure),
                totalItems = 1,
                range = 0..1
            ).success()


            val result = service.getDefaultProcedure(specialtyId)
            assertThat(result).isSuccessWithData(expectedValue)
        }

    @Test
    fun `addProcedureResponse should add procedure and return procedure response`() = runBlocking {
        val expected = listOf(procedure.copy(id = procedureExecuted.id))
        coEvery {
            procedureExecutedService.addDefaultProcedureByAppointment(match {
                it.tussCode == procedureExecuted.tussCode &&
                        it.tussProcedureAliceCode == procedureExecuted.tussProcedureAliceCode &&
                        it.isPriced == procedureExecuted.isPriced &&
                        it.appointmentId == appointmentId &&
                        it.status == ProcedureExecutedStatus.DRAFT
            })
        } returns procedureExecuted.success()

        val result = service.addProcedureDefault(appointmentId, procedure)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { procedureExecutedService.addDefaultProcedureByAppointment(any()) }
    }

    @Test
    fun `add should add procedure and return response`() = runBlocking {
        val expected = procedure.copy(id = procedureExecuted.id)

        coEvery {
            procedureExecutedService.add(procedureExecuted)
        } returns procedureExecuted.success()
        coEvery {
            healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(
                listOf(
                    TussCodeWithAliceCodeRequest(
                        tussCode = procedureExecuted.tussCode,
                        aliceCode = procedureExecuted.healthSpecialistResourceBundleCode
                    )
                )
            )
        } returns healthSpecialistProcedures.success()

        val result = service.add(procedureExecuted)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { procedureExecutedService.add(any()) }
        coVerifyOnce { healthSpecialistProcedureService.findByTussCodesOrHealthSpecialistResourceBundleCodes(any()) }
    }

    @Test
    fun `deleteDraftProcedure should delete draft procedure`() = runBlocking {
        coEvery { procedureExecutedService.deleteDraftProcedure(procedureExecuted.id) } returns true.success()

        val result = service.deleteDraftProcedure(procedureExecuted.id)
        assertThat(result).isSuccess()

        coVerifyOnce { procedureExecutedService.deleteDraftProcedure(any()) }
    }

    @Test
    fun `addV2 should add procedure and return response`() = runBlocking {
        val expected = procedureV2.copy(id = procedureExecuted.id)
        val procedureExecuted = procedureExecuted.copy(
            typeOfService = TypeOfService.CONSULTATION,
            name = "Consulta Alice",
        )
        coEvery {
            procedureExecutedService.add(procedureExecuted)
        } returns procedureExecuted.success()

        val result = service.addV2(procedureExecuted)
        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { procedureExecutedService.add(any()) }
    }

    @Test
    fun `#getSuggestedProcedures should return procedure response`() =
        runBlocking {
            val suggestedProcedure = SpecialistResource(
                id = UUID.randomUUID(),
                primaryTuss = SpecialistResourceTuss("101012", "Teste"),
                secondaryResources = emptyList(),
                code = "ALICE-123",
                description = "Teste de recurso",
                medicalSpecialties = listOf(
                    SpecialistResourceSpecialty(
                        medicalSpecialtyId = specialtyId,
                        description = "Cardiologia",
                        pricingStatus = PricingStatus.PRICED,
                        recommendationLevel = AppointmentRecommendationLevel.RECOMMENDED
                    )
                ),
                serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION,
                updatedAt = LocalDateTime.now(),
                createdAt = LocalDateTime.now(),
                executionAmount = 1,
                executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL,
                status = Status.ACTIVE
            )

            val expected = listOf(
                ProcedureResponse(
                    id = suggestedProcedure.id,
                    name = suggestedProcedure.description,
                    tussCode = suggestedProcedure.primaryTuss.code,
                    aliceCode = suggestedProcedure.code,
                    groupType = ProcedureGroupType.CONSULTA,
                    isPriced = true
                )
            )

            coEvery {
                specialistResourceService.list(
                    filter = SpecialistResourceFilters(
                        medicalSpecialtyIds = listOf(specialtyId),
                        status = Status.ACTIVE,
                        recommendationLevel = listOf(AppointmentRecommendationLevel.RECOMMENDED),
                    ),
                    range = 0..20
                )
            } returns PaginatedList(
                items = listOf(suggestedProcedure),
                totalItems = 1,
                range = 0..20
            ).success()

            val result = service.getSuggestedProcedures(specialtyId, SuggestedProcedureType.PROCEDURE_EXECUTED)
            assertThat(result).isSuccessWithData(expected)
        }

    @Test
    fun `#getSuggestedProceduresV2 should return procedure response`() =
        runBlocking {
            val suggestedProcedure = SpecialistResource(
                id = UUID.randomUUID(),
                primaryTuss = SpecialistResourceTuss("101012", "Teste"),
                secondaryResources = emptyList(),
                code = "ALICE-123",
                description = "Teste de recurso",
                medicalSpecialties = listOf(
                    SpecialistResourceSpecialty(
                        medicalSpecialtyId = specialtyId,
                        description = "Cardiologia",
                        pricingStatus = PricingStatus.PRICED,
                        recommendationLevel = AppointmentRecommendationLevel.RECOMMENDED
                    )
                ),
                serviceType = HealthSpecialistResourceBundleServiceType.PROCEDURE,
                updatedAt = LocalDateTime.now(),
                createdAt = LocalDateTime.now(),
                executionAmount = 1,
                executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL,
                status = Status.ACTIVE
            )

            val expected = listOf(
                ProcedureResponseV2(
                    id = suggestedProcedure.id,
                    name = suggestedProcedure.description,
                    tussCode = suggestedProcedure.primaryTuss.code,
                    aliceCode = suggestedProcedure.code,
                    isPriced = true,
                    typeOfService = TypeOfService.SURGICAL_PROCEDURE
                )
            )

            coEvery {
                specialistResourceService.list(
                    filter = SpecialistResourceFilters(
                        medicalSpecialtyIds = listOf(specialtyId),
                        status = Status.ACTIVE,
                        recommendationLevel = listOf(AppointmentRecommendationLevel.RECOMMENDED),
                    ),
                    range = 0..20
                )
            } returns PaginatedList(
                items = listOf(suggestedProcedure),
                totalItems = 1,
                range = 0..20
            ).success()

            val result = service.getSuggestedProceduresV2(specialtyId, SuggestedProcedureType.PROCEDURE_EXECUTED)
            assertThat(result).isSuccessWithData(expected)
        }
}
