package br.com.alice.common.sqs.builders

import br.com.alice.common.aws.withCredentials
import br.com.alice.common.sqs.interfaces.QueueConsumeConfig
import com.amazonaws.regions.Regions.US_EAST_1
import com.amazonaws.services.sqs.AmazonSQS
import com.amazonaws.services.sqs.AmazonSQSClientBuilder
import com.amazonaws.services.sqs.model.ReceiveMessageRequest

object ConsumerBuilder {
    const val maxNumberOfMessages = 10 //limited by SQS

    fun buildConsumer(): AmazonSQS {
        return AmazonSQSClientBuilder.standard()
            .withCredentials()
            .withRegion(US_EAST_1)
            .build()
    }

    fun buildReceiveMessageRequest(url: String, queueConsumeConfig: QueueConsumeConfig): ReceiveMessageRequest {
        return ReceiveMessageRequest()
            .withQueueUrl(url)
            .withMaxNumberOfMessages(maxNumberOfMessages)
            .withWaitTimeSeconds(queueConsumeConfig.waitTimeSeconds)
            .withVisibilityTimeout(queueConsumeConfig.visibilityTimeout)
    }
}
