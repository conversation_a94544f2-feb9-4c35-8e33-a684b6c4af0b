package br.com.alice.person.services.internal

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.GracePeriodType
import br.com.alice.data.layer.models.GracePeriodTypeReason
import br.com.alice.data.layer.models.MemberBeneficiary
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.person.br.com.alice.person.exception.BeneficiaryDataIsOlderThanTheOneOnMember
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class MemberBeneficiaryServiceTest : MockedTestHelper() {
    private val memberService: MemberService = mockk()
    private val service = MemberBeneficiaryService(memberService)

    val parentMember = TestModelFactory.buildMember(
        id = RangeUUID.generate(),
        beneficiaryId = RangeUUID.generate()
    )
    private val beneficiary = TestModelFactory.buildBeneficiary(
        companyId = RangeUUID.generate(),
        companySubContractId = RangeUUID.generate(),
        parentBeneficiary = parentMember.beneficiaryId,
        type = BeneficiaryType.DEPENDENT,
        contractType = BeneficiaryContractType.PJ,
        parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        activatedAt = LocalDateTime.now(),
        canceledAt = LocalDateTime.now(),
        hiredAt = LocalDateTime.now(),
        parentBeneficiaryRelatedAt = LocalDateTime.now(),
        canceledReason = BeneficiaryCancelationReason.ANOTHER_COMPANY,
        canceledDescription = "canceledDescription2",
        cnpj = "cnpj2",
        hasContributed = false,
        version = 0,
        gracePeriodType = GracePeriodType.TOTAL_GRACE_PERIOD,
        gracePeriodTypeReason = GracePeriodTypeReason.PORTABILITY,
        gracePeriodBaseDate = LocalDate.now().plusDays(1),
        parentPerson = PersonId(),
        memberStatus = MemberStatus.ACTIVE
    )
    private val currentMember = TestModelFactory.buildMember(
        id = beneficiary.memberId,
        beneficiaryId = beneficiary.id,
        companyId = beneficiary.companyId,
        companySubContractId = beneficiary.companySubContractId,
        beneficiary = MemberBeneficiary(
            parentBeneficiary = beneficiary.parentBeneficiary,
            type = beneficiary.type,
            contractType = beneficiary.contractType,
            parentBeneficiaryRelationType = beneficiary.parentBeneficiaryRelationType,
            activatedAt = beneficiary.activatedAt,
            canceledAt = beneficiary.canceledAt,
            hiredAt = beneficiary.hiredAt,
            parentBeneficiaryRelatedAt = beneficiary.parentBeneficiaryRelatedAt,
            canceledReason = beneficiary.canceledReason,
            canceledDescription = beneficiary.canceledDescription,
            cnpj = beneficiary.cnpj,
            hasContributed = beneficiary.hasContributed,
            version = beneficiary.version,
            gracePeriodType = beneficiary.gracePeriodType,
            gracePeriodTypeReason = beneficiary.gracePeriodTypeReason,
            gracePeriodBaseDate = beneficiary.gracePeriodBaseDate,
        )
    )

    @Test
    fun `#syncBeneficiary should update member if beneficiary is not null and the version is greater`() = runBlocking {
        val toUpdate = beneficiary.copy(version = beneficiary.version + 1)
        val expectedMemberToUpdate = currentMember.copy(
            beneficiaryId = toUpdate.id,
            companyId = toUpdate.companyId,
            companySubContractId = toUpdate.companySubContractId,
            parentPerson = toUpdate.parentPerson,
            parentMember = parentMember.id,
            beneficiary = MemberBeneficiary(
                parentBeneficiary = parentMember.beneficiaryId,
                type = toUpdate.type,
                contractType = toUpdate.contractType,
                parentBeneficiaryRelationType = toUpdate.parentBeneficiaryRelationType,
                activatedAt = toUpdate.activatedAt,
                canceledAt = toUpdate.canceledAt,
                hiredAt = toUpdate.hiredAt,
                parentBeneficiaryRelatedAt = toUpdate.parentBeneficiaryRelatedAt,
                canceledReason = toUpdate.canceledReason,
                canceledDescription = toUpdate.canceledDescription,
                cnpj = toUpdate.cnpj,
                hasContributed = toUpdate.hasContributed,
                version = toUpdate.version,
                gracePeriodType = toUpdate.gracePeriodType,
                gracePeriodTypeReason = toUpdate.gracePeriodTypeReason,
                gracePeriodBaseDate = toUpdate.gracePeriodBaseDate,
            )
        )

        coEvery { memberService.get(currentMember.id) } returns
                currentMember.success()
        coEvery { memberService.getByBeneficiaryId(parentMember.beneficiaryId!!) } returns
                parentMember.success()
        coEvery { memberService.update(expectedMemberToUpdate) } returns
                expectedMemberToUpdate.success()

        assertThat(service.syncBeneficiary(toUpdate))
            .isSuccessWithData(expectedMemberToUpdate)

        coVerify {
            memberService.get(currentMember.id)
            memberService.getByBeneficiaryId(parentMember.beneficiaryId!!)
            memberService.update(expectedMemberToUpdate)
        }
    }

    @Test
    fun `#syncBeneficiary should update member if beneficiary is null`() = runBlocking {
        val memberWithNullBeneficiary = currentMember.copy(beneficiary = null)
        val beneficiaryToSync = beneficiary.copy(
            version = beneficiary.version + 1,
            parentBeneficiary = null
        )
        val expectedUpdatedMember = memberWithNullBeneficiary.copy(
            beneficiaryId = beneficiaryToSync.id,
            companyId = beneficiaryToSync.companyId,
            companySubContractId = beneficiaryToSync.companySubContractId,
            parentPerson = beneficiaryToSync.parentPerson,
            parentMember = null,
            beneficiary = MemberBeneficiary(
                parentBeneficiary = beneficiaryToSync.parentBeneficiary,
                type = beneficiaryToSync.type,
                contractType = beneficiaryToSync.contractType,
                parentBeneficiaryRelationType = beneficiaryToSync.parentBeneficiaryRelationType,
                activatedAt = beneficiaryToSync.activatedAt,
                canceledAt = beneficiaryToSync.canceledAt,
                hiredAt = beneficiaryToSync.hiredAt,
                parentBeneficiaryRelatedAt = beneficiaryToSync.parentBeneficiaryRelatedAt,
                canceledReason = beneficiaryToSync.canceledReason,
                canceledDescription = beneficiaryToSync.canceledDescription,
                cnpj = beneficiaryToSync.cnpj,
                hasContributed = beneficiaryToSync.hasContributed,
                version = beneficiaryToSync.version,
                gracePeriodType = beneficiaryToSync.gracePeriodType,
                gracePeriodTypeReason = beneficiaryToSync.gracePeriodTypeReason,
                gracePeriodBaseDate = beneficiaryToSync.gracePeriodBaseDate,
            )
        )

        coEvery { memberService.get(currentMember.id) } returns
                memberWithNullBeneficiary.success()
        coEvery { memberService.update(expectedUpdatedMember) } returns
                expectedUpdatedMember.success()

        assertThat(service.syncBeneficiary(beneficiaryToSync))
            .isSuccessWithData(expectedUpdatedMember)

        coVerify {
            memberService.get(currentMember.id)
            memberService.update(expectedUpdatedMember)
        }
    }

    @Test
    fun `#syncBeneficiary shouldn't update member if version isn't greater`() = runBlocking {
        coEvery { memberService.get(currentMember.id) } returns
                currentMember.success()

        assertThat(service.syncBeneficiary(beneficiary))
            .isFailureOfType(BeneficiaryDataIsOlderThanTheOneOnMember::class)

        coVerify { memberService.get(any()) }
        coVerifyNone { memberService.update(any()) }
    }

    @Test
    fun `#syncBeneficiary shouldn't update member if member not found`() = runBlocking {
        coEvery { memberService.get(currentMember.id) } returns
                NotFoundException().failure()

        assertThat(service.syncBeneficiary(beneficiary))
            .isFailureOfType(NotFoundException::class)

        coVerify { memberService.get(currentMember.id) }
        coVerifyNone { memberService.update(any()) }
    }

    @Test
    fun `#syncBeneficiary should handle equal version scenario`() = runBlocking {
        val beneficiaryWithEqualVersion = beneficiary.copy(version = currentMember.beneficiary!!.version)

        coEvery { memberService.get(currentMember.id) } returns
                currentMember.success()

        assertThat(service.syncBeneficiary(beneficiaryWithEqualVersion))
            .isFailureOfType(BeneficiaryDataIsOlderThanTheOneOnMember::class)

        coVerify { memberService.get(currentMember.id) }
        coVerifyNone { memberService.update(any()) }
        coVerifyNone { memberService.getByBeneficiaryId(any()) }
    }

    @Test
    fun `#syncBeneficiary should fail when parent member not found`() = runBlocking {
        val toUpdate = beneficiary.copy(version = beneficiary.version + 1)

        coEvery { memberService.get(currentMember.id) } returns
                currentMember.success()
        coEvery { memberService.getByBeneficiaryId(parentMember.beneficiaryId!!) } returns
                NotFoundException().failure()

        assertThat(service.syncBeneficiary(toUpdate))
            .isFailureOfType(NotFoundException::class)

        coVerify {
            memberService.get(currentMember.id)
            memberService.getByBeneficiaryId(parentMember.beneficiaryId!!)
        }
        coVerifyNone { memberService.update(any()) }
    }

    @Test
    fun `#syncBeneficiary should fail when member update fails`() = runBlocking {
        val beneficiaryToSync = beneficiary.copy(
            version = beneficiary.version + 1,
            parentBeneficiary = null
        )
        val memberWithNullBeneficiary = currentMember.copy(beneficiary = null)
        val expectedUpdatedMember = memberWithNullBeneficiary.copy(
            beneficiaryId = beneficiaryToSync.id,
            companyId = beneficiaryToSync.companyId,
            companySubContractId = beneficiaryToSync.companySubContractId,
            parentPerson = beneficiaryToSync.parentPerson,
            parentMember = null,
            beneficiary = MemberBeneficiary(
                parentBeneficiary = beneficiaryToSync.parentBeneficiary,
                type = beneficiaryToSync.type,
                contractType = beneficiaryToSync.contractType,
                parentBeneficiaryRelationType = beneficiaryToSync.parentBeneficiaryRelationType,
                activatedAt = beneficiaryToSync.activatedAt,
                canceledAt = beneficiaryToSync.canceledAt,
                hiredAt = beneficiaryToSync.hiredAt,
                parentBeneficiaryRelatedAt = beneficiaryToSync.parentBeneficiaryRelatedAt,
                canceledReason = beneficiaryToSync.canceledReason,
                canceledDescription = beneficiaryToSync.canceledDescription,
                cnpj = beneficiaryToSync.cnpj,
                hasContributed = beneficiaryToSync.hasContributed,
                version = beneficiaryToSync.version,
                gracePeriodType = beneficiaryToSync.gracePeriodType,
                gracePeriodTypeReason = beneficiaryToSync.gracePeriodTypeReason,
                gracePeriodBaseDate = beneficiaryToSync.gracePeriodBaseDate,
            )
        )
        val updateException = RuntimeException("Update failed")

        coEvery { memberService.get(currentMember.id) } returns
                memberWithNullBeneficiary.success()
        coEvery { memberService.update(expectedUpdatedMember) } returns
                updateException.failure()

        assertThat(service.syncBeneficiary(beneficiaryToSync))
            .isFailureOfType(RuntimeException::class)

        coVerify {
            memberService.get(currentMember.id)
            memberService.update(expectedUpdatedMember)
        }
    }

    @Test
    fun `#syncBeneficiary should handle EMPLOYEE type beneficiary without parent`() = runBlocking {
        val employeeBeneficiary = beneficiary.copy(
            version = beneficiary.version + 1,
            type = BeneficiaryType.EMPLOYEE,
            parentBeneficiary = null,
            parentBeneficiaryRelationType = null
        )
        val memberWithNullBeneficiary = currentMember.copy(beneficiary = null)
        val expectedUpdatedMember = memberWithNullBeneficiary.copy(
            beneficiaryId = employeeBeneficiary.id,
            companyId = employeeBeneficiary.companyId,
            companySubContractId = employeeBeneficiary.companySubContractId,
            parentPerson = employeeBeneficiary.parentPerson,
            parentMember = null,
            beneficiary = MemberBeneficiary(
                parentBeneficiary = employeeBeneficiary.parentBeneficiary,
                type = employeeBeneficiary.type,
                contractType = employeeBeneficiary.contractType,
                parentBeneficiaryRelationType = employeeBeneficiary.parentBeneficiaryRelationType,
                activatedAt = employeeBeneficiary.activatedAt,
                canceledAt = employeeBeneficiary.canceledAt,
                hiredAt = employeeBeneficiary.hiredAt,
                parentBeneficiaryRelatedAt = employeeBeneficiary.parentBeneficiaryRelatedAt,
                canceledReason = employeeBeneficiary.canceledReason,
                canceledDescription = employeeBeneficiary.canceledDescription,
                cnpj = employeeBeneficiary.cnpj,
                hasContributed = employeeBeneficiary.hasContributed,
                version = employeeBeneficiary.version,
                gracePeriodType = employeeBeneficiary.gracePeriodType,
                gracePeriodTypeReason = employeeBeneficiary.gracePeriodTypeReason,
                gracePeriodBaseDate = employeeBeneficiary.gracePeriodBaseDate,
            )
        )

        coEvery { memberService.get(currentMember.id) } returns
                memberWithNullBeneficiary.success()
        coEvery { memberService.update(expectedUpdatedMember) } returns
                expectedUpdatedMember.success()

        assertThat(service.syncBeneficiary(employeeBeneficiary))
            .isSuccessWithData(expectedUpdatedMember)

        coVerify {
            memberService.get(currentMember.id)
            memberService.update(expectedUpdatedMember)
        }
        coVerifyNone { memberService.getByBeneficiaryId(any()) }
    }

    @Test
    fun `#syncBeneficiary should handle member with existing beneficiary data and higher version`() = runBlocking {
        val memberWithHigherVersion = currentMember.copy(
            beneficiary = currentMember.beneficiary?.copy(version = beneficiary.version + 2)
        )
        val beneficiaryWithLowerVersion = beneficiary.copy(version = beneficiary.version + 1)

        coEvery { memberService.get(currentMember.id) } returns
                memberWithHigherVersion.success()

        assertThat(service.syncBeneficiary(beneficiaryWithLowerVersion))
            .isFailureOfType(BeneficiaryDataIsOlderThanTheOneOnMember::class)

        coVerify { memberService.get(currentMember.id) }
        coVerifyNone { memberService.update(any()) }
        coVerifyNone { memberService.getByBeneficiaryId(any()) }
    }

    @Test
    fun `#syncBeneficiary should handle parent member update failure`() = runBlocking {
        val toUpdate = beneficiary.copy(version = beneficiary.version + 1)
        val expectedMemberToUpdate = currentMember.copy(
            beneficiaryId = toUpdate.id,
            companyId = toUpdate.companyId,
            companySubContractId = toUpdate.companySubContractId,
            parentPerson = toUpdate.parentPerson,
            parentMember = parentMember.id,
            beneficiary = MemberBeneficiary(
                parentBeneficiary = parentMember.beneficiaryId,
                type = toUpdate.type,
                contractType = toUpdate.contractType,
                parentBeneficiaryRelationType = toUpdate.parentBeneficiaryRelationType,
                activatedAt = toUpdate.activatedAt,
                canceledAt = toUpdate.canceledAt,
                hiredAt = toUpdate.hiredAt,
                parentBeneficiaryRelatedAt = toUpdate.parentBeneficiaryRelatedAt,
                canceledReason = toUpdate.canceledReason,
                canceledDescription = toUpdate.canceledDescription,
                cnpj = toUpdate.cnpj,
                hasContributed = toUpdate.hasContributed,
                version = toUpdate.version,
                gracePeriodType = toUpdate.gracePeriodType,
                gracePeriodTypeReason = toUpdate.gracePeriodTypeReason,
                gracePeriodBaseDate = toUpdate.gracePeriodBaseDate,
            )
        )
        val updateException = RuntimeException("Parent member update failed")

        coEvery { memberService.get(currentMember.id) } returns
                currentMember.success()
        coEvery { memberService.getByBeneficiaryId(parentMember.beneficiaryId!!) } returns
                parentMember.success()
        coEvery { memberService.update(expectedMemberToUpdate) } returns
                updateException.failure()

        assertThat(service.syncBeneficiary(toUpdate))
            .isFailureOfType(RuntimeException::class)

        coVerify {
            memberService.get(currentMember.id)
            memberService.getByBeneficiaryId(parentMember.beneficiaryId!!)
            memberService.update(expectedMemberToUpdate)
        }
    }

    @Test
    fun `#syncBeneficiary should handle null parent beneficiary with IllegalArgumentException`() = runBlocking {
        val beneficiaryWithNullParent = beneficiary.copy(
            version = beneficiary.version + 1,
            parentBeneficiary = null
        )
        val memberWithNullBeneficiary = currentMember.copy(beneficiary = null)

        // Mock the member service to return a member with null beneficiary
        coEvery { memberService.get(currentMember.id) } returns
                memberWithNullBeneficiary.success()

        // Create expected member after merge
        val expectedUpdatedMember = memberWithNullBeneficiary.copy(
            beneficiaryId = beneficiaryWithNullParent.id,
            companyId = beneficiaryWithNullParent.companyId,
            companySubContractId = beneficiaryWithNullParent.companySubContractId,
            parentPerson = beneficiaryWithNullParent.parentPerson,
            parentMember = null,
            beneficiary = MemberBeneficiary(
                parentBeneficiary = beneficiaryWithNullParent.parentBeneficiary,
                type = beneficiaryWithNullParent.type,
                contractType = beneficiaryWithNullParent.contractType,
                parentBeneficiaryRelationType = beneficiaryWithNullParent.parentBeneficiaryRelationType,
                activatedAt = beneficiaryWithNullParent.activatedAt,
                canceledAt = beneficiaryWithNullParent.canceledAt,
                hiredAt = beneficiaryWithNullParent.hiredAt,
                parentBeneficiaryRelatedAt = beneficiaryWithNullParent.parentBeneficiaryRelatedAt,
                canceledReason = beneficiaryWithNullParent.canceledReason,
                canceledDescription = beneficiaryWithNullParent.canceledDescription,
                cnpj = beneficiaryWithNullParent.cnpj,
                hasContributed = beneficiaryWithNullParent.hasContributed,
                version = beneficiaryWithNullParent.version,
                gracePeriodType = beneficiaryWithNullParent.gracePeriodType,
                gracePeriodTypeReason = beneficiaryWithNullParent.gracePeriodTypeReason,
                gracePeriodBaseDate = beneficiaryWithNullParent.gracePeriodBaseDate,
            )
        )

        coEvery { memberService.update(expectedUpdatedMember) } returns
                expectedUpdatedMember.success()

        assertThat(service.syncBeneficiary(beneficiaryWithNullParent))
            .isSuccessWithData(expectedUpdatedMember)

        coVerify {
            memberService.get(currentMember.id)
            memberService.update(expectedUpdatedMember)
        }
        coVerifyNone { memberService.getByBeneficiaryId(any()) }
    }
}
