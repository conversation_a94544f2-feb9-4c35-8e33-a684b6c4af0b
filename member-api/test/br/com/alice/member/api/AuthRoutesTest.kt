package br.com.alice.member.api

import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonLogin
import br.com.alice.member.api.controllers.AuthController
import br.com.alice.member.api.models.NewAccessCodeRequest
import br.com.alice.member.api.models.SignInRequest
import br.com.alice.member.api.models.SignInResponse
import br.com.alice.member.api.services.AuthService
import br.com.alice.membership.client.InvalidCredentialsException
import br.com.alice.membership.client.InvalidCredentialsForGASException
import br.com.alice.membership.client.LoginExpiredException
import br.com.alice.membership.client.LoginNotFoundException
import br.com.alice.membership.client.LoginNotFoundForGASException
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class AuthRoutesTest : RoutesTestHelper() {

    private val authService: AuthService = mockk()
    private val authController = AuthController(authService)

    private val personLogin = PersonLogin(
        personId = PersonId(),
        nationalId = "nationalId",
        accessCode = "otp",
        saltAccessCode = "salt",
        expirationDate = LocalDateTime.now().plusDays(1)
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { authController }
    }

    private val newAccessCodeRequest = NewAccessCodeRequest(personLogin.nationalId)

    @Test
    fun `#generateNewAccessCode should return 400 BadRequest when login was not found`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", emptyList<String>()) {
            coEvery { 
                authService.getPersonAndSendNewAccessCodeByNationalId(personLogin.nationalId)
            } returns LoginNotFoundException().failure()

            internalAuthentication {
                post("/auth/send_access_code", body = newAccessCodeRequest) { response ->
                    assertThat(response).isBadRequest()

                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("invalid_login_credentials")
                }
            }
        }
    }

    @Test
    fun `#generateNewAccessCode should return 401 Unauthorized when login was not found and person is on allowed list`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf(personLogin.nationalId)) {
            coEvery { authService.getPersonAndSendNewAccessCodeByNationalId(personLogin.nationalId) } returns
                    LoginNotFoundForGASException("Login not found for national id '${personLogin.nationalId}").failure()

            internalAuthentication {
                post("/auth/send_access_code", body = newAccessCodeRequest) { response ->
                    assertThat(response).isUnauthorized()

                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("invalid_credentials")
                }
            }
        }
    }

    @Test
    fun `#generateNewAccessCode should return 200 when login by nationalId`() {
        val person = TestModelFactory.buildPerson(nationalId = personLogin.nationalId)
        coEvery {
            authService.getPersonAndSendNewAccessCodeByNationalId(personLogin.nationalId)
        } returns person.success()

        internalAuthentication {
            post("/auth/send_access_code", body = newAccessCodeRequest) { response ->
                assertThat(response).isOK()
                coVerify(exactly = 1) { authService.getPersonAndSendNewAccessCodeByNationalId(personLogin.nationalId) }
            }
        }
    }

    @Test
    fun `#signIn should return 200 OK with access token when credentials are ok`() {
        coEvery {
            authService.authenticateByNationalId(
                nationalId = personLogin.nationalId,
                oneTimePassword = personLogin.accessCode
            )
        } returns "SomeAccessToken".success()

        val signInRequest = SignInRequest(
            nationalId = "nationalId",
            otp = "otp"
        )

        internalAuthentication {
            post(to = "/auth/sign_in", body = signInRequest) { response ->
                assertThat(response).isSuccessfulJson()

                val content: SignInResponse = response.bodyAsJson()
                assertThat(content.accessToken).isNotBlank()
            }
        }
    }

    @Test
    fun `#signIn should return 401 Unauthorized when credentials are invalid`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", emptyList<String>()) {
            coEvery {
                authService.authenticateByNationalId(
                    nationalId = personLogin.nationalId,
                    oneTimePassword = personLogin.accessCode
                )
            } returns InvalidCredentialsException("Invalid credentials for national id '${personLogin.nationalId}'").failure()

            val signInRequest = SignInRequest(
                nationalId = "nationalId",
                otp = "otp"
            )

            internalAuthentication {
                post(to = "/auth/sign_in", body = signInRequest) { response ->
                    assertThat(response).isUnauthorized()

                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("invalid_login_credentials")
                }
            }
        }
    }

    @Test
    fun `#signIn should return 401 Unauthorized when credentials are invalid and person is on allowed list`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf(personLogin.nationalId)) {
            coEvery {
                authService.authenticateByNationalId(
                    nationalId = personLogin.nationalId,
                    oneTimePassword = personLogin.accessCode
                )
            } returns InvalidCredentialsForGASException("Invalid credentials for national id '${personLogin.nationalId}'").failure()

            val signInRequest = SignInRequest(
                nationalId = "nationalId",
                otp = "otp"
            )

            internalAuthentication {
                post(to = "/auth/sign_in", body = signInRequest) { response ->
                    assertThat(response).isUnauthorized()

                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("invalid_credentials")
                }
            }
        }
    }

    @Test
    fun `#signIn should return 401 Unauthorized when person was not found`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", emptyList<String>()) {

            coEvery {
                authService.authenticateByNationalId(
                    nationalId = personLogin.nationalId,
                    oneTimePassword = personLogin.accessCode
                )
            } returns LoginNotFoundException("Login not found for national id '${personLogin.nationalId}'").failure()

            val signInRequest = SignInRequest(
                nationalId = "nationalId",
                otp = "otp"
            )

            internalAuthentication {
                post(to = "/auth/sign_in", body = signInRequest) { response ->
                    assertThat(response).isUnauthorized()

                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("invalid_login_credentials")
                }
            }
        }
    }

    @Test
    fun `#signIn should return 401 Unauthorized when person was not found and person is on allow list`() = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf(personLogin.nationalId)) {

            coEvery {
                authService.authenticateByNationalId(
                    nationalId = personLogin.nationalId,
                    oneTimePassword = personLogin.accessCode
                )
            } returns LoginNotFoundForGASException("Login not found for national id '${personLogin.nationalId}'").failure()

            val signInRequest = SignInRequest(
                nationalId = "nationalId",
                otp = "otp"
            )

            internalAuthentication {
                post(to = "/auth/sign_in", body = signInRequest) { response ->
                    assertThat(response).isUnauthorized()

                    val content: ErrorResponse = response.bodyAsJson()
                    assertThat(content.code).isEqualTo("invalid_credentials")
                }
            }
        }
    }

    @Test
    fun `#signIn should return 401 Unauthorized when OTP is expired`() {
        coEvery {
            authService.authenticateByNationalId(
                nationalId = personLogin.nationalId,
                oneTimePassword = personLogin.accessCode
            )
        } returns LoginExpiredException("Login expired for national id '${personLogin.nationalId}'").failure()

        val signInRequest = SignInRequest(
            nationalId = "nationalId",
            otp = "otp"
        )

        internalAuthentication {
            post(to = "/auth/sign_in", body = signInRequest) { response ->
                assertThat(response).isUnauthorized()

                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content.code).isEqualTo("login_expired")
            }
        }
    }

    @Test
    fun `#generateToken should return 200 OK with new access`() {
        val token = RangeUUID.generate().toString()
        val person = TestModelFactory.buildPerson()

        coEvery { authService.generateToken(person.id) } returns "SomeAccessToken".success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/generate_token") { response ->
                assertThat(response).isSuccessfulJson()

                val content: SignInResponse = response.bodyAsJson()
                assertThat(content.accessToken).isNotBlank()
            }
        }
    }

    @Test
    fun `#generateToken should return 401 if not logged in`() {
        internalAuthentication {
            post(to = "/generate_token") { response ->
                assertThat(response).isUnauthorized()
            }
        }
    }

}
